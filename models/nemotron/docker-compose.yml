version: '3.8'

services:
  nemotron-14b:
    image: pytorch/pytorch:2.3.0-cuda12.1-cudnn8-devel
    container_name: nemotron-14b
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - HF_HOME=/cache
    volumes:
      - ./models:/models
      - ./cache:/cache
      - ./scripts:/scripts
    working_dir: /workspace
    command: sleep infinity
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["0"]
              capabilities: [gpu]
    stdin_open: true
    tty: true