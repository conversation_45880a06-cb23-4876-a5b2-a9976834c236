#!/usr/bin/env python3
"""
ABOUTME: Download script for NVIDIA OpenReasoning-Nemotron-14B model
ABOUTME: Uses huggingface_hub to download model files with proper GPU support
"""
import os
from huggingface_hub import snapshot_download

def download_nemotron():
    """Download the NVIDIA OpenReasoning-Nemotron-14B model"""
    print("🔄 Downloading NVIDIA OpenReasoning-Nemotron-14B...")
    
    model_id = "nvidia/OpenReasoning-Nemotron-14B"
    cache_dir = "/cache"
    
    try:
        # Download the model
        model_path = snapshot_download(
            repo_id=model_id,
            cache_dir=cache_dir,
            resume_download=True,
            local_files_only=False
        )
        
        print(f"✅ Model downloaded successfully to: {model_path}")
        return model_path
        
    except Exception as e:
        print(f"❌ Error downloading model: {e}")
        return None

if __name__ == "__main__":
    download_nemotron()