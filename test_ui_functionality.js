// Test script to verify all UI functionality with screenshots
const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

const BASE_URL = 'http://localhost:3000';
const SCREENSHOT_DIR = './test_screenshots';

// Create screenshots directory
if (!fs.existsSync(SCREENSHOT_DIR)) {
    fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
}

async function takeScreenshot(page, name, fullPage = false) {
    const filename = path.join(SCREENSHOT_DIR, `${name}.png`);
    await page.screenshot({ 
        path: filename, 
        fullPage: fullPage,
        type: 'png'
    });
    console.log(`✅ Screenshot taken: ${filename}`);
    return filename;
}

async function waitForContent(page, timeout = 10000) {
    // Wait for loading to finish and content to appear
    await page.waitForTimeout(2000);
    try {
        await page.waitForSelector('[data-testid], .lucide, .text-3xl', { timeout });
    } catch (e) {
        console.log('No specific content selectors found, continuing...');
    }
}

async function testMainDashboard(browser) {
    console.log('\n🏠 Testing Main Dashboard...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        await page.goto(`${BASE_URL}/`, { waitUntil: 'networkidle2' });
        await waitForContent(page);
        await takeScreenshot(page, '01-main-dashboard', true);
        
        // Test sidebar navigation
        await takeScreenshot(page, '02-sidebar-navigation');
        
        console.log('✅ Main Dashboard verified');
        return true;
    } catch (error) {
        console.error('❌ Main Dashboard error:', error.message);
        return false;
    } finally {
        await page.close();
    }
}

async function testGoldAnalysis(browser) {
    console.log('\n🥇 Testing Gold Analysis Page...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        await page.goto(`${BASE_URL}/gold-analysis`, { waitUntil: 'networkidle2' });
        await waitForContent(page, 15000); // Gold analysis might take longer to load
        await takeScreenshot(page, '03-gold-analysis-full', true);
        
        // Test period selector
        await page.click('select[id="period"]');
        await page.select('select[id="period"]', '1 M');
        await page.waitForTimeout(2000);
        await takeScreenshot(page, '04-gold-analysis-period-change');
        
        // Test chart functionality by clicking on a symbol
        try {
            await page.waitForSelector('.font-mono.font-semibold.text-blue-400', { timeout: 5000 });
            const symbolElements = await page.$$('.font-mono.font-semibold.text-blue-400');
            if (symbolElements.length > 0) {
                await symbolElements[0].click();
                await page.waitForTimeout(3000); // Wait for chart modal
                await takeScreenshot(page, '05-gold-analysis-chart-modal', true);
                
                // Close modal
                await page.click('button[aria-label="Close"], .lucide-x');
                await page.waitForTimeout(1000);
            }
        } catch (e) {
            console.log('Chart interaction test skipped - no clickable symbols found');
        }
        
        console.log('✅ Gold Analysis verified');
        return true;
    } catch (error) {
        console.error('❌ Gold Analysis error:', error.message);
        await takeScreenshot(page, '03-gold-analysis-error');
        return false;
    } finally {
        await page.close();
    }
}

async function testCurrencyImpact(browser) {
    console.log('\n💱 Testing Currency Impact Analysis...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        await page.goto(`${BASE_URL}/currency-impact`, { waitUntil: 'networkidle2' });
        await waitForContent(page, 15000);
        await takeScreenshot(page, '06-currency-impact-full', true);
        
        console.log('✅ Currency Impact Analysis verified');
        return true;
    } catch (error) {
        console.error('❌ Currency Impact Analysis error:', error.message);
        await takeScreenshot(page, '06-currency-impact-error');
        return false;
    } finally {
        await page.close();
    }
}

async function testMultiAsset(browser) {
    console.log('\n📊 Testing Multi-Asset View...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        await page.goto(`${BASE_URL}/multi-asset`, { waitUntil: 'networkidle2' });
        await waitForContent(page, 15000);
        await takeScreenshot(page, '07-multi-asset-overview', true);
        
        // Test tab switching
        try {
            const tabs = await page.$$('[role="tab"]');
            if (tabs.length > 1) {
                await tabs[1].click(); // Click second tab
                await page.waitForTimeout(2000);
                await takeScreenshot(page, '08-multi-asset-second-tab');
                
                if (tabs.length > 2) {
                    await tabs[2].click(); // Click third tab
                    await page.waitForTimeout(2000);
                    await takeScreenshot(page, '09-multi-asset-third-tab');
                }
            }
        } catch (e) {
            console.log('Tab switching test skipped');
        }
        
        console.log('✅ Multi-Asset View verified');
        return true;
    } catch (error) {
        console.error('❌ Multi-Asset View error:', error.message);
        await takeScreenshot(page, '07-multi-asset-error');
        return false;
    } finally {
        await page.close();
    }
}

async function testTradingInterface(browser) {
    console.log('\n📈 Testing IBKR Trading Interface...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        await page.goto(`${BASE_URL}/trading`, { waitUntil: 'networkidle2' });
        await waitForContent(page);
        await takeScreenshot(page, '10-trading-auth-screen');
        
        // Test authentication
        await page.type('input[type="password"]', 'dev-trading-key');
        await page.click('button:has-text("Connect to Trading")');
        await page.waitForTimeout(5000);
        await takeScreenshot(page, '11-trading-dashboard', true);
        
        // Test order form if accessible
        try {
            await page.type('input[placeholder*="AAPL"]', 'AAPL');
            await page.type('input[type="number"]', '100');
            await takeScreenshot(page, '12-trading-order-form');
        } catch (e) {
            console.log('Order form test skipped - form may not be accessible');
        }
        
        console.log('✅ Trading Interface verified');
        return true;
    } catch (error) {
        console.error('❌ Trading Interface error:', error.message);
        await takeScreenshot(page, '10-trading-error');
        return false;
    } finally {
        await page.close();
    }
}

async function testNavigationLinks(browser) {
    console.log('\n🧭 Testing Navigation Links...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        await page.goto(`${BASE_URL}/`, { waitUntil: 'networkidle2' });
        await waitForContent(page);
        
        // Test specific navigation items
        const navTests = [
            { name: 'Gold Analysis', path: '/gold-analysis' },
            { name: 'Currency Impact', path: '/currency-impact' },
            { name: 'Multi-Asset', path: '/multi-asset' },
            { name: 'IBKR Trading', path: '/trading' }
        ];
        
        for (const test of navTests) {
            try {
                await page.click(`a[href="${test.path}"]`);
                await page.waitForTimeout(3000);
                await takeScreenshot(page, `13-nav-${test.name.toLowerCase().replace(/\s+/g, '-')}`);
                console.log(`✅ Navigation to ${test.name} works`);
            } catch (e) {
                console.log(`❌ Navigation to ${test.name} failed:`, e.message);
            }
        }
        
        console.log('✅ Navigation Links verified');
        return true;
    } catch (error) {
        console.error('❌ Navigation Links error:', error.message);
        return false;
    } finally {
        await page.close();
    }
}

async function testWebSocketUpdates(browser) {
    console.log('\n🔌 Testing WebSocket Real-time Updates...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        await page.goto(`${BASE_URL}/gold-analysis`, { waitUntil: 'networkidle2' });
        await waitForContent(page);
        
        // Monitor WebSocket connections
        let wsConnected = false;
        page.on('websocket', ws => {
            console.log('✅ WebSocket connection detected');
            wsConnected = true;
            ws.on('framereceived', frame => {
                console.log('📥 WebSocket data received:', frame.payload.slice(0, 100));
            });
        });
        
        await page.waitForTimeout(10000); // Wait to see if WebSocket connects
        await takeScreenshot(page, '14-websocket-test');
        
        console.log(wsConnected ? '✅ WebSocket functionality verified' : '⚠️ WebSocket not detected');
        return true;
    } catch (error) {
        console.error('❌ WebSocket test error:', error.message);
        return false;
    } finally {
        await page.close();
    }
}

async function runAllTests() {
    console.log('🚀 Starting comprehensive UI functionality tests...\n');
    
    const browser = await puppeteer.launch({
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
        ]
    });
    
    const results = {
        mainDashboard: await testMainDashboard(browser),
        goldAnalysis: await testGoldAnalysis(browser),
        currencyImpact: await testCurrencyImpact(browser),
        multiAsset: await testMultiAsset(browser),
        tradingInterface: await testTradingInterface(browser),
        navigationLinks: await testNavigationLinks(browser),
        webSocketUpdates: await testWebSocketUpdates(browser)
    };
    
    await browser.close();
    
    // Summary
    console.log('\n📊 Test Results Summary:');
    console.log('═══════════════════════════════');
    Object.entries(results).forEach(([test, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    console.log(`📸 Screenshots saved to: ${SCREENSHOT_DIR}`);
    
    if (passedTests === totalTests) {
        console.log('🎉 ALL FUNCTIONALITY VERIFIED! UI is fully integrated and working.');
    } else {
        console.log('⚠️ Some issues detected. Check screenshots and logs for details.');
    }
    
    return results;
}

// Run the tests
runAllTests().catch(console.error);