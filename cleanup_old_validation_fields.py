#!/usr/bin/env python3
"""
ABOUTME: Script to remove old lX_valid validation fields from Redis pipeline documents
ABOUTME: Cleans up legacy validation markers no longer used by current pipeline
"""

import asyncio
import json
import logging
import redis.asyncio as redis
from typing import Dict, List, Set

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Old validation fields to remove
OLD_VALIDATION_FIELDS = [
    "l2_valid", "l2_valid_at", "l2_valid_score",
    "l4_valid", "l4_valid_at", "l4_valid_score"
]

async def get_redis_client():
    """Get Redis client connection"""
    return await redis.from_url("redis://localhost:6381", decode_responses=True)

async def get_all_queues(redis_client):
    """Get all queue names in Redis"""
    keys = await redis_client.keys("*queue*")
    # Filter for actual queue names, not internal Redis keys
    queues = [key for key in keys if not key.startswith("_") and "queue" in key]
    return queues

async def clean_document_fields(doc_json: str) -> tuple[str, bool]:
    """
    Clean old validation fields from a document JSON string
    
    Returns:
        (cleaned_json, was_modified)
    """
    try:
        doc = json.loads(doc_json)
        was_modified = False
        
        for field in OLD_VALIDATION_FIELDS:
            if field in doc:
                del doc[field]
                was_modified = True
                logger.debug(f"Removed field: {field}")
        
        return json.dumps(doc), was_modified
    except json.JSONDecodeError:
        logger.error(f"Failed to parse JSON: {doc_json[:100]}...")
        return doc_json, False

async def clean_queue(redis_client, queue_name: str) -> Dict[str, int]:
    """
    Clean old validation fields from all documents in a queue
    
    Returns:
        Dictionary with cleaning statistics
    """
    logger.info(f"Cleaning queue: {queue_name}")
    
    stats = {
        "total_docs": 0,
        "modified_docs": 0,
        "errors": 0
    }
    
    # Get all documents from the queue
    all_docs = await redis_client.lrange(queue_name, 0, -1)
    stats["total_docs"] = len(all_docs)
    
    if not all_docs:
        logger.info(f"Queue {queue_name} is empty")
        return stats
    
    cleaned_docs = []
    
    for doc_json in all_docs:
        try:
            cleaned_json, was_modified = await clean_document_fields(doc_json)
            cleaned_docs.append(cleaned_json)
            
            if was_modified:
                stats["modified_docs"] += 1
                
        except Exception as e:
            logger.error(f"Error cleaning document: {e}")
            stats["errors"] += 1
            # Keep original document on error
            cleaned_docs.append(doc_json)
    
    # Replace the entire queue with cleaned documents
    if stats["modified_docs"] > 0:
        logger.info(f"Updating {queue_name} with {stats['modified_docs']} cleaned documents")
        
        # Use transaction to ensure atomicity
        pipe = redis_client.pipeline()
        pipe.delete(queue_name)  # Clear the queue
        
        # Add all cleaned documents back
        for doc in cleaned_docs:
            pipe.lpush(queue_name, doc)
            
        await pipe.execute()
        
        logger.info(f"✅ Updated queue {queue_name}")
    else:
        logger.info(f"No changes needed for queue {queue_name}")
    
    return stats

async def main():
    """Main cleanup function"""
    logger.info("🧹 Starting cleanup of old validation fields")
    logger.info(f"Fields to remove: {OLD_VALIDATION_FIELDS}")
    
    redis_client = await get_redis_client()
    
    try:
        # Test Redis connection
        await redis_client.ping()
        logger.info("✅ Connected to Redis")
        
        # Get all queues
        queues = await get_all_queues(redis_client)
        logger.info(f"Found {len(queues)} queues to check")
        
        total_stats = {
            "total_queues": len(queues),
            "queues_with_changes": 0,
            "total_docs": 0,
            "total_modified": 0,
            "total_errors": 0
        }
        
        # Process each queue
        for queue_name in queues:
            try:
                queue_stats = await clean_queue(redis_client, queue_name)
                
                total_stats["total_docs"] += queue_stats["total_docs"]
                total_stats["total_modified"] += queue_stats["modified_docs"]
                total_stats["total_errors"] += queue_stats["errors"]
                
                if queue_stats["modified_docs"] > 0:
                    total_stats["queues_with_changes"] += 1
                
                logger.info(f"Queue {queue_name}: {queue_stats['modified_docs']}/{queue_stats['total_docs']} documents modified")
                
            except Exception as e:
                logger.error(f"Error processing queue {queue_name}: {e}")
                total_stats["total_errors"] += 1
        
        # Final summary
        logger.info("\n" + "="*60)
        logger.info("🎉 CLEANUP SUMMARY")
        logger.info("="*60)
        logger.info(f"Queues processed: {total_stats['total_queues']}")
        logger.info(f"Queues with changes: {total_stats['queues_with_changes']}")
        logger.info(f"Total documents: {total_stats['total_docs']}")
        logger.info(f"Documents modified: {total_stats['total_modified']}")
        logger.info(f"Errors encountered: {total_stats['total_errors']}")
        logger.info("="*60)
        
        if total_stats["total_modified"] > 0:
            logger.info(f"✅ Successfully removed old validation fields from {total_stats['total_modified']} documents")
        else:
            logger.info("ℹ️  No old validation fields found - all queues are clean")
            
    except Exception as e:
        logger.error(f"Fatal error during cleanup: {e}")
        raise
    finally:
        await redis_client.close()

if __name__ == "__main__":
    asyncio.run(main())