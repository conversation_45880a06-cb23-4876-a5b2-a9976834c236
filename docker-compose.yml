version: '3.8'

services:
  # === CORE INFRASTRUCTURE ===

  # TimescaleDB (includes PostgreSQL) - Main database
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: omotesamba-timescaledb
    environment:
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - POSTGRES_HOST_AUTH_METHOD=md5
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    ports:
      - "5435:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omotesamba -d omotesamba"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis - Queue management and caching
  redis:
    image: redis:7-alpine
    container_name: omotesamba-redis
    command: redis-server --appendonly yes --maxmemory 8gb --maxmemory-policy noeviction
    volumes:
      - redis_data:/data
    ports:
      - "6381:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3
    restart: unless-stopped

  # ChromaDB - Vector database for embeddings
  chromadb:
    image: chromadb/chroma:latest
    container_name: omotesamba-chromadb
    environment:
      - CHROMA_HOST=0.0.0.0
      - CHROMA_PORT=8000
      - PERSIST_DIRECTORY=/chroma/chroma
    volumes:
      - chroma_data:/chroma/chroma
    ports:
      - "8044:8000"
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    restart: unless-stopped

  # IBKR TWS - Interactive Brokers Trading Workstation for market data
  tws:
    restart: unless-stopped
    devices:
      - /dev/dri:/dev/dri
    privileged: true
    shm_size: "1gb"
    security_opt:
      - seccomp:unconfined
    image: ghcr.io/gnzsnz/tws-rdesktop:latest
    container_name: omotesamba-tws
    environment:
      - PUID=1000
      - PGID=1000
      - PASSWD=${PASSWD:-}
      - TWS_USERID=${TWS_USERID}
      - TWS_PASSWORD=${TWS_PASSWORD}
      - TRADING_MODE=paper
      - TWS_USERID_PAPER=${TWS_USERID_PAPER:-${TWS_USERID}}
      - TWS_PASSWORD_PAPER=${TWS_PASSWORD_PAPER:-${TWS_PASSWORD}}
      - TWS_SETTINGS_PATH=${TWS_SETTINGS_PATH:-}
      - TWS_ACCEPT_INCOMING=accept
      - READ_ONLY_API=no
      - TWOFA_TIMEOUT_ACTION=restart
      - BYPASS_WARNING=${BYPASS_WARNING:-}
      - AUTO_RESTART_TIME=${AUTO_RESTART_TIME:-}
      - AUTO_LOGOFF_TIME=${AUTO_LOGOFF_TIME:-}
      - TWS_COLD_RESTART=${TWS_COLD_RESTART:-}
      - SAVE_TWS_SETTINGS=${SAVE_TWS_SETTINGS:-}
      - RELOGIN_AFTER_TWOFA_TIMEOUT=${RELOGIN_AFTER_TWOFA_TIMEOUT:-no}
      - TWOFA_EXIT_INTERVAL=${TWOFA_EXIT_INTERVAL:-60}
      - TWOFA_DEVICE=${TWOFA_DEVICE:-}
      - EXISTING_SESSION_DETECTED_ACTION=${EXISTING_SESSION_DETECTED_ACTION:-primary}
      - ALLOW_BLIND_TRADING=${ALLOW_BLIND_TRADING:-no}
      - TIME_ZONE=Asia/Tokyo
      - TZ=Asia/Tokyo
      - CUSTOM_CONFIG=${CUSTOM_CONFIG:-NO}
      - JAVA_HEAP_SIZE=${JAVA_HEAP_SIZE:-}
      - SSH_TUNNEL=${SSH_TUNNEL:-}
      - SSH_OPTIONS=${SSH_OPTIONS:-}
      - SSH_ALIVE_INTERVAL=${SSH_ALIVE_INTERVAL:-}
      - SSH_ALIVE_COUNT=${SSH_ALIVE_COUNT:-}
      - SSH_PASSPHRASE=${SSH_PASSPHRASE:-}
      - SSH_REMOTE_PORT=${SSH_REMOTE_PORT:-}
      - SSH_USER_TUNNEL=${SSH_USER_TUNNEL:-}
      - SSH_RESTART=${SSH_RESTART:-}
      - SSH_RDP_PORT=${SSH_RDP_PORT:-}
    volumes:
      - tws_data:/tmp
      - tws_settings:/home/<USER>/Jts
    ports:
      - "7496:7498"  # TWS API port (live)
      - "7497:7499"  # TWS API port (paper)
      - "3370:3389"  # Remote desktop port
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep java | grep -v grep || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # === DATA COLLECTION & EXTRACTION ===

  # Document Scrapers - RSS feeds, APIs, document discovery
  scrapers:
    image: omotesamba-unified-scraper:latest
    container_name: omotesamba-scrapers
    ports:
      - "8001:8001"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - EDINET_KEY=${EDINET_KEY}
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    volumes:
      - ./scraped_pdfs:/app/scraped_pdfs
      - ./downloads:/app/downloads
    restart: unless-stopped

  # Unified PDF Extractor - Queue-based PDF processing with MinerU integration
  pdf-extractor:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.unified-pdf-extractor
    container_name: omotesamba-pdf-extractor
    ports:
      - "8081:8080"  # Metrics API port
    environment:
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MINERU_CONTAINER_NAME=omotesamba-mineru
      - MINERU_SERVICE_URL=http://omotesamba-mineru:30000
      - ENABLE_MINERU=true
      - ASSET_STORAGE_PATH=/app/extracted_assets
      - EDINET_KEY=${EDINET_KEY}
      - MAX_EXTRACTION_RETRIES=3
      - RETRY_DELAY_BASE_SECONDS=60
      - RETRY_DELAY_MAX_SECONDS=3600
      - METRICS_API_PORT=8080
    depends_on:
      redis:
        condition: service_healthy
      # MinerU now handles queue processing directly
      # mineru:
      #   condition: service_healthy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./extracted_assets:/app/extracted_assets
      - ./service-dashboard/backend/settings.json:/app/settings.json:ro
    restart: unless-stopped

  # MineRU Container - GPU-accelerated PDF processing with multi-language support (UPGRADED to 2.1.0!)
  mineru:
    image: mineru-japanese:2.1.0
    container_name: omotesamba-mineru
    # Run the startup script that starts both sglang server and queue consumer
    command: /app/start_mineru_with_queue.sh
    environment:
      - MINERU_MODEL_SOURCE=local
      - MINERU_DEVICE=cuda
      - CUDA_VISIBLE_DEVICES=0
      - FORCE_CUDA=1
      - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./data/mineru:/app/data
      - ./tmp/mineru:/tmp
      - ./extracted_assets:/app/extracted_assets  # For asset storage
      - ./services/pdf_extractor/mineru_queue_consumer.py:/app/mineru_queue_consumer.py:ro  # Mount the queue consumer
      - ./services/pdf_extractor/start_mineru_with_queue.sh:/app/start_mineru_with_queue.sh:ro  # Mount startup script
    deploy:
      resources:
        limits:
          cpus: '8.0'      # Allow up to 8 CPU cores
          memory: 32G      # 32GB RAM limit
        reservations:
          cpus: '4.0'      # Reserve 4 CPU cores minimum
          memory: 16G      # Reserve 16GB RAM minimum
          devices:
            - driver: nvidia
              device_ids: ["0"]
              capabilities: [gpu]
    depends_on:
      redis:
        condition: service_healthy
    # No ports needed - direct queue processing
    restart: unless-stopped
    shm_size: '32gb'
    ipc: host
    ulimits:
      memlock: -1
      stack: 67108864

  # === AI PROCESSING PIPELINE ===

  # SmolLM3-3B vLLM Service - GPU-accelerated inference with tools
  smoll-vllm:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.smoll-vllm
    image: omotesamba-smoll-vllm:latest
    container_name: omotesamba-smoll-vllm
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - VLLM_WORKER_MULTIPROC_METHOD=spawn
      - TOKENIZERS_PARALLELISM=false
    volumes:
      - ./logs/smoll_vllm:/app/logs
      - huggingface_cache:/root/.cache/huggingface
    ports:
      - "8089:8089"  # vLLM server
      - "8093:8093"  # Smart tools server
      - "8094:8094"  # Chat UI web interface
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8093/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 300s

  # Categorizer - Document categorization and routing service
  categorizer:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.categorizer-lite
    image: omotesamba-categorizer:latest
    container_name: omotesamba-categorizer
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - PORT=8002
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - BAIDU_API_KEY=${BAIDU_API_KEY}
      - KIMI_API_KEY=${KIMI_API_KEY}
      - DEFAULT_PROVIDER=${DEFAULT_PROVIDER:-kimi}
      - DEFAULT_MODEL_NAME=${DEFAULT_MODEL:-moonshot-v1-8k}
      - CATEGORIZATION_MODEL=smoll
      - CATEGORIZER_MODEL=${CATEGORIZER_MODEL:-smoll}  # Options: deepseek, gemma, smoll
      - CATEGORIZER_ENABLE_ALL_MODELS=${CATEGORIZER_ENABLE_ALL_MODELS:-false}
      - GEMMA_API_URL=http://**********:8089/v1/chat/completions
      - SMOLL_TOOLS_URL=http://smoll-vllm:8093/v1
      - ENABLE_QUEUE_CONSUMER=true
      - INPUT_QUEUE=preprocessing_queue
    volumes:
      - ./service-dashboard/backend/settings.json:/app/settings.json:ro
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8002:8002"
    restart: unless-stopped

  # Qwen VLLM Categorizer - GPU-accelerated categorization with local inference
  qwen-vllm-categorizer:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.qwen-vllm-categorizer
    image: omotesamba-qwen-vllm-categorizer:latest
    container_name: omotesamba-qwen-vllm-categorizer
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - PORT=8081
      - GPU_MEMORY_UTILIZATION=0.3
      - VLLM_WORKER_MULTIPROC_METHOD=spawn
      - VLLM_LOGGING_LEVEL=INFO
      - TOKENIZERS_PARALLELISM=false
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./config/qwen_vllm_config.yaml:/app/config/qwen_vllm_config.yaml:ro
      - ./logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8081:8081"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped

  # Company Enricher - Add real-time financial data to corporate documents
  company-enricher:
    build:
      context: .
      dockerfile: ./services/company_enricher/Dockerfile
    image: omotesamba-company-enricher:latest
    container_name: omotesamba-company-enricher
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - IB_HOST=tws
      - IB_PORT=7499
      - IB_CLIENT_ID=2
      - PORT=8025
      - COMPANY_ENRICHER_ENABLED=true
      - ENRICHER_INPUT_QUEUE=enrichment_input_queue
      - ENRICHER_OUTPUT_QUEUE=enrichment_completed_queue
      - ENRICHER_FAILED_QUEUE=enrichment_failed_dlq
      - ENRICHER_BATCH_SIZE=1
      - ENRICHER_PROCESSING_INTERVAL=5
      - COMPANY_DATA_CACHE_TTL=300
      - IBKR_HISTORICAL_DATA_TIMEOUT=30
      - MIN_COMPANY_DATA_COMPLETENESS=0.6
    depends_on:
      redis:
        condition: service_healthy
      tws:
        condition: service_healthy
    ports:
      - "8025:8025"
    restart: unless-stopped

  # Pro Analyzer - Deep AI analysis service
  pro-analyzer:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.pro-analyzer-lite
    image: omotesamba-pro-analyzer:latest
    container_name: omotesamba-pro-analyzer
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - PORT=8003
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - KIMI_API_KEY=${KIMI_API_KEY}
      - DEFAULT_PROVIDER=${DEFAULT_PROVIDER:-kimi}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-moonshot-v1-8k}
      - ENABLE_QUEUE_CONSUMER=true
    volumes:
      - ./service-dashboard/backend/settings.json:/app/settings.json:ro
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8003:8003"
    restart: unless-stopped

  # Prompt Manager Service - Manages AI prompts and versioning
  prompt-manager:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.prompt-manager-lite
    image: omotesamba-prompt-manager:latest
    container_name: omotesamba-prompt-manager
    command: ["python", "services/prompt_manager/service.py"]
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - DB_HOST=timescaledb
      - DB_PORT=5432
      - DB_NAME=omotesamba
      - DB_USER=omotesamba
      - DB_PASSWORD=omotesamba
      - PROMPT_MANAGER_PORT=8004
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8004:8004"
    restart: unless-stopped

  # === ANALYSIS & INTELLIGENCE ===

  # Market Analyzer - Generates market insights
  market-analyzer:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.market-analyzer-lite
    container_name: omotesamba-market-analyzer
    environment:
      - REDIS_URL=redis://redis:6379
      - MARKET_ANALYZER_PORT=8037
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - KIMI_API_KEY=${KIMI_API_KEY}
      - DEFAULT_PROVIDER=${DEFAULT_PROVIDER:-kimi}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-moonshot-v1-8k}
      - ENABLE_QUEUE_CONSUMER=true
      - EMBEDDINGS_SERVICE_URL=http://enhanced-embeddings:8000
      - GRAPH_MANAGER_URL=http://graph-manager:8000
    volumes:
      - ./services/market_analyzer:/app/services/market_analyzer
      - ./shared:/app/shared
      - ./service-dashboard/backend/settings.json:/app/settings.json:ro
    depends_on:
      redis:
        condition: service_healthy
      enhanced-embeddings:
        condition: service_started
      graph-manager:
        condition: service_started
    ports:
      - "8037:8037"
    restart: unless-stopped

  # Trading Signals Service - Signal generation
  trading-signals:
    build:
      context: .
      dockerfile: services/dockerfiles/Dockerfile.trading-signals
    container_name: omotesamba-trading-signals
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=postgresql+asyncpg://omotesamba:omotesamba@timescaledb:5432/omotesamba
      - TRADING_SIGNALS_PORT=8038
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - ENABLE_AUTO_GENERATION=false
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8038:8038"
    restart: unless-stopped

  # Prediction Tracker - Performance tracking with pipeline integration
  prediction-tracker:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.prediction-tracker
    container_name: omotesamba-prediction-tracker
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - PREDICTION_TRACKER_PORT=8007
      # Pipeline integration settings
      - QUEUE_CONSUMER_ENABLED=true
      - INPUT_QUEUE=prediction_input_queue
      - ACTIVE_TRACKING_QUEUE=active_tracking_queue
      - OUTPUT_QUEUE=pipeline_completed_queue
      - TRACKING_CHECK_INTERVAL=300
      - COMPANY_ENRICHER_URL=http://company-enricher:8025
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
      company-enricher:
        condition: service_started
    ports:
      - "8007:8007"
    restart: unless-stopped

  # === DATA & GRAPH SERVICES ===

  # Neo4j - Knowledge graph database
  neo4j:
    image: neo4j:5.20-community
    container_name: omotesamba-neo4j
    environment:
      - NEO4J_AUTH=neo4j/omotesamba
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    ports:
      - "7474:7474"
      - "7687:7687"
    restart: unless-stopped

  # Graph Manager - Neo4j knowledge graph service
  graph-manager:
    image: omotesamba-graph-manager:latest
    container_name: omotesamba-graph-manager
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=omotesamba
      - GRAPH_MANAGER_PORT=8008
    depends_on:
      - redis
      - neo4j
    ports:
      - "8008:8008"
    restart: unless-stopped


  # Enhanced Embeddings Service - Multi-modal embeddings for ChromaDB
  enhanced-embeddings:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.enhanced-embeddings
    container_name: omotesamba-enhanced-embeddings
    environment:
      - CHROMA_HOST=chromadb
      - CHROMA_PORT=8000
      - ENHANCED_EMBEDDINGS_PORT=8043
    depends_on:
      chromadb:
        condition: service_healthy
    ports:
      - "8043:8000"
    restart: unless-stopped

  # Company Data Sync - Daily IBKR data enrichment for JP/US companies
  company-data-sync:
    build:
      context: .
      dockerfile: ./services/company_data_sync/Dockerfile
    image: omotesamba-company-data-sync:latest
    container_name: omotesamba-company-data-sync
    restart: unless-stopped
    environment:
      # Database connection
      - DB_HOST=timescaledb
      - DB_PORT=5432
      - DB_USER=omotesamba
      - DB_PASSWORD=omotesamba
      - DB_NAME=omotesamba
      
      # IBKR connection
      - IB_HOST=tws
      - IB_PORT=7497
      - IB_CLIENT_ID=25
      
      # Redis connection
      - REDIS_URL=redis://redis:6379
      
      # Rate limiting configuration
      - JP_BATCH_SIZE=5
      - US_BATCH_SIZE=10
      - REQUEST_DELAY=3.0
      - BATCH_DELAY=15.0
      - MAX_COMPANIES_PER_RUN=500
      
      # Scheduling (disable internal scheduling, use external cron)
      - ENABLE_SCHEDULING=false
      - PORT=8050
      
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
      tws:
        condition: service_started
        
    ports:
      - "8050:8050"
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8050/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Company Data Sync Cron - Scheduled daily runs
  company-data-sync-cron:
    build:
      context: .
      dockerfile: ./services/company_data_sync/Dockerfile.cron
    image: omotesamba-company-data-sync-cron:latest
    container_name: omotesamba-company-data-sync-cron
    restart: unless-stopped
    environment:
      - COMPANY_DATA_SYNC_URL=http://company-data-sync:8050
      - CRON_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - SCHEDULER_TIMEOUT=7200   # 2 hours max
      
    depends_on:
      company-data-sync:
        condition: service_healthy
        
    healthcheck:
      test: ["CMD", "pgrep", "cron"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s


  # Queue Manager - Advanced queue orchestration
  queue-manager:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.queue-manager
    container_name: omotesamba-queue-manager
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - QUEUE_MANAGER_PORT=8010
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8010:8010"
    restart: unless-stopped

  # Pipeline Validator - Quality gates between processing stages
  pipeline-validator:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.pipeline-validator
    container_name: omotesamba-pipeline-validator
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - PIPELINE_VALIDATOR_PORT=8015
    volumes:
      - ./service-dashboard/backend/settings.json:/app/settings.json:ro
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8015:8015"
    restart: unless-stopped

  # Document Storage Service - Persists documents to all databases
  document-storage:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.document-storage
    container_name: omotesamba-document-storage
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - CHROMA_HOST=chromadb
      - CHROMA_PORT=8000
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=omotesamba
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
      chromadb:
        condition: service_started
      neo4j:
        condition: service_started
    restart: unless-stopped

  # Document TTL functionality moved to native Redis TTL in document-storage service

  # Quality Validation API - Real-time quality metrics and DLQ monitoring
  quality-validation-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile
    container_name: omotesamba-quality-validation-api
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - QUALITY_VALIDATION_API_PORT=8016
    command: python api/quality_validation_api.py
    depends_on:
      redis:
        condition: service_healthy
    ports:
      - "8016:8016"
    restart: unless-stopped

  # === MONITORING & MANAGEMENT ===

  # Monitoring Services API - Track Grafana stack status
  monitoring-services-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile
    container_name: omotesamba-monitoring-services-api
    environment:
      - MONITORING_SERVICES_API_PORT=8035
    command: python api/monitoring_services_api_simple.py
    ports:
      - "8035:8035"
    network_mode: host
    restart: unless-stopped

  # API Gateway - Service coordination (temporarily disabled - missing Dockerfile)
  # api-gateway:
  #   build:
  #     context: ./services/api_gateway
  #     dockerfile: ../dockerfiles/Dockerfile.api-gateway
  #   container_name: omotesamba-api-gateway
  #   environment:
  #     - REDIS_HOST=redis
  #     - REDIS_PORT=6379
  #     - API_GATEWAY_PORT=8080
  #   depends_on:
  #     redis:
  #       condition: service_healthy
  #   ports:
  #     - "8080:8080"
  #   restart: unless-stopped

  # Metrics Collector - System metrics
  metrics-collector:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.metrics-collector
    container_name: omotesamba-metrics-collector
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TIMESCALE_HOST=timescaledb
      - TIMESCALE_PORT=5432
      - TIMESCALE_DB=omotesamba
      - TIMESCALE_USER=omotesamba
      - TIMESCALE_PASSWORD=omotesamba
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - METRICS_COLLECTOR_PORT=8011
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8011:8011"
    restart: unless-stopped

  # Service Health Monitor - Comprehensive health monitoring for all services
  service-health-monitor:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.service-health-monitor
    container_name: omotesamba-service-health-monitor
    environment:
      - REDIS_URL=redis://redis:6379
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    ports:
      - "8095:8095"
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8095/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Document Count Monitor functionality consolidated into queue-monitor

  # Queue Monitor - Consolidated monitoring with flow tracking, emergency stop, and alerting
  queue-monitor:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.queue-monitor
    container_name: omotesamba-queue-monitor
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ALERT_THRESHOLD=5000
      - BACKUP_THRESHOLD=1000
      - DOCUMENT_COUNT_THRESHOLD=2
      - FLOW_WINDOW_MINUTES=60
      - FLOW_DATA_RETENTION_HOURS=24
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      redis:
        condition: service_healthy
    ports:
      - "8091:8090"  # Prometheus metrics port
    restart: unless-stopped

  # Queue Flow Tracker functionality consolidated into queue-monitor

  # Queue Monitor API functionality has been merged into queue-viewer-api
  # The queue-viewer-api now provides queue management endpoints

  # Queue Viewer API - Comprehensive queue management API
  queue-viewer-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile
    container_name: omotesamba-queue-viewer-api
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - QUEUE_VIEWER_API_PORT=8014
    command: python api/queue_viewer_api.py
    depends_on:
      redis:
        condition: service_healthy
    ports:
      - "8014:8014"
    restart: unless-stopped

  # Settings API - Feature flags and configuration management - CONSOLIDATED into dashboard-api
  # settings-api:
  #   build:
  #     context: ./service-dashboard/backend
  #     dockerfile: Dockerfile
  #   container_name: omotesamba-settings-api
  #   environment:
  #     - SETTINGS_API_PORT=8025
  #   command: python api/settings_api.py
  #   ports:
  #     - "8025:8025"
  #   volumes:
  #     - ./service-dashboard/backend/settings.json:/app/settings.json
  #     - ./service-dashboard/backend/scrapers/feature_flags.json:/app/scrapers/feature_flags.json
  #   restart: unless-stopped

  # GPU Monitor - Functionality merged into metrics-collector
  # gpu-monitor:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: omotesamba-gpu-monitor
  #   profiles: ["monitoring", "all"]
  #   environment:
  #     - REDIS_URL=redis://redis:6379
  #     - PYTHONPATH=/app
  #   depends_on:
  #     redis:
  #       condition: service_healthy
  #   command: python scripts/gpu_monitor.py
  #   volumes:
  #     - ./scripts:/app/scripts
  #     - ./shared:/app/shared
  #   restart: unless-stopped

  # === DASHBOARD SERVICES ===

  # System API - Core system monitoring - CONSOLIDATED into dashboard-api
  # system-api:
  #   build:
  #     context: ./service-dashboard/backend
  #     dockerfile: Dockerfile
  #   container_name: omotesamba-system-api
  #   ports:
  #     - "8031:8031"
  #   environment:
  #     - REDIS_HOST=redis
  #     - REDIS_PORT=6379
  #     - POSTGRES_HOST=timescaledb
  #     - POSTGRES_PORT=5432
  #     - POSTGRES_DB=omotesamba
  #     - POSTGRES_USER=omotesamba
  #     - POSTGRES_PASSWORD=omotesamba
  #     - SYSTEM_API_PORT=8031
  #   command: python api/system_api.py
  #   depends_on:
  #     redis:
  #       condition: service_healthy
  #     timescaledb:
  #       condition: service_healthy
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:8031/api/v1/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 40s
  #   restart: unless-stopped

  # Dashboard API - Main dashboard backend
  dashboard-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile
    container_name: omotesamba-dashboard-api
    ports:
      - "5000:5000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - DASHBOARD_API_PORT=5000
      # API URLs for all services
      - PDF_EXTRACTOR_URL=http://pdf-extractor:8001
      - CATEGORIZER_URL=http://categorizer:8002
      - QWEN_VLLM_CATEGORIZER_URL=http://qwen-vllm-categorizer:8081
      - PRO_ANALYZER_URL=http://pro-analyzer:8003
      - PROMPT_MANAGER_URL=http://prompt-manager:8004
      - MARKET_ANALYZER_URL=http://market-analyzer:8037
      - TRADING_SIGNALS_URL=http://trading-signals:8006
      - PREDICTION_TRACKER_URL=http://prediction-tracker:8007
      - GRAPH_MANAGER_URL=http://graph-manager:8008
      - EMBEDDINGS_SERVICE_URL=http://enhanced-embeddings:8000
      - QUEUE_MANAGER_URL=http://queue-manager:8010
      - METRICS_COLLECTOR_URL=http://metrics-collector:8011
      - QUEUE_MONITOR_URL=http://queue-viewer-api:8014
      - UNIFIED_BACKUP_URL=http://unified-backup:8013
    command: python api/dashboard_api.py
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # AI Analytics API - Real-time AI usage tracking and cost analysis with TimescaleDB
  ai-analytics:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile
    container_name: omotesamba-ai-analytics
    ports:
      - "8040:8040"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - PORT=8040
    command: python api/ai_analytics_api_timescale.py
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8040/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Extraction monitoring functionality has been merged into pipeline-validator
  # The pipeline-validator now provides extraction monitoring endpoints at port 8015

  # Reactive Settings API - Real-time configuration sync via Redis pub/sub
  reactive-settings-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile
    container_name: omotesamba-reactive-settings-api
    ports:
      - "8026:8026"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - REACTIVE_SETTINGS_API_PORT=8026
    command: python api/reactive_settings_simple.py
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./service-dashboard/backend/settings.json:/app/settings.json
    restart: unless-stopped

  # Stop Calculator API - IBKR-integrated stop loss calculator
  stop-calculator-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile
    container_name: omotesamba-stop-calculator-api
    ports:
      - "8017:8017"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379
      - IB_HOST=tws
      - IB_PORT=7499
      - IB_CLIENT_ID=1
      - PORT=8017
    command: python api/stop_calculator_api.py
    depends_on:
      redis:
        condition: service_healthy
      tws:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8017/api/stop-calculator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Market Tools API - Gold-denominated analysis with financial-data-api integration
  market-tools-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile.market-tools
    container_name: omotesamba-market-tools-api
    ports:
      - "8027:8026"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FINANCIAL_DATA_API_HOST=financial-data-api
      - FINANCIAL_DATA_API_PORT=8090
      - PORT=8026
      - MARKET_TOOLS_API_URL=http://market-tools-api:8026
    depends_on:
      redis:
        condition: service_healthy
      financial-data-api:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8026/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Unified Financial Data API - Consolidates all financial data providers
  financial-data-api:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.financial-data-api
    container_name: omotesamba-financial-data-api
    ports:
      - "8090:8090"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - IB_HOST=tws
      - IB_PORT=7499
      - IB_CLIENT_ID=10
      - PORT=8090
      - JQUANTS_API_KEY=${JQUANTS_API_KEY}
      - JQUANTS_MAILADDRESS=${JQUANTS_MAILADDRESS}
      - JQUANTS_PASSWORD=${JQUANTS_PASSWORD}
    depends_on:
      redis:
        condition: service_healthy
      tws:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # IBKR Trading Service - Order execution and portfolio management
  ibkr-trading:
    build:
      context: .
      dockerfile: ./services/dockerfiles/Dockerfile.ibkr-trading
    container_name: omotesamba-ibkr-trading
    ports:
      - "8095:8095"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - IB_HOST=tws
      - IB_PORT=7497  # Live trading port
      - IB_CLIENT_ID=10
      - PORT=8095
      - TRADING_ACCOUNT=${TRADING_ACCOUNT:-}
      - TRADING_API_KEY=${TRADING_API_KEY:-dev-trading-key}
    depends_on:
      redis:
        condition: service_healthy
      tws:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8095/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Frontend - Next.js Dashboard (includes integrated queue viewer)
  # Search API - Company and document search service
  search-api:
    build:
      context: ./service-dashboard/backend
      dockerfile: Dockerfile.search
    container_name: omotesamba-search-api
    ports:
      - "8028:8028"
    environment:
      - DB_HOST=timescaledb
      - DB_PORT=5432
      - DB_NAME=omotesamba
      - DB_USER=omotesamba
      - DB_PASSWORD=omotesamba
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - PORT=8028
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  dashboard-frontend:
    build:
      context: ./service-dashboard/frontend
      dockerfile: Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
        - CACHE_BUST=${CACHE_BUST:-default}
        - NEXT_PUBLIC_STOP_CALCULATOR_API_URL=http://localhost:8017
    container_name: omotesamba-dashboard-frontend
    ports:
      - "3000:3000"
    environment:
      - SYSTEM_API_URL=http://omotesamba-dashboard-api:5000
      - BACKEND_URL=http://omotesamba-dashboard-api:5000
      - DASHBOARD_API_URL=http://omotesamba-dashboard-api:5000
      - API_GATEWAY_URL=http://api-gateway:8080
      - NEO4J_BROWSER_URL=http://localhost:7474
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=timescaledb
      - POSTGRES_PORT=5432
      - POSTGRES_DB=omotesamba
      - PDF_EXTRACTOR_METRICS_URL=http://pdf-extractor:8080
      - POSTGRES_USER=omotesamba
      - POSTGRES_PASSWORD=omotesamba
      - SCRAPER_API_URL=http://scrapers:8001
      - SETTINGS_API_URL=http://dashboard-api:5000
      - QUEUE_MONITOR_API_URL=http://queue-viewer-api:8014
      - UNIFIED_BACKUP_API_URL=http://unified-backup:8013
      - NEXT_PUBLIC_UNIFIED_BACKUP_API_URL=http://localhost:8013
      - QUALITY_VALIDATION_API_URL=http://quality-validation-api:8016
      - QUEUE_VIEWER_API_URL=http://queue-viewer-api:8014
      - NEXT_PUBLIC_QUEUE_MONITOR_API_URL=http://queue-viewer-api:8014
      - AI_ANALYTICS_API_URL=http://ai-analytics:8040
      - NEXT_PUBLIC_AI_ANALYTICS_API_URL=http://localhost:8040
      - NEXT_PUBLIC_QUALITY_VALIDATION_API_URL=http://localhost:8016
      - MONITORING_SERVICES_API_URL=http://monitoring-services-api:8035
      - NEXT_PUBLIC_MONITORING_SERVICES_API_URL=http://localhost:8035
      - EXTRACTION_MONITORING_API_URL=http://pipeline-validator:8015
      - NEXT_PUBLIC_EXTRACTION_MONITORING_API_URL=http://localhost:8015
      - SEARCH_API_URL=http://search-api:8028
      - NEXT_PUBLIC_SEARCH_API_URL=http://localhost:8028
      - PIPELINE_VALIDATOR_URL=http://pipeline-validator:8015
      - PROMPT_MANAGER_URL=http://prompt-manager:8004
      - STOP_CALCULATOR_API_URL=http://stop-calculator-api:8017
      - NEXT_PUBLIC_STOP_CALCULATOR_API_URL=http://localhost:8017
      - FINANCIAL_DATA_API_URL=http://financial-data-api:8090
      - NEXT_PUBLIC_FINANCIAL_DATA_API_URL=http://localhost:8090
      # Legacy - to be removed
      - IBKR_DATA_API_URL=http://financial-data-api:8090
      - NEXT_PUBLIC_IBKR_DATA_API_URL=http://localhost:8090
      - YAHOO_FINANCE_API_URL=http://financial-data-api:8090
      - NEXT_PUBLIC_YAHOO_FINANCE_API_URL=http://localhost:8090
      - JQUANTS_API_URL=http://financial-data-api:8090
      - NEXT_PUBLIC_JQUANTS_API_URL=http://localhost:8090
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./service-dashboard/backend/alert_config.json:/config/alert_config.json:ro
    depends_on:
      - dashboard-api
      # - api-gateway  # temporarily disabled
    restart: unless-stopped

  # === BACKUP & RELIABILITY ===

  # Unified Backup Service - Ultra-sparse backup for all databases and assets
  unified-backup:
    build:
      context: ./services/unified-backup
      dockerfile: Dockerfile
    container_name: omotesamba-unified-backup
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DB_HOST=timescaledb
      - DB_PORT=5432
      - DB_NAME=omotesamba
      - DB_USER=omotesamba
      - DB_PASSWORD=omotesamba
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=omotesamba
      - UNIFIED_BACKUP_PORT=8013
      - BACKUP_BASE_DIR=/backups
    volumes:
      - ./backups:/backups
      - ./extracted_assets:/app/extracted_assets:ro
      - redis_data:/redis-data:ro
      - /var/run/docker.sock:/var/run/docker.sock
      - ./scraped_pdfs:/app/scraped_pdfs:ro
      - ./downloads:/app/downloads:ro
    depends_on:
      redis:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
    ports:
      - "8013:8013"
    healthcheck:
      test: ["CMD", "python", "/app/health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Real Estate Scraper - Continuous property monitoring and data collection
  real-estate-scraper:
    build:
      context: ./real-estate-scraper
      dockerfile: Dockerfile
    container_name: omotesamba-real-estate-scraper
    environment:
      - DATABASE_URL=***************************************************/omotesamba
      - REDIS_URL=redis://redis:6379
      - SCRAPE_INTERVAL_MINUTES=30
      - TARGET_AREAS=shibuya,minato,aoyama,omotesando
      - MAX_CONCURRENT_REQUESTS=5
      - REQUEST_DELAY_SECONDS=1.5
      - DEBUG=false
      - DATA_DIRECTORY=/app/data
      - IMAGES_DIRECTORY=/app/data/images
    volumes:
      - ./scraped_properties:/app/data
      - /var/run/docker.sock:/var/run/docker.sock:ro
    ports:
      - "8020:8000"
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # === AI MODELS ===

  # Gemma-3n - Local categorization model
  gemma3n:
    build:
      context: ./services/gemma3n
      dockerfile: Dockerfile
    container_name: omotesamba-gemma3n
    environment:
      - GEMMA_PORT=8089
      - GEMMA_HOST=0.0.0.0
      - GEMMA_GPU_LAYERS=999
      - GEMMA_CTX_SIZE=8192
      - GEMMA_BATCH_SIZE=512
      - GEMMA_THREADS=8
    ports:
      - "8089:8089"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8089/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: unless-stopped

volumes:
  redis_data:
  timescale_data:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  chroma_data:
  tws_data:
  tws_settings:
  huggingface_cache:

networks:
  default:
    name: omotesamba_default
