#!/usr/bin/env python3
"""
ABOUTME: Simple script to download NVIDIA OpenReasoning-Nemotron-14B model
ABOUTME: Downloads model files to local directory for testing
"""
import os
from huggingface_hub import snapshot_download

def main():
    print("🔄 Downloading NVIDIA OpenReasoning-Nemotron-14B...")
    print("📋 Model details: https://huggingface.co/nvidia/OpenReasoning-Nemotron-14B")
    
    # Create models directory
    model_dir = "./models/nemotron-14b"
    os.makedirs(model_dir, exist_ok=True)
    
    try:
        # Download model
        print("📥 Starting download (this will take a while - model is ~30GB)...")
        
        downloaded_path = snapshot_download(
            repo_id="nvidia/OpenReasoning-Nemotron-14B",
            local_dir=model_dir,
            resume_download=True,
            local_files_only=False,
            ignore_patterns=["*.bin"]  # Skip some large files if needed
        )
        
        print(f"✅ Model downloaded successfully to: {downloaded_path}")
        print(f"📁 Model files are in: {os.path.abspath(model_dir)}")
        
        # List downloaded files
        print("\n📂 Downloaded files:")
        for root, dirs, files in os.walk(model_dir):
            for file in files:
                file_path = os.path.join(root, file)
                size = os.path.getsize(file_path) / (1024*1024)  # MB
                print(f"   {file}: {size:.1f} MB")
                
    except Exception as e:
        print(f"❌ Error downloading model: {e}")
        print("💡 Try checking your internet connection or HuggingFace access")

if __name__ == "__main__":
    main()