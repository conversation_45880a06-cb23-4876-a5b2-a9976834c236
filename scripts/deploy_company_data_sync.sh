#!/bin/bash
# ABOUTME: Deployment script for company data sync service
# ABOUTME: Builds, deploys, and verifies the company data sync service and cron scheduler

set -e

echo "🚀 Deploying Company Data Sync Service..."

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: Must be run from omotesamba root directory"
    exit 1
fi

# Check if required services are running
echo "🔍 Checking required services..."

if ! docker ps | grep -q "omotesamba-timescaledb"; then
    echo "❌ TimescaleDB is not running. Start core services first:"
    echo "   docker-compose up -d timescaledb redis"
    exit 1
fi

if ! docker ps | grep -q "omotesamba-tws"; then
    echo "❌ IBKR TWS is not running. Start TWS service first:"
    echo "   docker-compose up -d tws"
    exit 1
fi

echo "✅ Required services are running"

# Build the services
echo "🔨 Building company data sync services..."
docker-compose build company-data-sync company-data-sync-cron

# Start the services
echo "🚀 Starting company data sync services..."
docker-compose up -d company-data-sync company-data-sync-cron

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Check service health
echo "🏥 Checking service health..."

# Check main service
if curl -f http://localhost:8050/api/status > /dev/null 2>&1; then
    echo "✅ Company Data Sync service is healthy"
else
    echo "❌ Company Data Sync service is not healthy"
    echo "📋 Service logs:"
    docker-compose logs --tail=20 company-data-sync
    exit 1
fi

# Check cron service
if docker-compose exec -T company-data-sync-cron pgrep cron > /dev/null 2>&1; then
    echo "✅ Company Data Sync Cron is healthy"
else
    echo "❌ Company Data Sync Cron is not healthy"
    echo "📋 Cron logs:"
    docker-compose logs --tail=20 company-data-sync-cron
    exit 1
fi

# Show service status
echo "📊 Service Status:"
curl -s http://localhost:8050/api/status | python3 -m json.tool || echo "Could not get status"

echo ""
echo "🎉 Company Data Sync Service deployed successfully!"
echo ""
echo "📋 Service Information:"
echo "   • Main Service: http://localhost:8050"
echo "   • Status API: http://localhost:8050/api/status"
echo "   • Metrics: http://localhost:8050/metrics"
echo "   • Daily Schedule: 2:00 AM (configurable via CRON_SCHEDULE)"
echo ""
echo "🔧 Manual Operations:"
echo "   • Trigger JP companies: curl -X POST http://localhost:8050/api/trigger/jp"
echo "   • Trigger US companies: curl -X POST http://localhost:8050/api/trigger/us"
echo "   • Trigger full sync: curl -X POST http://localhost:8050/api/trigger/full"
echo "   • Python trigger script: python3 scripts/trigger_company_data_sync.py --help"
echo ""
echo "📋 Container Management:"
echo "   • View logs: docker-compose logs -f company-data-sync"
echo "   • View cron logs: docker-compose logs -f company-data-sync-cron"
echo "   • Restart service: docker-compose restart company-data-sync"
echo "   • Stop service: docker-compose stop company-data-sync company-data-sync-cron"
echo ""
echo "⚙️ Configuration:"
echo "   • Rate limiting and batch sizes can be adjusted via environment variables"
echo "   • Cron schedule: CRON_SCHEDULE='0 2 * * *' (daily at 2 AM)"
echo "   • Max companies per run: MAX_COMPANIES_PER_RUN=500"

echo ""
echo "✅ Deployment complete!"