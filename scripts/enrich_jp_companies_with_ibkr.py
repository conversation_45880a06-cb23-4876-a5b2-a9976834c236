#!/usr/bin/env python3
"""
ABOUTME: Script to enrich Japanese companies with comprehensive IBKR fundamental data
ABOUTME: Fetches real-time financial metrics, ratios, and company details from IBKR API
"""

import os
import sys
import asyncio
import asyncpg
import logging
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from ib_insync import IB, Stock, util
import xml.etree.ElementTree as ET
import re
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5435,
    'user': 'omotesamba',
    'password': 'omotesamba',
    'database': 'omotesamba'
}

# IBKR configuration
IB_HOST = os.getenv('IB_HOST', 'localhost')
IB_PORT = int(os.getenv('IB_PORT', 7497))
IB_CLIENT_ID = int(os.getenv('IB_CLIENT_ID', 20))  # Use unique client ID

class IBKRJPCompanyEnricher:
    def __init__(self):
        self.ib = IB()
        self.db_pool = None
        self.processed_count = 0
        self.error_count = 0
        self.batch_size = 10  # Process in small batches to avoid rate limits
        
    async def initialize(self):
        """Initialize IBKR connection and database pool"""
        try:
            # Connect to IBKR
            await self.ib.connectAsync(IB_HOST, IB_PORT, clientId=IB_CLIENT_ID)
            logger.info(f"Connected to IBKR at {IB_HOST}:{IB_PORT}")
            
            # Create database pool
            self.db_pool = await asyncpg.create_pool(**DB_CONFIG)
            logger.info("Connected to database")
            
        except Exception as e:
            logger.error(f"Failed to initialize: {e}")
            raise
            
    async def cleanup(self):
        """Cleanup connections"""
        if self.ib.isConnected():
            self.ib.disconnect()
        if self.db_pool:
            await self.db_pool.close()
            
    async def get_companies_to_process(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get Japanese companies that need fundamental data enrichment"""
        query = """
        SELECT DISTINCT c.code, c.symbol, c.company_name, c.company_name_english,
               c.sector_17_name, c.scale_category, c.market_name
        FROM companies c
        LEFT JOIN company_market_data cf ON c.code = cf.code 
            AND c.country = cf.country 
            AND cf.snapshot_date = CURRENT_DATE
        WHERE c.country = 'JP' 
        AND c.is_active = true
        AND c.data_source = 'jquants'
        AND cf.id IS NULL  -- Only companies without today's fundamental data
        AND c.code IN ('7203', '6758', '9984', '8306', '4502', '6861', '9432', '9433')  -- Known tradeable companies
        ORDER BY c.code
        """
        
        if limit:
            query += f" LIMIT {limit}"
            
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch(query)
            
        companies = []
        for row in rows:
            companies.append({
                'code': row['code'],
                'symbol': row['symbol'],
                'company_name': row['company_name'],
                'company_name_english': row['company_name_english'],
                'sector': row['sector_17_name'],
                'scale_category': row['scale_category'],
                'market': row['market_name']
            })
            
        logger.info(f"Found {len(companies)} Japanese companies to process")
        return companies
        
    def create_jp_stock_contract(self, code: str) -> Stock:
        """Create IBKR contract for Japanese stock"""
        # Japanese stocks use TSEJ exchange with JPY currency
        return Stock(code, "TSEJ", "JPY")
        
    async def fetch_ibkr_fundamentals(self, company: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Fetch comprehensive fundamental data from IBKR for a Japanese company"""
        code = company['code']
        symbol = company['symbol']
        
        try:
            logger.info(f"Processing {code} ({company['company_name']})")
            
            # Create and qualify contract
            contract = self.create_jp_stock_contract(code)
            contracts = await self.ib.qualifyContractsAsync(contract)
            
            if not contracts:
                logger.warning(f"Could not qualify contract for {code}")
                return None
                
            qualified = contracts[0]
            logger.info(f"Qualified contract: {qualified}")
            
            # Initialize fundamental data structure
            fundamental_data = {
                'country': 'JP',
                'code': code,
                'symbol': symbol,
                'snapshot_date': date.today(),
                'data_source': 'ibkr',
                'currency': 'JPY'
            }
            
            # Get market data
            try:
                ticker = await self.ib.reqMktDataAsync(qualified, '', False, False)
                await asyncio.sleep(3)  # Wait for market data
                
                if ticker and ticker.last and ticker.last > 0:
                    fundamental_data.update({
                        'current_price': float(ticker.last),
                        'week_52_high': float(ticker.high52) if ticker.high52 and ticker.high52 > 0 else None,
                        'week_52_low': float(ticker.low52) if ticker.low52 and ticker.low52 > 0 else None,
                        'avg_volume_30d': int(ticker.avgVolume) if ticker.avgVolume and ticker.avgVolume > 0 else None
                    })
                    logger.info(f"Got market data for {code}: ¥{ticker.last}")
                else:
                    logger.warning(f"No valid market data for {code}")
                    
                # Cancel market data to avoid accumulation
                self.ib.cancelMktData(qualified)
                
            except Exception as e:
                logger.warning(f"Error getting market data for {code}: {e}")
            
            # Get fundamental data from IBKR
            try:
                logger.info(f"Requesting fundamental data for {code}")
                fundamental_xml = await self.ib.reqFundamentalDataAsync(qualified, "ReportsFinSummary")
                
                if fundamental_xml:
                    parsed_fundamentals = self.parse_ibkr_fundamental_xml(fundamental_xml)
                    fundamental_data.update(parsed_fundamentals)
                    logger.info(f"Parsed fundamental data for {code}: {len(parsed_fundamentals)} fields")
                else:
                    logger.warning(f"No fundamental XML data for {code}")
                    
            except Exception as e:
                logger.warning(f"Error getting fundamental data for {code}: {e}")
            
            # Get additional financial ratios
            try:
                ratios_xml = await self.ib.reqFundamentalDataAsync(qualified, "ReportsRatios")
                if ratios_xml:
                    parsed_ratios = self.parse_ibkr_ratios_xml(ratios_xml)
                    fundamental_data.update(parsed_ratios)
                    logger.info(f"Parsed ratios for {code}: {len(parsed_ratios)} fields")
                    
            except Exception as e:
                logger.warning(f"Error getting ratios for {code}: {e}")
            
            # Get company snapshot for additional details
            try:
                snapshot_xml = await self.ib.reqFundamentalDataAsync(qualified, "ReportsFinStatements")
                if snapshot_xml:
                    parsed_snapshot = self.parse_ibkr_snapshot_xml(snapshot_xml)
                    fundamental_data.update(parsed_snapshot)
                    logger.info(f"Parsed snapshot for {code}: {len(parsed_snapshot)} fields")
                    
            except Exception as e:
                logger.warning(f"Error getting snapshot for {code}: {e}")
            
            # Calculate data quality score
            fundamental_data['data_quality_score'] = self.calculate_data_quality(fundamental_data)
            
            logger.info(f"✅ Successfully enriched {code} with quality score {fundamental_data['data_quality_score']}")
            return fundamental_data
            
        except Exception as e:
            logger.error(f"Error processing {code}: {e}")
            self.error_count += 1
            return None
            
    def parse_ibkr_fundamental_xml(self, xml_data: str) -> Dict[str, Any]:
        """Parse IBKR fundamental XML data"""
        data = {}
        try:
            # Remove namespace prefixes for easier parsing
            clean_xml = re.sub(r'xmlns[^=]*="[^"]*"', '', xml_data)
            clean_xml = re.sub(r'[a-zA-Z]+:', '', clean_xml)
            
            root = ET.fromstring(clean_xml)
            
            # Parse financial highlights
            for highlight in root.findall('.//FinancialHighlight'):
                # Revenue
                revenue = highlight.find('.//Revenue[@periodType="TTM"]')
                if revenue is not None and revenue.text:
                    try:
                        data['revenue_ttm'] = int(float(revenue.text) * 1000000)  # Convert to full amount
                    except:
                        pass
                
                # Net Income  
                net_income = highlight.find('.//NetIncome[@periodType="TTM"]')
                if net_income is not None and net_income.text:
                    try:
                        data['net_income_ttm'] = int(float(net_income.text) * 1000000)
                    except:
                        pass
                
                # EBITDA
                ebitda = highlight.find('.//EBITDA[@periodType="TTM"]')
                if ebitda is not None and ebitda.text:
                    try:
                        data['ebitda_ttm'] = int(float(ebitda.text) * 1000000)
                    except:
                        pass
            
            # Parse ratios
            for ratio_group in root.findall('.//Ratios'):
                # P/E Ratio
                pe_ratio = ratio_group.find('.//PriceEarningsRatio')
                if pe_ratio is not None and pe_ratio.text:
                    try:
                        data['pe_ratio'] = float(pe_ratio.text)
                    except:
                        pass
                
                # P/B Ratio
                pb_ratio = ratio_group.find('.//PriceBookRatio')
                if pb_ratio is not None and pb_ratio.text:
                    try:
                        data['pb_ratio'] = float(pb_ratio.text)
                    except:
                        pass
                
                # ROE
                roe = ratio_group.find('.//ReturnOnEquity')
                if roe is not None and roe.text:
                    try:
                        data['roe'] = float(roe.text) / 100  # Convert percentage to decimal
                    except:
                        pass
                
                # Debt to Equity
                debt_equity = ratio_group.find('.//DebtEquityRatio')
                if debt_equity is not None and debt_equity.text:
                    try:
                        data['debt_to_equity'] = float(debt_equity.text)
                    except:
                        pass
            
            logger.debug(f"Parsed fundamental data: {data}")
            
        except Exception as e:
            logger.warning(f"Error parsing fundamental XML: {e}")
            
        return data
        
    def parse_ibkr_ratios_xml(self, xml_data: str) -> Dict[str, Any]:
        """Parse IBKR ratios XML data"""
        data = {}
        try:
            clean_xml = re.sub(r'xmlns[^=]*="[^"]*"', '', xml_data)
            clean_xml = re.sub(r'[a-zA-Z]+:', '', clean_xml)
            
            root = ET.fromstring(clean_xml)
            
            # Look for specific ratio values
            ratio_mappings = {
                'CurrentRatio': 'current_ratio',
                'QuickRatio': 'quick_ratio', 
                'DebtToAssets': 'debt_to_assets',
                'ReturnOnAssets': 'roa',
                'GrossMargin': 'gross_margin',
                'OperatingMargin': 'operating_margin',
                'NetMargin': 'net_margin',
                'DividendYield': 'dividend_yield'
            }
            
            for xml_name, db_field in ratio_mappings.items():
                element = root.find(f'.//{xml_name}')
                if element is not None and element.text:
                    try:
                        value = float(element.text)
                        # Convert percentages to decimals for margin and yield fields
                        if db_field in ['gross_margin', 'operating_margin', 'net_margin', 'dividend_yield', 'roa']:
                            value = value / 100
                        data[db_field] = value
                    except:
                        pass
                        
        except Exception as e:
            logger.warning(f"Error parsing ratios XML: {e}")
            
        return data
        
    def parse_ibkr_snapshot_xml(self, xml_data: str) -> Dict[str, Any]:
        """Parse IBKR snapshot XML for additional company details"""
        data = {}
        try:
            clean_xml = re.sub(r'xmlns[^=]*="[^"]*"', '', xml_data)
            clean_xml = re.sub(r'[a-zA-Z]+:', '', clean_xml)
            
            root = ET.fromstring(clean_xml)
            
            # Company info
            company_info = root.find('.//CompanyInfo')
            if company_info is not None:
                employees = company_info.find('.//Employees')
                if employees is not None and employees.text:
                    try:
                        data['employees_count'] = int(employees.text)
                    except:
                        pass
                
                description = company_info.find('.//BusinessDescription')
                if description is not None and description.text:
                    data['business_description'] = description.text[:1000]  # Limit length
                    
                website = company_info.find('.//WebsiteURL')
                if website is not None and website.text:
                    data['website_url'] = website.text
            
            # Balance sheet data
            balance_sheet = root.find('.//BalanceSheet')
            if balance_sheet is not None:
                total_assets = balance_sheet.find('.//TotalAssets')
                if total_assets is not None and total_assets.text:
                    try:
                        data['total_assets'] = int(float(total_assets.text) * 1000000)
                    except:
                        pass
                
                total_debt = balance_sheet.find('.//TotalDebt')
                if total_debt is not None and total_debt.text:
                    try:
                        data['total_debt'] = int(float(total_debt.text) * 1000000)
                    except:
                        pass
                        
                shareholders_equity = balance_sheet.find('.//ShareholdersEquity')
                if shareholders_equity is not None and shareholders_equity.text:
                    try:
                        data['shareholders_equity'] = int(float(shareholders_equity.text) * 1000000)
                    except:
                        pass
                        
        except Exception as e:
            logger.warning(f"Error parsing snapshot XML: {e}")
            
        return data
        
    def calculate_data_quality(self, data: Dict[str, Any]) -> float:
        """Calculate data quality score based on available fields"""
        required_fields = ['current_price', 'pe_ratio', 'revenue_ttm']
        important_fields = ['pb_ratio', 'roe', 'debt_to_equity', 'net_income_ttm', 'ebitda_ttm']
        optional_fields = ['current_ratio', 'dividend_yield', 'employees_count', 'total_assets']
        
        required_score = sum(1 for field in required_fields if data.get(field) is not None) / len(required_fields)
        important_score = sum(1 for field in important_fields if data.get(field) is not None) / len(important_fields)
        optional_score = sum(1 for field in optional_fields if data.get(field) is not None) / len(optional_fields)
        
        # Weighted score: required 50%, important 35%, optional 15%
        total_score = (required_score * 0.5) + (important_score * 0.35) + (optional_score * 0.15)
        return round(total_score, 2)
        
    async def insert_fundamental_data(self, fundamental_data: Dict[str, Any]) -> bool:
        """Insert fundamental data into database"""
        try:
            insert_query = """
            INSERT INTO company_market_data (
                country, code, symbol, snapshot_date, data_source, currency,
                current_price, pe_ratio, pb_ratio, ps_ratio, roe, roa,
                debt_to_equity, current_ratio, quick_ratio, debt_to_assets,
                gross_margin, operating_margin, net_margin, dividend_yield,
                revenue_ttm, net_income_ttm, ebitda_ttm, total_assets, total_debt,
                shareholders_equity, employees_count, business_description, website_url,
                week_52_high, week_52_low, avg_volume_30d, data_quality_score
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16,
                $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33
            )
            ON CONFLICT (country, code, snapshot_date, data_source)
            DO UPDATE SET
                current_price = EXCLUDED.current_price,
                pe_ratio = EXCLUDED.pe_ratio,
                pb_ratio = EXCLUDED.pb_ratio,
                ps_ratio = EXCLUDED.ps_ratio,
                roe = EXCLUDED.roe,
                roa = EXCLUDED.roa,
                debt_to_equity = EXCLUDED.debt_to_equity,
                current_ratio = EXCLUDED.current_ratio,
                quick_ratio = EXCLUDED.quick_ratio,
                debt_to_assets = EXCLUDED.debt_to_assets,
                gross_margin = EXCLUDED.gross_margin,
                operating_margin = EXCLUDED.operating_margin,
                net_margin = EXCLUDED.net_margin,
                dividend_yield = EXCLUDED.dividend_yield,
                revenue_ttm = EXCLUDED.revenue_ttm,
                net_income_ttm = EXCLUDED.net_income_ttm,
                ebitda_ttm = EXCLUDED.ebitda_ttm,
                total_assets = EXCLUDED.total_assets,
                total_debt = EXCLUDED.total_debt,
                shareholders_equity = EXCLUDED.shareholders_equity,
                employees_count = EXCLUDED.employees_count,
                business_description = EXCLUDED.business_description,
                website_url = EXCLUDED.website_url,
                week_52_high = EXCLUDED.week_52_high,
                week_52_low = EXCLUDED.week_52_low,
                avg_volume_30d = EXCLUDED.avg_volume_30d,
                data_quality_score = EXCLUDED.data_quality_score,
                last_updated = CURRENT_TIMESTAMP
            """
            
            async with self.db_pool.acquire() as conn:
                await conn.execute(
                    insert_query,
                    fundamental_data.get('country'), fundamental_data.get('code'),
                    fundamental_data.get('symbol'), fundamental_data.get('snapshot_date'),
                    fundamental_data.get('data_source'), fundamental_data.get('currency'),
                    fundamental_data.get('current_price'), fundamental_data.get('pe_ratio'),
                    fundamental_data.get('pb_ratio'), fundamental_data.get('ps_ratio'),
                    fundamental_data.get('roe'), fundamental_data.get('roa'),
                    fundamental_data.get('debt_to_equity'), fundamental_data.get('current_ratio'),
                    fundamental_data.get('quick_ratio'), fundamental_data.get('debt_to_assets'),
                    fundamental_data.get('gross_margin'), fundamental_data.get('operating_margin'),
                    fundamental_data.get('net_margin'), fundamental_data.get('dividend_yield'),
                    fundamental_data.get('revenue_ttm'), fundamental_data.get('net_income_ttm'),
                    fundamental_data.get('ebitda_ttm'), fundamental_data.get('total_assets'),
                    fundamental_data.get('total_debt'), fundamental_data.get('shareholders_equity'),
                    fundamental_data.get('employees_count'), fundamental_data.get('business_description'),
                    fundamental_data.get('website_url'), fundamental_data.get('week_52_high'),
                    fundamental_data.get('week_52_low'), fundamental_data.get('avg_volume_30d'),
                    fundamental_data.get('data_quality_score')
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Error inserting fundamental data for {fundamental_data.get('code')}: {e}")
            return False
            
    async def get_enrichment_stats(self) -> Dict[str, Any]:
        """Get statistics on fundamental data enrichment"""
        async with self.db_pool.acquire() as conn:
            # Total enriched companies today
            today_count = await conn.fetchval("""
                SELECT COUNT(*) FROM company_market_data 
                WHERE snapshot_date = CURRENT_DATE AND country = 'JP'
            """)
            
            # Average data quality
            avg_quality = await conn.fetchval("""
                SELECT AVG(data_quality_score) FROM company_market_data 
                WHERE snapshot_date = CURRENT_DATE AND country = 'JP'
            """)
            
            # Quality distribution
            quality_dist = await conn.fetch("""
                SELECT 
                    CASE 
                        WHEN data_quality_score >= 0.8 THEN 'High (≥0.8)'
                        WHEN data_quality_score >= 0.6 THEN 'Medium (0.6-0.8)'
                        WHEN data_quality_score >= 0.4 THEN 'Low (0.4-0.6)'
                        ELSE 'Very Low (<0.4)'
                    END as quality_tier,
                    COUNT(*) as count
                FROM company_market_data 
                WHERE snapshot_date = CURRENT_DATE AND country = 'JP'
                GROUP BY quality_tier
                ORDER BY quality_tier
            """)
            
            return {
                'total_enriched_today': today_count,
                'average_quality_score': float(avg_quality) if avg_quality else 0.0,
                'quality_distribution': [{'tier': row['quality_tier'], 'count': row['count']} for row in quality_dist]
            }
            
    async def enrich_japanese_companies(self, limit: Optional[int] = None):
        """Main method to enrich Japanese companies with IBKR fundamental data"""
        try:
            logger.info("Starting Japanese companies IBKR fundamental data enrichment...")
            
            # Initialize connections
            await self.initialize()
            
            # Get companies to process
            companies = await self.get_companies_to_process(limit)
            if not companies:
                logger.info("No companies found that need enrichment")
                return
            
            # Process companies in batches
            for i in range(0, len(companies), self.batch_size):
                batch = companies[i:i + self.batch_size]
                logger.info(f"\n=== Processing batch {i//self.batch_size + 1}: companies {i+1}-{min(i+self.batch_size, len(companies))} ===")
                
                for company in batch:
                    try:
                        # Fetch fundamental data from IBKR
                        fundamental_data = await self.fetch_ibkr_fundamentals(company)
                        
                        if fundamental_data:
                            # Insert into database
                            success = await self.insert_fundamental_data(fundamental_data)
                            if success:
                                self.processed_count += 1
                                logger.info(f"✅ Saved fundamental data for {company['code']}")
                            else:
                                self.error_count += 1
                        else:
                            self.error_count += 1
                        
                        # Rate limiting - wait between requests
                        await asyncio.sleep(2)
                        
                    except Exception as e:
                        logger.error(f"Error processing company {company['code']}: {e}")
                        self.error_count += 1
                        continue
                
                # Wait between batches
                if i + self.batch_size < len(companies):
                    logger.info(f"Waiting 10 seconds before next batch...")
                    await asyncio.sleep(10)
            
            # Get final statistics
            stats = await self.get_enrichment_stats()
            
            # Report results
            logger.info("=" * 80)
            logger.info("JAPANESE COMPANIES IBKR ENRICHMENT COMPLETE")
            logger.info("=" * 80)
            logger.info(f"✅ Successfully processed: {self.processed_count} companies")
            logger.info(f"❌ Errors encountered: {self.error_count} companies")
            logger.info(f"📊 Total enriched today: {stats['total_enriched_today']} companies")
            logger.info(f"🎯 Average data quality: {stats['average_quality_score']:.2f}")
            
            logger.info("\n📈 Data Quality Distribution:")
            for dist in stats['quality_distribution']:
                logger.info(f"   • {dist['tier']}: {dist['count']} companies")
            
            return True
            
        except Exception as e:
            logger.error(f"Enrichment failed: {e}")
            return False
        finally:
            await self.cleanup()


async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enrich Japanese companies with IBKR fundamental data')
    parser.add_argument('--limit', type=int, help='Limit number of companies to process (for testing)')
    parser.add_argument('--batch-size', type=int, default=10, help='Batch size for processing')
    
    args = parser.parse_args()
    
    enricher = IBKRJPCompanyEnricher()
    if args.batch_size:
        enricher.batch_size = args.batch_size
        
    success = await enricher.enrich_japanese_companies(args.limit)
    
    if success:
        logger.info("\n🎉 Japanese companies enrichment completed successfully!")
        logger.info("💡 You can now view enriched data in the company_fundamentals table")
        sys.exit(0)
    else:
        logger.error("\n❌ Japanese companies enrichment failed")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())