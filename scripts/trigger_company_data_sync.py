#!/usr/bin/env python3
"""
ABOUTME: Manual trigger script for company data sync service
ABOUTME: Allows manual triggering of JP, US, or full company data enrichment
"""

import asyncio
import aiohttp
import argparse
import logging
import sys
from datetime import datetime
from typing import Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO, 
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def trigger_enrichment(service_url: str, endpoint: str, limit: Optional[int] = None) -> bool:
    """Trigger company data enrichment"""
    try:
        logger.info(f"🎯 Triggering {endpoint} enrichment...")
        
        params = {}
        if limit:
            params['limit'] = limit
            
        async with aiohttp.ClientSession() as session:
            # Check status first
            async with session.get(f"{service_url}/api/status") as response:
                if response.status != 200:
                    logger.error(f"❌ Service not available: {response.status}")
                    return False
                    
                status = await response.json()
                if status.get('status') == 'running':
                    logger.warning("⚠️ Service is already running")
                    return False
                    
                logger.info("✅ Service is available and idle")
            
            # Trigger enrichment
            async with session.post(f"{service_url}/api/{endpoint}", params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ Triggered successfully: {result.get('message')}")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to trigger: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ Error triggering enrichment: {e}")
        return False


async def wait_and_monitor(service_url: str, max_wait_seconds: int = 3600) -> bool:
    """Wait for enrichment to complete and monitor progress"""
    start_time = datetime.now()
    last_stats = None
    
    logger.info("⏳ Monitoring enrichment progress...")
    
    while (datetime.now() - start_time).total_seconds() < max_wait_seconds:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{service_url}/api/status") as response:
                    if response.status == 200:
                        status = await response.json()
                        
                        if status.get('status') == 'idle':
                            # Get final stats
                            stats = status.get('stats', {})
                            
                            logger.info("🎉 Enrichment completed!")
                            logger.info("📊 Final Results:")
                            logger.info(f"   • JP Processed: {stats.get('jp_processed', 0)}")
                            logger.info(f"   • JP Errors: {stats.get('jp_errors', 0)}")
                            logger.info(f"   • US Processed: {stats.get('us_processed', 0)}")
                            logger.info(f"   • US Errors: {stats.get('us_errors', 0)}")
                            
                            duration = datetime.now() - start_time
                            logger.info(f"⏱️ Total Duration: {duration}")
                            
                            return True
                            
                        elif status.get('status') == 'running':
                            # Show progress if stats changed
                            current_stats = status.get('stats', {})
                            if current_stats != last_stats:
                                logger.info(f"📈 Progress - JP: {current_stats.get('jp_processed', 0)}, US: {current_stats.get('us_processed', 0)}")
                                last_stats = current_stats.copy()
                        
                        else:
                            logger.warning(f"⚠️ Unexpected status: {status.get('status')}")
            
            await asyncio.sleep(30)  # Check every 30 seconds
            
        except Exception as e:
            logger.warning(f"⚠️ Error checking status: {e}")
            await asyncio.sleep(60)  # Wait longer on error
    
    logger.error(f"❌ Enrichment did not complete within {max_wait_seconds} seconds")
    return False


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Trigger company data sync enrichment')
    
    parser.add_argument('--url', default='http://localhost:8050', 
                       help='Company data sync service URL')
    parser.add_argument('--type', choices=['jp', 'us', 'full'], default='full',
                       help='Type of enrichment to run')
    parser.add_argument('--limit', type=int, 
                       help='Limit number of companies to process (for testing)')
    parser.add_argument('--wait', action='store_true',
                       help='Wait for completion and monitor progress')
    parser.add_argument('--timeout', type=int, default=3600,
                       help='Maximum wait time in seconds')
    
    args = parser.parse_args()
    
    # Map type to endpoint
    endpoint_map = {
        'jp': 'trigger/jp',
        'us': 'trigger/us', 
        'full': 'trigger/full'
    }
    
    endpoint = endpoint_map[args.type]
    
    logger.info("🚀 Company Data Sync Manual Trigger")
    logger.info(f"   • Service URL: {args.url}")
    logger.info(f"   • Type: {args.type}")
    logger.info(f"   • Limit: {args.limit or 'None'}")
    logger.info(f"   • Wait for completion: {args.wait}")
    
    # Trigger enrichment
    success = await trigger_enrichment(args.url, endpoint, args.limit)
    
    if not success:
        logger.error("❌ Failed to trigger enrichment")
        sys.exit(1)
    
    if args.wait:
        # Wait for completion
        success = await wait_and_monitor(args.url, args.timeout)
        
        if success:
            logger.info("✅ Enrichment completed successfully")
            sys.exit(0)
        else:
            logger.error("❌ Enrichment failed or timed out")
            sys.exit(1)
    else:
        logger.info("✅ Enrichment triggered successfully (not waiting for completion)")
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())