#!/usr/bin/env python3
# ABOUTME: Search API for companies and documents in the financial database
# ABOUTME: Provides unified search across TimescaleDB with intelligent ranking

import os
import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from flask import Flask, jsonify, request
from flask_cors import CORS
import psycopg2
from psycopg2.extras import RealDictCursor
import redis

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Database configuration
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '5435')
DB_NAME = os.getenv('DB_NAME', 'omotesamba')
DB_USER = os.getenv('DB_USER', 'omotesamba')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'omotesamba')

# Redis configuration for caching
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6381))
CACHE_TTL = 300  # 5 minutes cache

def get_db_connection():
    """Get database connection."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            cursor_factory=RealDictCursor
        )
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        return None

def get_redis_connection():
    """Get Redis connection."""
    try:
        return redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
    except Exception as e:
        logger.error(f"Redis connection error: {e}")
        return None

def search_companies(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Search for companies by name, code, or other identifiers."""
    conn = get_db_connection()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        
        # Clean query - remove common stock exchange suffixes
        clean_query = query
        for suffix in ['.T', '.TO', '.TSE', '.NYSE', '.NASDAQ', '.L', '.HK']:
            if clean_query.upper().endswith(suffix):
                clean_query = clean_query[:-len(suffix)]
                break
        
        # Search in multiple fields with ranking
        search_query = """
        SELECT DISTINCT
            code as id,
            company_name as title,
            CONCAT(code, ' - ', COALESCE(sector_17_name, 'Unknown Sector')) as subtitle,
            jsonb_build_object(
                'code', code,
                'sector', COALESCE(sector_17_name, 'Unknown'),
                'market', COALESCE(market_name, 'Unknown'),
                'last_updated', last_updated,
                'document_count', (
                    SELECT COUNT(*) 
                    FROM documents d 
                    WHERE d.company_code = c.code
                )
            ) as metadata,
            -- Ranking based on match type
            CASE 
                WHEN code = %s THEN 1
                WHEN code ILIKE %s THEN 2
                WHEN company_name ILIKE %s THEN 3
                WHEN company_name ILIKE %s THEN 4
                WHEN sector_17_name ILIKE %s THEN 5
                ELSE 6
            END as rank
        FROM companies c
        WHERE 
            code = %s OR
            code ILIKE %s OR
            company_name ILIKE %s OR
            sector_17_name ILIKE %s
        ORDER BY rank, company_name
        LIMIT %s
        """
        
        like_query = f"%{clean_query}%"
        exact_code = clean_query.upper()
        exact_name = f"{clean_query}%"
        
        cursor.execute(search_query, (
            exact_code, exact_code, exact_name, like_query, like_query,
            exact_code, exact_code, like_query, like_query, limit
        ))
        
        results = []
        for row in cursor.fetchall():
            results.append({
                'type': 'company',
                'id': row['id'],
                'title': row['title'],
                'subtitle': row['subtitle'],
                'metadata': row['metadata']
            })
        
        return results
        
    except Exception as e:
        logger.error(f"Company search error: {e}")
        return []
    finally:
        conn.close()

def search_documents(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Search for documents by ID, title, content, or metadata."""
    conn = get_db_connection()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        
        # Search in multiple document fields
        search_query = """
        SELECT 
            id::text,
            COALESCE(title, 'Document ' || SUBSTRING(id::text, 1, 8)) as title,
            CONCAT(
                COALESCE(company_code, 'Unknown'), 
                ' - ', 
                COALESCE(doc_type, 'Unknown Type'),
                CASE 
                    WHEN published_date IS NOT NULL 
                    THEN ' - ' || TO_CHAR(published_date, 'YYYY-MM-DD')
                    ELSE ''
                END
            ) as subtitle,
            jsonb_build_object(
                'company_code', company_code,
                'document_type', doc_type,
                'date', TO_CHAR(published_date, 'YYYY-MM-DD'),
                'category', category,
                'status', status,
                'source', source
            ) as metadata,
            -- Ranking based on match type
            CASE 
                WHEN id::text = %s THEN 1
                WHEN id::text ILIKE %s THEN 2
                WHEN title ILIKE %s THEN 3
                WHEN company_code ILIKE %s THEN 4
                WHEN doc_type ILIKE %s THEN 5
                ELSE 6
            END as rank
        FROM documents
        WHERE 
            id::text = %s OR
            id::text ILIKE %s OR
            title ILIKE %s OR
            company_code ILIKE %s OR
            doc_type ILIKE %s
        ORDER BY rank, published_date DESC
        LIMIT %s
        """
        
        like_query = f"%{query}%"
        exact_id = query
        
        cursor.execute(search_query, (
            exact_id, like_query, like_query, like_query, like_query,
            exact_id, like_query, like_query, like_query, like_query,
            limit
        ))
        
        results = []
        for row in cursor.fetchall():
            results.append({
                'type': 'document',
                'id': row['id'],
                'title': row['title'],
                'subtitle': row['subtitle'],
                'metadata': row['metadata']
            })
        
        return results
        
    except Exception as e:
        logger.error(f"Document search error: {e}")
        return []
    finally:
        conn.close()

@app.route('/api/search', methods=['GET'])
def search():
    """Unified search endpoint for companies and documents."""
    try:
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'results': [], 'error': 'Query parameter required'})
        
        # Limit search length to prevent abuse
        if len(query) < 2:
            return jsonify({'results': [], 'error': 'Query too short (minimum 2 characters)'})
        
        if len(query) > 100:
            query = query[:100]
        
        # Check cache first
        redis_client = get_redis_connection()
        cache_key = f"search:{query.lower()}"
        
        if redis_client:
            cached_result = redis_client.get(cache_key)
            if cached_result:
                try:
                    cached_data = json.loads(cached_result)
                    logger.info(f"Search cache hit for query: {query}")
                    return jsonify(cached_data)
                except json.JSONDecodeError:
                    pass
        
        # Search both companies and documents
        company_results = search_companies(query, limit=5)
        document_results = search_documents(query, limit=10)
        
        # Combine and limit total results
        all_results = company_results + document_results
        total_results = len(all_results)
        
        # If we have too many results, prioritize companies first
        if len(all_results) > 15:
            all_results = company_results + document_results[:15-len(company_results)]
        
        response_data = {
            'results': all_results,
            'total': total_results,
            'query': query,
            'timestamp': datetime.now().isoformat()
        }
        
        # Cache the results
        if redis_client:
            try:
                redis_client.setex(cache_key, CACHE_TTL, json.dumps(response_data))
            except Exception as e:
                logger.warning(f"Cache write error: {e}")
        
        logger.info(f"Search completed for '{query}': {len(company_results)} companies, {len(document_results)} documents")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Search API error: {e}")
        return jsonify({'error': 'Internal server error', 'results': []}), 500

@app.route('/api/search/company/<company_code>', methods=['GET'])
def get_company_details(company_code: str):
    """Get detailed information about a specific company."""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500
    
    try:
        cursor = conn.cursor()
        
        # Get company information
        cursor.execute("""
        SELECT *,
            (SELECT COUNT(*) FROM documents WHERE company_code = %s) as document_count,
            (SELECT MAX(created_at) FROM documents WHERE company_code = %s) as last_document_date
        FROM companies 
        WHERE code = %s
        """, (company_code, company_code, company_code))
        
        company = cursor.fetchone()
        if not company:
            return jsonify({'error': 'Company not found'}), 404
        
        # Get recent documents for this company
        cursor.execute("""
        SELECT 
            id,
            doc_type as document_type,
            created_at,
            status as current_stage,
            category,
            title
        FROM documents 
        WHERE company_code = %s 
        ORDER BY created_at DESC 
        LIMIT 10
        """, (company_code,))
        
        recent_documents = cursor.fetchall()
        
        return jsonify({
            'company': dict(company),
            'recent_documents': [dict(doc) for doc in recent_documents]
        })
        
    except Exception as e:
        logger.error(f"Company details error: {e}")
        return jsonify({'error': 'Internal server error'}), 500
    finally:
        conn.close()

@app.route('/api/search/document/<document_id>', methods=['GET'])
def get_document_details(document_id: str):
    """Get detailed information about a specific document."""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500
    
    try:
        cursor = conn.cursor()
        
        # Get document information
        cursor.execute("SELECT * FROM documents WHERE id = %s", (document_id,))
        document = cursor.fetchone()
        
        if not document:
            return jsonify({'error': 'Document not found'}), 404
        
        return jsonify({'document': dict(document)})
        
    except Exception as e:
        logger.error(f"Document details error: {e}")
        return jsonify({'error': 'Internal server error'}), 500
    finally:
        conn.close()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    try:
        # Test database connection
        conn = get_db_connection()
        if conn:
            conn.close()
            db_status = "healthy"
        else:
            db_status = "unhealthy"
        
        # Test Redis connection
        redis_client = get_redis_connection()
        if redis_client:
            redis_client.ping()
            redis_status = "healthy"
        else:
            redis_status = "unhealthy"
        
        return jsonify({
            'status': 'healthy' if db_status == 'healthy' and redis_status == 'healthy' else 'degraded',
            'database': db_status,
            'redis': redis_status,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return jsonify({'status': 'unhealthy', 'error': str(e)}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8028))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting Search API on port {port}")
    logger.info(f"Database: {DB_HOST}:{DB_PORT}/{DB_NAME}")
    logger.info(f"Redis: {REDIS_HOST}:{REDIS_PORT}")
    
    app.run(host='0.0.0.0', port=port, debug=debug)