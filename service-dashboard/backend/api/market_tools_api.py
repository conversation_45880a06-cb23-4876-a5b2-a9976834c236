# ABOUTME: Optimized Market Tools API with parallel processing and smart caching
# ABOUTME: Provides gold-denominated prices, FX rates, and historical analysis

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Query, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from contextlib import asynccontextmanager
import pandas as pd
import numpy as np
import redis.asyncio as redis
import json
import httpx
from asyncio import Semaphore

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6381))
FINANCIAL_DATA_API_HOST = os.getenv('FINANCIAL_DATA_API_HOST', 'localhost')
FINANCIAL_DATA_API_PORT = int(os.getenv('FINANCIAL_DATA_API_PORT', 8090))
FINANCIAL_DATA_API_URL = f"http://{FINANCIAL_DATA_API_HOST}:{FINANCIAL_DATA_API_PORT}"
PORT = int(os.getenv('PORT', 8026))

# Cache configuration
CACHE_TTL = 300  # 5 minutes cache for market data
HISTORICAL_CACHE_TTL = 3600  # 1 hour for historical data
BACKUP_CACHE_TTL = 86400  # 24 hours backup cache
CACHE_WARMING_INTERVAL = 240  # 4 minutes - refresh before 5 min expiry

# Rate limiting configuration
IBKR_RATE_LIMIT = 10  # Maximum concurrent requests to IBKR via financial-data-api
BATCH_DELAY = 0.5  # Delay between batches in seconds
MAX_RETRIES = 3  # Maximum retries for failed requests

# Company name mappings for better identification
COMPANY_NAMES = {
    # Japanese companies
    "8058.T": "Mitsubishi Corporation",
    "8031.T": "Mitsui & Co",
    "8001.T": "Itochu Corporation",
    "8002.T": "Marubeni Corporation",
    "8053.T": "Sumitomo Corporation",
    "7203.T": "Toyota Motor Corporation",
    "6758.T": "Sony Group Corporation",
    "9984.T": "SoftBank Group Corp",
    "6501.T": "Hitachi Ltd",
    "8766.T": "Tokio Marine Holdings",
    "8411.T": "Mizuho Financial Group",
    "8316.T": "Sumitomo Mitsui Financial",
    "8309.T": "Sumitomo Mitsui Trust",
    "8306.T": "Mitsubishi UFJ Financial",
    "8604.T": "Nomura Holdings",
    "6701.T": "NEC Corporation",
    "6702.T": "Fujitsu Limited",
    "6752.T": "Panasonic Holdings",
    "6502.T": "Toshiba Corporation",
    "9437.T": "NTT DoCoMo",
    "6504.T": "Fuji Electric",
    "6503.T": "Mitsubishi Electric",
    "4543.T": "Terumo Corporation",
    "6703.T": "OKI Electric Industry",
    "6704.T": "Iwatsu Electric",
    "8697.T": "Japan Exchange Group",
    "8750.T": "Daiwa Securities Group",
    "8698.T": "Monex Group",
    "8699.T": "澤田ホールディングス",
    "8795.T": "T&D Holdings",
    "8801.T": "Mitsui Fudosan",
    "8802.T": "Mitsubishi Estate",
    "8830.T": "Sumitomo Realty",
    "8804.T": "Tokyo Tatemono",
    "8803.T": "Heiwa Real Estate",
    "3658.T": "イーブックイニシアティブジャパン",
    "3659.T": "ネクソン",
    # Brazilian companies
    "VALE3": "Vale S.A.",
    "GGBR4": "Gerdau S.A.",
    "SUZB3": "Suzano S.A.",
    "PETR4": "Petróleo Brasileiro S.A.",
    "PRIO3": "PetroRio S.A.",
    "CMIG4": "Cia Energética de Minas Gerais",
    "ELET3": "Centrais Elétricas Brasileiras",
    "CPFE3": "CPFL Energia S.A.",
    "TAEE11": "Transmissora Aliança",
    "COCE5": "Coelce - Cia Energética do Ceará",
    "KLBN11": "Klabin S.A.",
    "FIBR3": "FibraBrasil",
    "CCRO3": "CCR S.A.",
    "TRPL4": "Cteep - Cia Transmissão Energia Elétrica Paulista",
    "EGIE3": "Engie Brasil Energia S.A.",
    "NEOE3": "Neoenergia S.A.",
    "CPLE6": "Copel",
    "EQTL3": "Equatorial Energia S.A.",
    "RENT3": "Localiza Rent a Car S.A.",
    "LREN3": "Lojas Renner S.A.",
    # US companies  
    "OXY": "Occidental Petroleum Corporation",
    "SOC": "Southern Company",
    "EWBC": "East West Bancorp Inc",
    "HCC": "Warrior Met Coal Inc",
    "AMR": "Alpha Metallurgical Resources",
    "ARCH": "Arch Resources Inc",
    "BTU": "Peabody Energy Corporation",
    "CEIX": "CONSOL Energy Inc",
    "ARLP": "Alliance Resource Partners",
    "METC": "Ramaco Resources Inc",
    "CTRA": "Coterra Energy Inc",
    "MRO": "Marathon Oil Corporation",
    "DVN": "Devon Energy Corporation",
    "FANG": "Diamondback Energy Inc",
    "EOG": "EOG Resources Inc",
    "PXD": "Pioneer Natural Resources",
    "COP": "ConocoPhillips",
    "XOM": "Exxon Mobil Corporation"
}

# Stock lists by country - Complete list from gold_dashboard.py
JP_STOCKS = [
    "8058.T",  # Mitsubishi Corp
    "8031.T",  # Mitsui & Co
    "8001.T",  # Itochu Corp
    "8002.T",  # Marubeni Corp
    "8053.T",  # Sumitomo Corp
    "8015.T",  # Toyota Tsusho
    "7012.T",  # Kawasaki Heavy Industries
    "9501.T",  # Tokyo Electric Power
    "7936.T",  # Asics Corp
    "6645.T",  # Omron Corp
    "7453.T",  # Ryohin Keikaku (MUJI)
]

BR_STOCKS = [
    "VALE3",  # Vale
    "GGBR4",  # Gerdau
    "SUZB3",  # Suzano
    "PETR4",  # Petrobras
    "PRIO3",  # PetroRio
    "JBSS3",  # JBS
    "SLCE3",  # SLC Agricola
    "SMTO3",  # São Martinho
    "KEPL3",  # Klabin
    "RAIL3",  # Rumo
    "TUPY3",  # Tupy
    "AGRO3",  # BrasilAgro
    "SOJA3",  # Boa Safra
    "EMBR3",  # Embraer
    "BRAV3",  # BRF Aviacao
    "BBDC4",  # Bradesco
    "MRFG3",  # Marfrig
    "BRFS3",  # BRF SA
]

US_STOCKS = [
    "OXY",    # Occidental Petroleum
    "SOC",    # Southern Company
    "EWBC",   # East West Bancorp
    "HCC",    # Warrior Met Coal
    "AMR",    # Alpha Metallurgical Resources
    "DPZ",    # Domino's Pizza
    "ALMTF",  # Alamos Gold
    "NXE",    # NexGen Energy
    "CCJ",    # Cameco
    "SRUUF",  # Sprott Physical Uranium Trust
    "URA",    # Global X Uranium ETF
    "ANGPY",  # Anglo American
    "LULU",   # Lululemon
    "BN",     # Brookfield Corp
    "PM",     # Philip Morris
    "QSR",    # Restaurant Brands
    "COST",   # Costco
    "FLUT",   # Flutter Entertainment
    "LAD",    # Lithia Motors
    "NKE",    # Nike
    "AXP",    # American Express
    "POOL",   # Pool Corp
    "NVDA",   # NVIDIA
    "BABA",   # Alibaba
    "JD",     # JD.com
    "NIO",    # NIO Inc
    "PDD",    # PDD Holdings
    "BIDU",   # Baidu
    "TCEHY",  # Tencent ADR
]

# Global variables
redis_client: Optional[redis.Redis] = None
http_client: Optional[httpx.AsyncClient] = None
rate_limiter: Optional[Semaphore] = None
background_task: Optional[asyncio.Task] = None
cache_update_running = False

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.symbol_subscriptions: Dict[WebSocket, List[str]] = {}

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.symbol_subscriptions[websocket] = []
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        if websocket in self.symbol_subscriptions:
            del self.symbol_subscriptions[websocket]
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def subscribe(self, websocket: WebSocket, symbols: List[str]):
        self.symbol_subscriptions[websocket] = symbols
        logger.info(f"WebSocket subscribed to symbols: {symbols}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

    async def broadcast_symbol_update(self, symbol: str, data: Dict):
        """Send updates only to connections subscribed to this symbol"""
        message = json.dumps({
            "type": "price_update",
            "symbol": symbol,
            "data": data,
            "timestamp": datetime.now().isoformat()
        })
        
        for connection, symbols in self.symbol_subscriptions.items():
            if symbol in symbols or "*" in symbols:  # "*" means subscribe to all
                try:
                    await connection.send_text(message)
                except:
                    pass

manager = ConnectionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle"""
    global redis_client, http_client, rate_limiter, background_task, cache_update_running
    
    # Connect to Redis
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        decode_responses=True
    )
    
    # Create HTTP client for financial-data-api
    http_client = httpx.AsyncClient(
        base_url=FINANCIAL_DATA_API_URL,
        timeout=30.0
    )
    
    # Initialize rate limiter
    rate_limiter = Semaphore(IBKR_RATE_LIMIT)
    
    logger.info(f"Connected to Financial Data API at {FINANCIAL_DATA_API_URL}")
    logger.info(f"Rate limiting configured: {IBKR_RATE_LIMIT} concurrent requests")
    
    # Load cached data on startup
    await warm_cache_on_startup()
    
    # Start background cache update worker
    cache_update_running = True
    background_task = asyncio.create_task(update_cache_worker())
    logger.info("Started background cache update worker")
    
    yield
    
    # Stop background worker
    cache_update_running = False
    if background_task:
        background_task.cancel()
        try:
            await background_task
        except asyncio.CancelledError:
            logger.info("Background cache update worker stopped")
    
    # Cleanup
    if http_client:
        await http_client.aclose()
    if redis_client:
        await redis_client.close()

app = FastAPI(title="Market Tools API", lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def calculate_currency_impact(symbol: str, country_code: str, local_price: float, usd_price: float, gold_price: float, current_fx_rates: Dict[str, float]) -> Dict:
    """Calculate real currency impact analysis using historical FX data"""
    if country_code == 'US':
        # US stocks have no FX impact (already in USD)
        return {
            "fxExposure": "USD",
            "currentFxRate": 1.0,
            "fxImpact1D": 0.0,
            "fxImpact1W": 0.0,
            "fxImpact1M": 0.0,
            "localCurrencyReturn": 0.0,
            "fxAdjustedReturn": 0.0,
            "note": "No FX exposure (USD-denominated)"
        }
    
    # Determine FX pair and current rate
    if country_code == 'JP':
        fx_pair = "USDJPY"
        current_fx_rate = current_fx_rates.get("USDJPY", 1.0)
    elif country_code == 'BR':
        fx_pair = "USDBRL"
        current_fx_rate = current_fx_rates.get("USDBRL", 1.0)
    else:
        return {
            "fxExposure": "Unknown",
            "currentFxRate": 1.0,
            "fxImpact1D": 0.0,
            "fxImpact1W": 0.0,
            "fxImpact1M": 0.0,
            "note": "Unsupported currency"
        }
    
    try:
        # Get historical FX data for impact calculation
        historical_fx = await get_historical_fx_data(fx_pair)
        
        if historical_fx.empty:
            return {
                "fxExposure": fx_pair,
                "currentFxRate": current_fx_rate,
                "fxImpact1D": 0.0,
                "fxImpact1W": 0.0,
                "fxImpact1M": 0.0,
                "note": "Historical FX data unavailable"
            }
        
        # Calculate FX impact over different periods
        fx_impacts = {}
        periods = {"1D": 1, "1W": 7, "1M": 30}
        
        for period_name, days_back in periods.items():
            if len(historical_fx) > days_back:
                historical_rate = historical_fx.iloc[-days_back]["close"]
                fx_change = (current_fx_rate - historical_rate) / historical_rate
                
                # FX impact on USD returns (negative because we're converting TO USD)
                fx_impact = -fx_change * 100  # Convert to percentage
                fx_impacts[f"fxImpact{period_name}"] = round(fx_impact, 2)
            else:
                fx_impacts[f"fxImpact{period_name}"] = 0.0
        
        return {
            "fxExposure": fx_pair,
            "currentFxRate": round(current_fx_rate, 4),
            **fx_impacts,
            "note": "Live FX impact calculation"
        }
        
    except Exception as e:
        logger.warning(f"Error calculating currency impact for {symbol}: {e}")
        return {
            "fxExposure": fx_pair,
            "currentFxRate": current_fx_rate,
            "fxImpact1D": 0.0,
            "fxImpact1W": 0.0,
            "fxImpact1M": 0.0,
            "note": "Currency impact calculation failed"
        }

async def get_historical_fx_data(fx_pair: str) -> pd.DataFrame:
    """Get historical FX data for currency impact analysis"""
    cache_key = f"fx_historical:{fx_pair}"
    
    # Check cache first
    cached = await redis_client.get(cache_key)
    if cached:
        df = pd.read_json(cached, orient='split')
        df.index = pd.to_datetime(df.index)
        return df
    
    # Get historical data from financial-data-api
    async with rate_limiter:
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=60)  # Get 2 months for good coverage
            
            response = await http_client.post(
                "/api/financial-data/historical",
                json={
                    "symbol": fx_pair,
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d"),
                    "interval": "1d",
                    "use_cache": True
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("data"):
                    df = pd.DataFrame(data["data"])
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                    
                    # Cache for 4 hours (FX changes frequently)
                    await redis_client.setex(cache_key, 14400, df.to_json(orient='split'))
                    return df
            else:
                logger.warning(f"Failed to get historical FX data for {fx_pair}: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error getting historical FX data for {fx_pair}: {e}")
    
    return pd.DataFrame()

def calculate_metrics(hist_df: pd.DataFrame, current_price: float) -> Dict:
    """Calculate distance percentages and historical ranges for ALL time windows"""
    # Define time windows (trading days)
    windows = {
        '1w': 5,    # 5 trading days
        '1m': 21,   # ~1 month
        '3m': 63,   # ~3 months
        '6m': 126,  # ~6 months
        '1y': 252,  # ~1 year
        '2y': 504,  # ~2 years
        '3y': 756,  # ~3 years
        '5y': 1260  # ~5 years
    }
    
    distance_percentages = {}
    historical_ranges = {}
    
    # Calculate for ALL windows, ensuring each period uses distinct time ranges
    for window_name, days in windows.items():
        if hist_df.empty:
            # No data available
            distance_percentages[window_name] = None
            historical_ranges[window_name] = {"low": None, "high": None}
        elif len(hist_df) >= days:
            # We have enough data for the full window - use exact window
            window_data = hist_df.tail(days)
            low = float(window_data['low'].min())
            high = float(window_data['high'].max())
            
            # Calculate distance percentage
            if high > low:
                distance_pct = ((current_price - low) / (high - low)) * 100
                distance_percentages[window_name] = round(distance_pct, 1)
            else:
                distance_percentages[window_name] = 50.0
            
            historical_ranges[window_name] = {
                "low": round(low, 4),
                "high": round(high, 4)
            }
        else:
            # Not enough data for full window - skip this time period entirely
            # This ensures we don't show misleading identical values across periods
            distance_percentages[window_name] = None
            historical_ranges[window_name] = {"low": None, "high": None}
    
    return {
        "distancePercentages": distance_percentages,
        "historicalRanges": historical_ranges
    }

async def get_market_data(symbol: str) -> Optional[Dict]:
    """Get current market data from financial-data-api with rate limiting"""
    cache_key = f"market:{symbol}"
    
    # Check cache first
    cached = await redis_client.get(cache_key)
    if cached:
        return json.loads(cached)
    
    # Apply rate limiting
    async with rate_limiter:
        try:
            # Call financial-data-api
            response = await http_client.get(
                f"/api/financial-data/stock/{symbol}",
                params={"use_cache": True}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("price"):
                    # Extract price data
                    price_info = data["price"]
                    market_data = {
                        "symbol": symbol,
                        "price": price_info.get("price"),
                        "bid": price_info.get("bid"),
                        "ask": price_info.get("ask"),
                        "high": price_info.get("high"),
                        "low": price_info.get("low"),
                        "volume": price_info.get("volume"),
                        "timestamp": price_info.get("timestamp", datetime.now().isoformat())
                    }
                    
                    # Cache the data
                    await redis_client.setex(cache_key, CACHE_TTL, json.dumps(market_data))
                    return market_data
            else:
                logger.error(f"Failed to get market data for {symbol}: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
        
    return None

async def get_historical_data(symbol: str, duration: str = "1 Y") -> pd.DataFrame:
    """Get historical data from financial-data-api with rate limiting"""
    cache_key = f"historical:{symbol}:{duration}"
    
    # Check cache
    cached = await redis_client.get(cache_key)
    if cached:
        df = pd.read_json(cached, orient='split')
        df.index = pd.to_datetime(df.index)
        return df
    
    # Apply rate limiting
    async with rate_limiter:
        try:
            # Convert duration to date range
            end_date = datetime.now()
            if duration == "1 W":
                start_date = end_date - timedelta(days=7)
            elif duration == "1 M":
                start_date = end_date - timedelta(days=30)
            elif duration == "3 M":
                start_date = end_date - timedelta(days=90)
            elif duration == "6 M":
                start_date = end_date - timedelta(days=180)
            elif duration == "1 Y":
                start_date = end_date - timedelta(days=365)
            elif duration == "5 Y":
                start_date = end_date - timedelta(days=1825)
            else:
                start_date = end_date - timedelta(days=365)
            
            # Call financial-data-api
            response = await http_client.post(
                "/api/financial-data/historical",
                json={
                    "symbol": symbol,
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d"),
                    "interval": "1d",
                    "use_cache": True
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("data"):
                    # Convert to DataFrame
                    df = pd.DataFrame(data["data"])
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                    
                    # Cache the data
                    await redis_client.setex(
                        cache_key,
                        HISTORICAL_CACHE_TTL,
                        df.to_json(orient='split')
                    )
                    return df
            else:
                logger.error(f"Failed to get historical data for {symbol}: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
    
    return pd.DataFrame()

async def set_cache_with_backup(key: str, value: Any, ttl: int = CACHE_TTL):
    """Set cache with primary TTL and backup TTL"""
    json_value = json.dumps(value)
    # Set primary cache
    await redis_client.setex(key, ttl, json_value)
    # Set backup cache with longer TTL
    backup_key = f"{key}:backup"
    await redis_client.setex(backup_key, BACKUP_CACHE_TTL, json_value)

async def get_cache_with_fallback(key: str) -> Optional[Any]:
    """Get cache with fallback to backup if primary expired"""
    # Try primary cache
    value = await redis_client.get(key)
    if value:
        return json.loads(value)
    
    # Try backup cache
    backup_key = f"{key}:backup"
    backup_value = await redis_client.get(backup_key)
    if backup_value:
        logger.info(f"Using backup cache for {key}")
        # Restore to primary cache with short TTL
        await redis_client.setex(key, 60, backup_value)
        return json.loads(backup_value)
    
    return None

async def warm_cache_on_startup():
    """Warm up cache on startup with last known good data"""
    try:
        # Check if we have backup cache
        cached_data = await get_cache_with_fallback("gold_analysis_all")
        if cached_data:
            logger.info(f"Restored {len(cached_data.get('data', []))} stocks from backup cache")
    except Exception as e:
        logger.error(f"Error warming cache: {e}")

async def update_cache_worker():
    """Background worker to periodically update cache"""
    global cache_update_running
    
    while cache_update_running:
        try:
            logger.info("Background cache update starting...")
            
            # Update FX rates cache
            try:
                fx_rates = await get_fx_rates()
                logger.info(f"Updated FX rates cache: {fx_rates}")
            except Exception as e:
                logger.error(f"Failed to update FX rates cache: {e}")
            
            # Update gold analysis cache for all stocks
            try:
                # Get current FX rates and gold price
                fx_rates = await get_fx_rates()
                gold_price = fx_rates.get("XAUUSD", 0)
                
                if gold_price > 0:
                    # Process stocks in smaller batches to avoid overwhelming the API
                    all_stocks = []
                    
                    for batch_name, stocks, country in [
                        ("JP", JP_STOCKS, "JP"),  # Full stock lists
                        ("BR", BR_STOCKS, "BR"),
                        ("US", US_STOCKS, "US")
                    ]:
                        batch_results = []
                        for symbol in stocks:
                            try:
                                stock_data = await process_stock(
                                    symbol, 
                                    country, 
                                    fx_rates, 
                                    gold_price,
                                    skip_historical=False,
                                    historical_period="1 W"  # Use 1 week for cache updates
                                )
                                if stock_data:
                                    batch_results.append(stock_data)
                                    logger.info(f"Updated cache for {symbol}")
                                    
                                    # Broadcast update via WebSocket
                                    await manager.broadcast_symbol_update(symbol, stock_data)
                                
                                # Small delay between stocks
                                await asyncio.sleep(0.1)
                                
                            except Exception as e:
                                logger.error(f"Failed to update cache for {symbol}: {e}")
                        
                        all_stocks.extend(batch_results)
                        
                        # Delay between country batches
                        await asyncio.sleep(1)
                    
                    # Update the main cache
                    if all_stocks:
                        cache_data = {
                            "data": all_stocks,
                            "metadata": {
                                "totalStocks": len(all_stocks),
                                "lastUpdated": datetime.now().isoformat(),
                                "goldPrice": gold_price,
                                "fxRates": fx_rates
                            }
                        }
                        
                        await set_cache_with_backup("gold_analysis_all", cache_data, CACHE_TTL)
                        logger.info(f"Updated main cache with {len(all_stocks)} stocks")
                    
            except Exception as e:
                logger.error(f"Failed to update gold analysis cache: {e}")
            
            # Update commodity/FX cache
            try:
                commodity_data = await get_fx_commodity_data()
                if commodity_data:
                    logger.info(f"Updated commodity cache with {len(commodity_data)} items")
            except Exception as e:
                logger.error(f"Failed to update commodity cache: {e}")
            
            logger.info("Background cache update completed")
            
            # Wait before next update (4 minutes - before cache expires)
            await asyncio.sleep(CACHE_WARMING_INTERVAL)
            
        except Exception as e:
            logger.error(f"Error in cache update worker: {e}")
            # Wait a bit before retrying on error
            await asyncio.sleep(60)

async def get_fx_rates() -> Dict[str, float]:
    """Get current FX rates from financial-data-api with rate limiting"""
    # Check cache first
    cache_key = "fx_rates_all"
    cached = await get_cache_with_fallback(cache_key)
    if cached:
        logger.info("Using cached FX rates for quick response")
        return cached
    
    rates = {}
    
    # Get FX rates and gold price in parallel
    fx_symbols = ["USDJPY", "USDBRL", "XAUUSD"]
    
    # Apply rate limiting for batch request
    async with rate_limiter:
        try:
            # Use batch endpoint for efficiency
            response = await http_client.post(
                "/api/financial-data/batch",
                json={
                    "symbols": fx_symbols,
                    "data_types": ["price"],
                    "use_cache": True
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                prices = data.get("data", {}).get("prices", {})
                
                for symbol in fx_symbols:
                    if prices.get(symbol) and prices[symbol].get("price"):
                        rates[symbol] = prices[symbol]["price"]
                        logger.info(f"Got {symbol} rate: {prices[symbol]['price']}")
            else:
                logger.error(f"Failed to get FX rates: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error getting FX rates: {e}")
    
    # Cache the rates if we got any
    if rates:
        await set_cache_with_backup(cache_key, rates, CACHE_TTL)
    
    return rates

async def get_fx_commodity_data() -> List[Dict]:
    """Get FX rates and commodity prices for cache update"""
    cache_key = "fx_commodity_all"
    
    try:
        # Get current FX rates
        fx_rates = await get_fx_rates()
        
        # Build commodity data list
        data = []
        
        if 'XAUUSD' in fx_rates:
            data.append({
                "symbol": "XAUUSD",
                "name": "Gold Spot",
                "price": fx_rates['XAUUSD'],
                "type": "commodity"
            })
        
        if 'USDJPY' in fx_rates:
            data.append({
                "symbol": "USDJPY", 
                "name": "USD/JPY",
                "price": fx_rates['USDJPY'],
                "type": "fx"
            })
            
        if 'USDBRL' in fx_rates:
            data.append({
                "symbol": "USDBRL",
                "name": "USD/BRL", 
                "price": fx_rates['USDBRL'],
                "type": "fx"
            })
        
        # Cache the commodity data
        if data:
            await set_cache_with_backup(cache_key, data, CACHE_TTL)
        
        return data
        
    except Exception as e:
        logger.error(f"Error getting FX/commodity data: {e}")
        return []

# Define async function to process a single stock
async def process_stock(symbol: str, country_code: str, fx_rates: Dict[str, float], gold_price: float, skip_historical: bool = False, historical_period: str = "5 Y") -> Optional[Dict]:
    try:
        # Try to get current market data first
        market_data = await get_market_data(symbol)
        local_price = None
        last_updated = datetime.now().isoformat()
        
        # If no current market data (market closed), get from historical data
        if not market_data or not market_data.get('price'):
            hist_df = await get_historical_data(symbol, "5 D")  # Get last 5 days
            if not hist_df.empty:
                # Get the most recent close price
                local_price = float(hist_df.iloc[-1]['close'])
                last_updated = hist_df.index[-1].isoformat()
                logger.info(f"Using last trading day price for {symbol}: {local_price}")
            else:
                logger.warning(f"No data available for {symbol}")
                return None
        else:
            local_price = market_data['price']
            last_updated = market_data['timestamp']
        
        # Convert to USD if needed
        usd_price = local_price
        
        if country_code == 'JP' and 'USDJPY' in fx_rates:
            usd_price = local_price / fx_rates['USDJPY']
        elif country_code == 'BR' and 'USDBRL' in fx_rates:
            usd_price = local_price / fx_rates['USDBRL']
        
        # Calculate gold-denominated price
        gold_denominated_price = usd_price / gold_price
        
        # Calculate currency impact analysis
        currency_impact = await calculate_currency_impact(
            symbol, country_code, local_price, usd_price, gold_price, fx_rates
        )
        
        # Skip historical data for quick response if requested
        if skip_historical:
            metrics = {
                "distancePercentages": {},
                "historicalRanges": {}
            }
        else:
            # Get historical data based on requested period
            hist_df = await get_historical_data(symbol, historical_period)  # Get data for requested period
            if hist_df.empty:
                return None
            
            # Calculate metrics with limited data
            metrics = calculate_metrics(hist_df, local_price)
        
        # Check data quality - NOVAL indicators
        data_quality = {
            "isStale": False,
            "hasAllWindows": len(metrics["distancePercentages"]) >= 1 if not skip_historical else False,
            "dataAge": None
        }
        
        # Check if data is stale (consider weekends)
        try:
            update_time = pd.to_datetime(last_updated)
            current_time = pd.Timestamp.now(tz=update_time.tz)
            time_diff = current_time - update_time
            data_age_hours = time_diff.total_seconds() / 3600
            data_quality["dataAge"] = data_age_hours
            
            # Data is only stale if it's older than last trading day
            weekday = current_time.weekday()
            if weekday >= 5:  # Saturday or Sunday
                days_since_friday = weekday - 4
                hours_since_friday = days_since_friday * 24
                data_quality["isStale"] = data_age_hours > (hours_since_friday + 24)
            else:
                data_quality["isStale"] = data_age_hours > 24
        except Exception:
            pass
        
        stock_data = {
            "symbol": symbol,
            "name": COMPANY_NAMES.get(symbol, symbol),  # Use proper company name if available
            "country": country_code,
            "price": round(local_price, 2),
            "goldPrice": round(gold_price, 2),
            "goldDenominatedPrice": round(gold_denominated_price, 6),
            "currencyImpact": currency_impact,
            "distancePercentages": metrics["distancePercentages"],
            "historicalRanges": metrics["historicalRanges"],
            "lastUpdated": last_updated,
            "dataQuality": data_quality
        }
        
        # Add fundamentals data (market cap and FCF)
        stock_data = await enrich_stock_with_fundamentals(stock_data, symbol)
        return stock_data
        
    except Exception as e:
        logger.error(f"Error processing {symbol}: {e}")
        return None

async def get_fundamentals_data(symbol: str) -> Dict[str, Optional[float]]:
    """Get market cap and free cash flow from financial-data-api with rate limiting"""
    cache_key = f"fundamentals:{symbol}"
    
    # Check cache first (fundamentals change less frequently)
    cached = await redis_client.get(cache_key)
    if cached:
        return json.loads(cached)
    
    # Apply rate limiting
    async with rate_limiter:
        try:
            # Request fundamentals data from financial-data-api
            logger.info(f"Requesting fundamentals for {symbol}")
            
            # Use batch endpoint with fundamentals data type
            response = await http_client.post(
                "/api/financial-data/batch",
                json={
                    "symbols": [symbol],
                    "data_types": ["price", "fundamentals"],
                    "use_cache": True
                }
            )
            
            market_cap = None
            free_cash_flow = None
            
            if response.status_code == 200:
                data = response.json()
                fundamentals_data = data.get("data", {}).get("fundamentals", {})
                
                if fundamentals_data.get(symbol):
                    fundamentals = fundamentals_data[symbol]
                    market_cap = fundamentals.get("market_cap")
                    free_cash_flow = fundamentals.get("free_cash_flow")
                    
                    # If still no data, try alternative fields
                    if market_cap is None and fundamentals.get("marketCap"):
                        market_cap = fundamentals.get("marketCap")
                    if free_cash_flow is None and fundamentals.get("freeCashflow"):
                        free_cash_flow = fundamentals.get("freeCashflow")
                    
                    logger.info(f"Got fundamentals for {symbol}: Market Cap={market_cap}, FCF={free_cash_flow}")
            else:
                logger.warning(f"Failed to get fundamentals for {symbol}: {response.status_code}")
            
            result = {
                "market_cap": market_cap,
                "free_cash_flow": free_cash_flow
            }
            
            # Cache for 1 hour (fundamentals don't change frequently)
            if market_cap is not None or free_cash_flow is not None:
                await redis_client.setex(cache_key, 3600, json.dumps(result))
            
            return result
            
        except Exception as e:
            logger.warning(f"Error getting fundamentals for {symbol}: {e}")
            # Return empty data instead of failing
            return {"market_cap": None, "free_cash_flow": None}


async def enrich_stock_with_fundamentals(stock_data: Dict, symbol: str) -> Dict:
    """Add market cap and FCF data to stock analysis"""
    try:
        # Get fundamentals from financial-data-api
        fundamentals = await get_fundamentals_data(symbol)
        
        stock_data["marketCap"] = fundamentals.get("market_cap")
        stock_data["freeCashFlow"] = fundamentals.get("free_cash_flow")
        
        # Provide appropriate note based on data availability
        if stock_data["marketCap"] is None and stock_data["freeCashFlow"] is None:
            # Check if it's a known limitation or general failure
            if symbol.endswith('.T'):  # Japanese stock
                stock_data["fundamentalsNote"] = "Fundamental data unavailable for Japanese stocks via current providers"
            elif symbol in ['VALE3', 'GGBR4']:  # Brazilian stocks
                stock_data["fundamentalsNote"] = "Fundamental data unavailable for Brazilian stocks via current providers"
            else:  # US stocks
                stock_data["fundamentalsNote"] = "Fundamental data temporarily unavailable (rate limits or subscription required)"
        elif stock_data["marketCap"] is not None:
            # We have some data
            stock_data["fundamentalsNote"] = "Fundamental data provided"
        
    except Exception as e:
        logger.warning(f"Failed to get fundamentals for {symbol}: {e}")
        stock_data["marketCap"] = None
        stock_data["freeCashFlow"] = None
        stock_data["fundamentalsNote"] = "Unable to retrieve fundamental data"
    
    return stock_data

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check financial-data-api health
        financial_api_healthy = False
        if http_client:
            response = await http_client.get("/health")
            financial_api_healthy = response.status_code == 200
    except Exception:
        financial_api_healthy = False
    
    return {
        "status": "healthy",
        "service": "market-tools-api",
        "financial_data_api_connected": financial_api_healthy,
        "redis_connected": await redis_client.ping() if redis_client else False
    }

@app.get("/api/market-tools/fx-rates-debug")
async def get_fx_rates_debug():
    """Debug endpoint to check FX rates"""
    rates = await get_fx_rates()
    return {
        "rates": rates,
        "timestamp": datetime.now().isoformat(),
        "has_gold": "XAUUSD" in rates,
        "gold_price": rates.get("XAUUSD", None)
    }

@app.get("/api/market-tools/gold-analysis-quick")
async def get_gold_analysis_quick(
    limit: int = Query(10, description="Number of stocks to return"),
    with_minimal_history: bool = Query(False, description="Include minimal historical data (1W)")
):
    """Get quick gold analysis with limited stocks for fast response"""
    # Try to return cached data first
    cached_data = await get_cache_with_fallback("gold_analysis_all")
    if cached_data:
        # Return limited subset
        cached_data["data"] = cached_data["data"][:limit]
        cached_data["metadata"]["limited"] = True
        return cached_data
    
    # Get FX rates
    fx_rates = await get_fx_rates()
    if 'XAUUSD' not in fx_rates:
        raise HTTPException(status_code=503, detail="Gold price not available")
    
    gold_price = fx_rates['XAUUSD']
    
    # Get a subset of stocks for quick response
    quick_stocks = [
        ("8058.T", "JP"),  # Mitsubishi
        ("VALE3", "BR"),  # Vale
        ("COST", "US"),  # Costco
        ("NVDA", "US"),  # NVIDIA
        ("8031.T", "JP"),  # Mitsui
    ][:limit]
    
    results = []
    
    # Process stocks in parallel for quick response
    if with_minimal_history:
        # Include 1 week of historical data for basic distance percentages
        tasks = [
            process_stock(symbol, country_code, fx_rates, gold_price, skip_historical=False, historical_period="1 W")
            for symbol, country_code in quick_stocks
        ]
    else:
        # Skip historical data entirely for fastest response
        tasks = [
            process_stock(symbol, country_code, fx_rates, gold_price, skip_historical=True)
            for symbol, country_code in quick_stocks
        ]
    
    batch_results = await asyncio.gather(*tasks)
    
    # Filter out None results
    results = [r for r in batch_results if r is not None]
    
    response = {
        "data": results,
        "metadata": {
            "total": len(results),
            "goldPrice": round(gold_price, 2),
            "fxRates": fx_rates,
            "lastUpdated": datetime.now().isoformat(),
            "source": "financial-data-api-quick",
            "limited": True,
            "historicalPeriod": "1 W" if with_minimal_history else "none"
        }
    }
    
    return response

@app.get("/api/market-tools/gold-analysis-cached")
async def get_gold_analysis_cached():
    """Get cached gold analysis data for quick response"""
    cache_key = "gold_analysis_all"
    
    # Try to get from cache with fallback to backup
    cached_data = await get_cache_with_fallback(cache_key)
    if cached_data:
        return cached_data
    
    # If no cache, return empty data
    return {
        "data": [],
        "metadata": {
            "total": 0,
            "lastUpdated": datetime.now().isoformat(),
            "source": "no-cache-available"
        }
    }

@app.get("/api/market-tools/gold-analysis")
async def get_gold_analysis(
    country: Optional[str] = Query(None, description="Filter by country (JP, BR, US)"),
    historical_period: str = Query("5 Y", description="Historical data period (e.g., '1 W', '1 M', '1 Y', '5 Y')")
):
    """Get gold-denominated analysis for stocks"""
    
    # Get FX rates first
    fx_rates = await get_fx_rates()
    if 'XAUUSD' not in fx_rates:
        raise HTTPException(status_code=503, detail="Gold price not available")
    
    gold_price = fx_rates['XAUUSD']
    
    # Determine which stocks to analyze
    stocks_to_analyze = []
    if country == 'JP':
        stocks_to_analyze = [(s, 'JP') for s in JP_STOCKS]
    elif country == 'BR':
        stocks_to_analyze = [(s, 'BR') for s in BR_STOCKS]
    elif country == 'US':
        stocks_to_analyze = [(s, 'US') for s in US_STOCKS]
    elif country is None:
        # Get all stocks
        stocks_to_analyze = (
            [(s, 'JP') for s in JP_STOCKS] +
            [(s, 'BR') for s in BR_STOCKS] +
            [(s, 'US') for s in US_STOCKS]
        )
    
    results = []
    
    # Process stocks in parallel batches
    batch_size = 10  # Process 10 stocks concurrently
    for i in range(0, len(stocks_to_analyze), batch_size):
        batch = stocks_to_analyze[i:i + batch_size]
        
        # Process batch in parallel
        tasks = [
            process_stock(symbol, country_code, fx_rates, gold_price, historical_period=historical_period)
            for symbol, country_code in batch
        ]
        
        batch_results = await asyncio.gather(*tasks)
        
        # Filter out None results and add to results list
        results.extend([r for r in batch_results if r is not None])
        
        # Log progress
        logger.info(f"Processed {len(results)}/{len(stocks_to_analyze)} stocks")
        
        # Add small delay between batches to avoid overwhelming the API
        if i + batch_size < len(stocks_to_analyze):
            await asyncio.sleep(BATCH_DELAY)  # Use configured batch delay
    
    # Sort by gold-denominated price
    results.sort(key=lambda x: x['goldDenominatedPrice'], reverse=True)
    
    response = {
        "data": results,
        "metadata": {
            "total": len(results),
            "goldPrice": round(gold_price, 2),
            "fxRates": fx_rates,
            "lastUpdated": datetime.now().isoformat(),
            "source": "financial-data-api"
        }
    }
    
    # Cache the results with backup for quick access
    cache_key = "gold_analysis_all"
    await set_cache_with_backup(cache_key, response, CACHE_TTL)
    
    return response

@app.get("/api/market-tools/fx-commodities")
async def get_fx_commodities():
    """Get FX rates and commodity prices"""
    try:
        # Get current FX rates
        fx_rates = await get_fx_rates()
        
        # Only return data we actually have
        data = []
        
        if 'XAUUSD' in fx_rates:
            data.append({
                "symbol": "XAUUSD",
                "name": "Gold Spot",
                "price": fx_rates['XAUUSD'],
                "type": "commodity"
            })
        
        if 'USDJPY' in fx_rates:
            data.append({
                "symbol": "USDJPY", 
                "name": "USD/JPY",
                "price": fx_rates['USDJPY'],
                "type": "fx"
            })
            
        if 'USDBRL' in fx_rates:
            data.append({
                "symbol": "USDBRL",
                "name": "USD/BRL", 
                "price": fx_rates['USDBRL'],
                "type": "fx"
            })
        
        return {
            "data": data,
            "metadata": {
                "total": len(data),
                "lastUpdated": datetime.now().isoformat(),
                "source": "financial-data-api"
            }
        }
    except Exception as e:
        logger.error(f"Error getting FX/commodity data: {e}")
        return {
            "data": [],
            "metadata": {
                "total": 0,
                "lastUpdated": datetime.now().isoformat(),
                "source": "error",
                "error": str(e)
            }
        }

@app.get("/api/market-tools/symbols")
async def get_symbols():
    """Get configured symbols"""
    symbols = []
    
    # Add Japanese stocks
    for symbol in JP_STOCKS:
        symbols.append({
            "symbol": symbol,
            "name": COMPANY_NAMES.get(symbol, symbol),
            "country": "JP",
            "sector": "Unknown",
            "active": True
        })
    
    # Add Brazilian stocks
    for symbol in BR_STOCKS:
        symbols.append({
            "symbol": symbol,
            "name": COMPANY_NAMES.get(symbol, symbol),
            "country": "BR",
            "sector": "Unknown",
            "active": True
        })
    
    # Add US stocks
    for symbol in US_STOCKS:
        symbols.append({
            "symbol": symbol,
            "name": COMPANY_NAMES.get(symbol, symbol),
            "country": "US",
            "sector": "Unknown",
            "active": True
        })
    
    return {
        "symbols": symbols,
        "metadata": {
            "total": len(symbols),
            "countries": {
                "JP": len(JP_STOCKS),
                "BR": len(BR_STOCKS),
                "US": len(US_STOCKS)
            },
            "lastUpdated": datetime.now().isoformat()
        }
    }

@app.get("/api/market-tools/financial-data-api-status")
async def get_financial_data_api_status():
    """Check financial-data-api connection status"""
    try:
        if http_client:
            response = await http_client.get("/health")
            if response.status_code == 200:
                health_data = response.json()
                return {
                    "connected": True,
                    "status": "Connected",
                    "api_url": FINANCIAL_DATA_API_URL,
                    "providers": health_data.get("providers", {}),
                    "message": "Financial Data API connection active"
                }
        
        return {
            "connected": False,
            "status": "Disconnected",
            "api_url": FINANCIAL_DATA_API_URL,
            "message": "Financial Data API connection is not active"
        }
    except Exception as e:
        logger.error(f"Error checking Financial Data API status: {e}")
        return {
            "connected": False,
            "status": "Error",
            "api_url": FINANCIAL_DATA_API_URL,
            "error": str(e),
            "message": "Failed to check Financial Data API status"
        }

@app.post("/api/market-tools/add-symbol")
async def add_symbol(request: Dict[str, str]):
    """Add a new symbol to the gold analysis list"""
    try:
        symbol = request.get("symbol", "").upper().strip()
        country = request.get("country", "").upper().strip()
        name = request.get("name", symbol).strip()
        
        if not symbol or not country:
            return {"success": False, "error": "Symbol and country are required"}
        
        if country not in ["JP", "BR", "US"]:
            return {"success": False, "error": "Country must be JP, BR, or US"}
        
        # Test if we can get market data for this symbol
        try:
            # Try to get basic market data to validate the symbol
            market_data = await get_market_data(symbol)
            if market_data is None:
                return {"success": False, "error": f"Cannot get market data for {symbol}"}
        except Exception as e:
            return {"success": False, "error": f"Unable to validate symbol: {str(e)}"}
        
        # Add to the appropriate stock list
        if country == "JP" and symbol not in JP_STOCKS:
            JP_STOCKS.append(symbol)
        elif country == "BR" and symbol not in BR_STOCKS:
            BR_STOCKS.append(symbol)
        elif country == "US" and symbol not in US_STOCKS:
            US_STOCKS.append(symbol)
        
        # Add to company names mapping
        COMPANY_NAMES[symbol] = name
        
        return {
            "success": True,
            "message": f"Symbol {symbol} ({name}) added successfully",
            "symbol": symbol,
            "country": country,
            "name": name
        }
        
    except Exception as e:
        logger.error(f"Error adding symbol: {e}")
        return {"success": False, "error": f"Failed to add symbol: {str(e)}"}

@app.delete("/api/market-tools/remove-symbol/{symbol}")
async def remove_symbol(symbol: str):
    """Remove a symbol from the gold analysis list"""
    try:
        symbol = symbol.upper().strip()
        
        # Check if symbol exists in any list and remove it
        found = False
        if symbol in JP_STOCKS:
            JP_STOCKS.remove(symbol)
            found = True
        elif symbol in BR_STOCKS:
            BR_STOCKS.remove(symbol)
            found = True
        elif symbol in US_STOCKS:
            US_STOCKS.remove(symbol)
            found = True
        
        if not found:
            return {"success": False, "error": f"Symbol {symbol} not found in any list"}
        
        # Remove from company names if exists
        if symbol in COMPANY_NAMES:
            del COMPANY_NAMES[symbol]
        
        return {
            "success": True,
            "message": f"Symbol {symbol} removed successfully"
        }
        
    except Exception as e:
        logger.error(f"Error removing symbol: {e}")
        return {"success": False, "error": f"Failed to remove symbol: {str(e)}"}

@app.get("/api/market-tools/stream/gold-analysis")
async def stream_gold_analysis():
    """Stream real-time gold analysis updates via Server-Sent Events"""
    
    async def event_generator():
        """Generate SSE events with gold analysis updates"""
        try:
            while True:
                # Get current gold analysis data
                try:
                    # Get cached data first for quick response
                    cached_data = await get_cache_with_fallback("gold_analysis_all")
                    
                    if cached_data:
                        # Send cached data immediately
                        yield f"data: {json.dumps(cached_data)}\n\n"
                        
                        # Wait before next update
                        await asyncio.sleep(30)  # Update every 30 seconds
                        
                        # Get fresh FX rates
                        fx_rates = await get_fx_rates()
                        if 'XAUUSD' not in fx_rates:
                            continue
                        
                        gold_price = fx_rates['XAUUSD']
                        
                        # Process a small batch of stocks for incremental updates
                        quick_stocks = [
                            ("COST", "US"),
                            ("NVDA", "US"),
                            ("8058.T", "JP"),
                            ("VALE3", "BR")
                        ]
                        
                        results = []
                        for symbol, country_code in quick_stocks:
                            stock_data = await process_stock(symbol, country_code, fx_rates, gold_price, skip_historical=True)
                            if stock_data:
                                results.append(stock_data)
                        
                        if results:
                            # Send incremental update
                            update_data = {
                                "type": "incremental_update",
                                "data": results,
                                "metadata": {
                                    "total": len(results),
                                    "goldPrice": round(gold_price, 2),
                                    "fxRates": fx_rates,
                                    "lastUpdated": datetime.now().isoformat(),
                                    "source": "financial-data-api-stream"
                                }
                            }
                            yield f"data: {json.dumps(update_data)}\n\n"
                    else:
                        # No cached data, send empty response
                        yield f"data: {json.dumps({'data': [], 'metadata': {'source': 'no-cache'}})}\n\n"
                        await asyncio.sleep(10)  # Shorter wait when no data
                        
                except Exception as e:
                    logger.error(f"Error in SSE stream: {e}")
                    error_data = {
                        "type": "error",
                        "message": f"Stream error: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"
                    await asyncio.sleep(5)  # Wait before retrying
                    
        except asyncio.CancelledError:
            logger.info("SSE stream cancelled")
        except Exception as e:
            logger.error(f"Critical error in SSE stream: {e}")
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@app.get("/api/market-tools/currency-impact")
async def get_currency_impact_analysis(
    country: Optional[str] = Query(None, description="Filter by country (JP, BR, US)")
):
    """Get detailed currency impact analysis for stocks"""
    
    # Get current FX rates
    fx_rates = await get_fx_rates()
    if 'XAUUSD' not in fx_rates:
        raise HTTPException(status_code=503, detail="Gold price not available")
    
    gold_price = fx_rates['XAUUSD']
    
    # Determine which stocks to analyze - use full lists
    stocks_to_analyze = []
    if country == 'JP':
        stocks_to_analyze = [(s, 'JP') for s in JP_STOCKS]  # Full list
    elif country == 'BR':
        stocks_to_analyze = [(s, 'BR') for s in BR_STOCKS]
    elif country == 'US':
        stocks_to_analyze = [(s, 'US') for s in US_STOCKS]
    elif country is None:
        # Get all stocks from each country
        stocks_to_analyze = (
            [(s, 'JP') for s in JP_STOCKS] +
            [(s, 'BR') for s in BR_STOCKS] +
            [(s, 'US') for s in US_STOCKS]
        )
    
    results = []
    
    # Process stocks with detailed currency analysis
    for symbol, country_code in stocks_to_analyze:
        try:
            # Get current price
            market_data = await get_market_data(symbol)
            if not market_data or not market_data.get('price'):
                continue
                
            local_price = market_data['price']
            
            # Convert to USD
            usd_price = local_price
            if country_code == 'JP' and 'USDJPY' in fx_rates:
                usd_price = local_price / fx_rates['USDJPY']
            elif country_code == 'BR' and 'USDBRL' in fx_rates:
                usd_price = local_price / fx_rates['USDBRL']
            
            # Calculate currency impact
            currency_impact = await calculate_currency_impact(
                symbol, country_code, local_price, usd_price, gold_price, fx_rates
            )
            
            # Get historical stock data for return calculations
            historical_stock = await get_historical_fx_data(symbol) if country_code != 'US' else pd.DataFrame()
            
            stock_analysis = {
                "symbol": symbol,
                "name": COMPANY_NAMES.get(symbol, symbol),
                "country": country_code,
                "localPrice": round(local_price, 2),
                "usdPrice": round(usd_price, 2),
                "goldPrice": round(gold_price, 2),
                "goldDenominatedPrice": round(usd_price / gold_price, 6),
                "currencyImpact": currency_impact,
                "riskLevel": classify_fx_risk(currency_impact),
                "timestamp": datetime.now().isoformat()
            }
            
            results.append(stock_analysis)
            
        except Exception as e:
            logger.error(f"Error analyzing currency impact for {symbol}: {e}")
            continue
    
    # Calculate aggregate currency exposure
    aggregate_exposure = calculate_aggregate_exposure(results)
    
    return {
        "data": results,
        "aggregateExposure": aggregate_exposure,
        "metadata": {
            "total": len(results),
            "goldPrice": round(gold_price, 2),
            "fxRates": fx_rates,
            "lastUpdated": datetime.now().isoformat(),
            "source": "financial-data-api"
        }
    }

def classify_fx_risk(currency_impact: Dict) -> str:
    """Classify FX risk level based on currency impact"""
    if currency_impact.get("fxExposure") == "USD":
        return "None"
    
    # Check maximum absolute FX impact across periods
    max_impact = max(
        abs(currency_impact.get("fxImpact1D", 0)),
        abs(currency_impact.get("fxImpact1W", 0)),
        abs(currency_impact.get("fxImpact1M", 0))
    )
    
    if max_impact >= 5.0:
        return "High"
    elif max_impact >= 2.0:
        return "Medium"
    elif max_impact >= 0.5:
        return "Low"
    else:
        return "Minimal"

def calculate_aggregate_exposure(results: List[Dict]) -> Dict:
    """Calculate aggregate currency exposure across all positions"""
    exposure_by_currency = {}
    total_positions = len(results)
    
    for result in results:
        currency = result["currencyImpact"]["fxExposure"]
        if currency not in exposure_by_currency:
            exposure_by_currency[currency] = {
                "count": 0,
                "avgImpact1D": 0,
                "avgImpact1W": 0,
                "avgImpact1M": 0,
                "riskDistribution": {"None": 0, "Minimal": 0, "Low": 0, "Medium": 0, "High": 0}
            }
        
        exposure_by_currency[currency]["count"] += 1
        exposure_by_currency[currency]["avgImpact1D"] += result["currencyImpact"].get("fxImpact1D", 0)
        exposure_by_currency[currency]["avgImpact1W"] += result["currencyImpact"].get("fxImpact1W", 0)
        exposure_by_currency[currency]["avgImpact1M"] += result["currencyImpact"].get("fxImpact1M", 0)
        exposure_by_currency[currency]["riskDistribution"][result["riskLevel"]] += 1
    
    # Calculate averages
    for currency, data in exposure_by_currency.items():
        count = data["count"]
        if count > 0:
            data["avgImpact1D"] = round(data["avgImpact1D"] / count, 2)
            data["avgImpact1W"] = round(data["avgImpact1W"] / count, 2)
            data["avgImpact1M"] = round(data["avgImpact1M"] / count, 2)
            data["percentage"] = round((count / total_positions) * 100, 1)
    
    return exposure_by_currency

@app.get("/api/market-tools/multi-asset-view")
async def get_multi_asset_view(
    include_stocks: bool = Query(True, description="Include stock analysis"),
    include_fx: bool = Query(True, description="Include FX rates"),
    include_commodities: bool = Query(True, description="Include commodity prices"),
    stock_limit: int = Query(20, description="Limit number of stocks")
):
    """Get unified multi-asset view across stocks, FX, and commodities"""
    
    # Get FX rates and gold price
    fx_rates = await get_fx_rates()
    if 'XAUUSD' not in fx_rates:
        raise HTTPException(status_code=503, detail="Gold price not available")
    
    gold_price = fx_rates['XAUUSD']
    
    multi_asset_data = {
        "stocks": [],
        "fx_rates": [],
        "commodities": [],
        "portfolio_summary": {},
        "cross_asset_analysis": {}
    }
    
    # 1. Stock Analysis (if requested)
    if include_stocks:
        try:
            # Get stocks from our comprehensive lists - no hardcoded symbols
            working_stocks = []
            
            # Add stocks from each country up to the limit
            stocks_per_country = max(1, stock_limit // 3)
            
            working_stocks.extend([(s, "JP") for s in JP_STOCKS[:stocks_per_country]])
            working_stocks.extend([(s, "BR") for s in BR_STOCKS[:stocks_per_country]])
            working_stocks.extend([(s, "US") for s in US_STOCKS[:stocks_per_country]])
            
            # If still under limit, add more
            remaining = stock_limit - len(working_stocks)
            if remaining > 0:
                additional_jp = JP_STOCKS[stocks_per_country:stocks_per_country + remaining//3] if len(JP_STOCKS) > stocks_per_country else []
                additional_br = BR_STOCKS[stocks_per_country:stocks_per_country + remaining//3] if len(BR_STOCKS) > stocks_per_country else []
                additional_us = US_STOCKS[stocks_per_country:stocks_per_country + remaining//3] if len(US_STOCKS) > stocks_per_country else []
                
                working_stocks.extend([(s, "JP") for s in additional_jp])
                working_stocks.extend([(s, "BR") for s in additional_br])
                working_stocks.extend([(s, "US") for s in additional_us])
            
            # Ensure we don't exceed the limit
            working_stocks = working_stocks[:stock_limit]
            
            # Process stocks with enhanced multi-asset context
            stock_tasks = []
            for symbol, country_code in working_stocks:
                stock_tasks.append(process_multi_asset_stock(symbol, country_code, fx_rates, gold_price))
            
            stock_results = await asyncio.gather(*stock_tasks, return_exceptions=True)
            
            # Filter successful results
            for result in stock_results:
                if isinstance(result, dict) and result:
                    multi_asset_data["stocks"].append(result)
                    
        except Exception as e:
            logger.error(f"Error processing stocks for multi-asset view: {e}")
    
    # 2. FX Rates Analysis (if requested)
    if include_fx:
        fx_analysis = []
        major_pairs = [
            ("USDJPY", "USD/JPY", "Japanese Yen"),
            ("USDBRL", "USD/BRL", "Brazilian Real"),
            ("EURUSD", "EUR/USD", "Euro"),
            ("GBPUSD", "GBP/USD", "British Pound")
        ]
        
        for fx_symbol, display_name, currency_name in major_pairs:
            if fx_symbol in fx_rates:
                # Get historical FX data for trend analysis
                fx_historical = await get_historical_fx_data(fx_symbol)
                
                fx_data = {
                    "symbol": fx_symbol,
                    "name": display_name,
                    "currency": currency_name,
                    "rate": fx_rates[fx_symbol],
                    "type": "fx_rate",
                    "trend": calculate_fx_trend(fx_historical) if not fx_historical.empty else None,
                    "volatility": calculate_fx_volatility(fx_historical) if not fx_historical.empty else None
                }
                fx_analysis.append(fx_data)
        
        multi_asset_data["fx_rates"] = fx_analysis
    
    # 3. Commodities Analysis (if requested)  
    if include_commodities:
        commodities = []
        
        # Gold analysis
        if 'XAUUSD' in fx_rates:
            gold_historical = await get_historical_fx_data("XAUUSD")
            gold_data = {
                "symbol": "XAUUSD",
                "name": "Gold Spot",
                "price": fx_rates['XAUUSD'],
                "type": "precious_metal",
                "trend": calculate_fx_trend(gold_historical) if not gold_historical.empty else None,
                "volatility": calculate_fx_volatility(gold_historical) if not gold_historical.empty else None,
                "role": "Safe haven asset and portfolio hedge"
            }
            commodities.append(gold_data)
        
        # Oil (if available)
        try:
            oil_data = await get_market_data("CL=F")  # Crude Oil futures
            if oil_data:
                commodities.append({
                    "symbol": "CL=F",
                    "name": "Crude Oil",
                    "price": oil_data.get("price"),
                    "type": "energy",
                    "role": "Energy commodity, inflation indicator"
                })
        except Exception:
            pass  # Oil data optional
            
        multi_asset_data["commodities"] = commodities
    
    # 4. Portfolio Summary
    multi_asset_data["portfolio_summary"] = calculate_portfolio_summary(
        multi_asset_data["stocks"], 
        multi_asset_data["fx_rates"], 
        multi_asset_data["commodities"]
    )
    
    # 5. Cross-Asset Analysis
    multi_asset_data["cross_asset_analysis"] = calculate_cross_asset_analysis(
        multi_asset_data["stocks"],
        multi_asset_data["fx_rates"], 
        multi_asset_data["commodities"],
        gold_price
    )
    
    return {
        "data": multi_asset_data,
        "metadata": {
            "total_stocks": len(multi_asset_data["stocks"]),
            "total_fx_pairs": len(multi_asset_data["fx_rates"]),
            "total_commodities": len(multi_asset_data["commodities"]),
            "goldPrice": round(gold_price, 2),
            "baseCurrency": "USD",
            "lastUpdated": datetime.now().isoformat(),
            "source": "financial-data-api-multi-asset"
        }
    }

async def process_multi_asset_stock(symbol: str, country_code: str, fx_rates: Dict[str, float], gold_price: float) -> Optional[Dict]:
    """Process stock with multi-asset context"""
    try:
        # Get basic stock data
        stock_data = await process_stock(symbol, country_code, fx_rates, gold_price, skip_historical=True)
        if not stock_data:
            return None
            
        # Add multi-asset specific fields
        stock_data["assetClass"] = "equity"
        stock_data["sector"] = determine_sector(symbol)
        stock_data["marketCorrelation"] = calculate_market_correlation(symbol, country_code)
        
        return stock_data
        
    except Exception as e:
        logger.error(f"Error processing multi-asset stock {symbol}: {e}")
        return None

def calculate_fx_trend(fx_historical: pd.DataFrame) -> Optional[Dict]:
    """Calculate FX trend analysis"""
    if fx_historical.empty or len(fx_historical) < 2:
        return None
        
    try:
        current_price = fx_historical.iloc[-1]["close"]
        week_ago = fx_historical.iloc[-7]["close"] if len(fx_historical) >= 7 else fx_historical.iloc[0]["close"]
        month_ago = fx_historical.iloc[-30]["close"] if len(fx_historical) >= 30 else fx_historical.iloc[0]["close"]
        
        return {
            "1w_change": round(((current_price - week_ago) / week_ago) * 100, 2),
            "1m_change": round(((current_price - month_ago) / month_ago) * 100, 2),
            "direction": "up" if current_price > week_ago else "down"
        }
    except Exception:
        return None

def calculate_fx_volatility(fx_historical: pd.DataFrame) -> Optional[float]:
    """Calculate FX volatility (annualized)"""
    if fx_historical.empty or len(fx_historical) < 10:
        return None
        
    try:
        # Calculate daily returns
        returns = fx_historical["close"].pct_change().dropna()
        # Annualized volatility (assuming 252 trading days)
        volatility = returns.std() * np.sqrt(252) * 100
        return round(volatility, 2)
    except Exception:
        return None

def determine_sector(symbol: str) -> str:
    """Determine stock sector based on symbol"""
    sector_mapping = {
        "COST": "Consumer Staples",
        "NVDA": "Technology", 
        "AAPL": "Technology",
        "8058.T": "Trading Company",
        "7203.T": "Automotive",
        "VALE3": "Mining",
        "PETR4": "Energy"
    }
    return sector_mapping.get(symbol, "Unknown")

def calculate_market_correlation(symbol: str, country_code: str) -> Optional[str]:
    """Calculate market correlation category"""
    try:
        if country_code == "US":
            return "High correlation with US market"
        elif country_code == "JP":
            return "Moderate correlation with Nikkei"
        elif country_code == "BR":
            return "High correlation with Brazilian market"
        else:
            return "Unknown correlation"
    except Exception:
        return None

def calculate_portfolio_summary(stocks: List[Dict], fx_rates: List[Dict], commodities: List[Dict]) -> Dict:
    """Calculate portfolio-level summary statistics"""
    try:
        summary = {
            "totalPositions": len(stocks) + len(fx_rates) + len(commodities),
            "assetAllocation": {
                "equities": len(stocks),
                "fx": len(fx_rates), 
                "commodities": len(commodities)
            },
            "currencyExposure": {},
            "riskProfile": "Diversified" if len(stocks) > 5 else "Concentrated"
        }
        
        # Currency exposure breakdown
        for stock in stocks:
            country = stock.get("country", "Unknown")
            if country not in summary["currencyExposure"]:
                summary["currencyExposure"][country] = 0
            summary["currencyExposure"][country] += 1
            
        return summary
        
    except Exception as e:
        logger.error(f"Error calculating portfolio summary: {e}")
        return {}

def calculate_cross_asset_analysis(stocks: List[Dict], fx_rates: List[Dict], commodities: List[Dict], gold_price: float) -> Dict:
    """Calculate cross-asset relationships and insights"""
    try:
        analysis = {
            "goldCorrelation": "Gold serves as portfolio hedge against currency risk",
            "fxImpact": "Currency movements affect international stock returns",
            "diversificationBenefit": "Mixed currency exposure provides natural hedging",
            "riskFactors": []
        }
        
        # Identify risk factors
        if any(stock.get("country") == "JP" for stock in stocks):
            analysis["riskFactors"].append("JPY volatility affects Japanese holdings")
            
        if any(stock.get("country") == "BR" for stock in stocks):
            analysis["riskFactors"].append("BRL volatility affects Brazilian holdings")
            
        if gold_price > 3000:
            analysis["riskFactors"].append("Gold at elevated levels - monitor for reversal")
            
        return analysis
        
    except Exception as e:
        logger.error(f"Error calculating cross-asset analysis: {e}")
        return {}

@app.get("/api/market-tools/expand-historical/{symbol}")
async def expand_historical_data(
    symbol: str,
    current_period: str = Query("1 W", description="Current historical period"),
    target_period: str = Query("1 Y", description="Target historical period to expand to")
):
    """Expand historical data for a specific symbol to a longer time period"""
    try:
        # Get current market data
        market_data = await get_market_data(symbol)
        if not market_data or not market_data.get('price'):
            raise HTTPException(status_code=404, detail=f"No market data available for {symbol}")
        
        current_price = market_data['price']
        
        # Get expanded historical data
        hist_df = await get_historical_data(symbol, target_period)
        if hist_df.empty:
            raise HTTPException(status_code=404, detail=f"No historical data available for {symbol}")
        
        # Calculate metrics for expanded period
        metrics = calculate_metrics(hist_df, current_price)
        
        return {
            "symbol": symbol,
            "currentPrice": current_price,
            "expandedPeriod": target_period,
            "previousPeriod": current_period,
            "distancePercentages": metrics["distancePercentages"],
            "historicalRanges": metrics["historicalRanges"],
            "dataPoints": len(hist_df),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error expanding historical data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market-tools/ibkr-status")
async def get_ibkr_status():
    """Get IBKR connection status from financial-data-api"""
    try:
        # Call financial-data-api health endpoint
        response = await http_client.get("/health")
        
        if response.status_code == 200:
            health_data = response.json()
            ibkr_status = health_data.get("providers", {}).get("ibkr", {})
            
            return {
                "connected": ibkr_status.get("healthy", False),
                "status": "healthy" if ibkr_status.get("healthy", False) else "disconnected",
                "host": ibkr_status.get("host", ""),
                "port": ibkr_status.get("port", 0),
                "client_id": ibkr_status.get("client_id", 0),
                "message": "IBKR connection via TWS" if ibkr_status.get("healthy", False) else "IBKR disconnected or unavailable"
            }
        else:
            return {
                "connected": False,
                "status": "error",
                "message": f"Failed to check status: HTTP {response.status_code}"
            }
            
    except Exception as e:
        logger.error(f"Error checking IBKR status: {e}")
        return {
            "connected": False,
            "status": "error",
            "message": f"Error: {str(e)}"
        }

@app.get("/api/market-tools/cache-status")
async def get_cache_status():
    """Get cache update worker status and cache statistics"""
    try:
        # Check various cache keys
        cache_stats = {}
        
        # Check main gold analysis cache
        gold_cache = await redis_client.get("gold_analysis_all")
        if gold_cache:
            gold_data = json.loads(gold_cache)
            cache_stats["gold_analysis"] = {
                "exists": True,
                "stock_count": len(gold_data.get("data", [])),
                "last_updated": gold_data.get("metadata", {}).get("lastUpdated", "Unknown"),
                "ttl": await redis_client.ttl("gold_analysis_all")
            }
        else:
            cache_stats["gold_analysis"] = {"exists": False}
        
        # Check FX rates cache
        fx_cache = await redis_client.get("fx_rates_all")
        if fx_cache:
            fx_data = json.loads(fx_cache)
            cache_stats["fx_rates"] = {
                "exists": True,
                "rates": list(fx_data.keys()) if isinstance(fx_data, dict) else [],
                "ttl": await redis_client.ttl("fx_rates_all")
            }
        else:
            cache_stats["fx_rates"] = {"exists": False}
        
        # Check commodity cache
        commodity_cache = await redis_client.get("fx_commodity_all")
        if commodity_cache:
            commodity_data = json.loads(commodity_cache)
            cache_stats["commodities"] = {
                "exists": True,
                "item_count": len(commodity_data) if isinstance(commodity_data, list) else 0,
                "ttl": await redis_client.ttl("fx_commodity_all")
            }
        else:
            cache_stats["commodities"] = {"exists": False}
        
        return {
            "worker_status": {
                "running": cache_update_running,
                "task_alive": background_task is not None and not background_task.done() if background_task else False,
                "update_interval_seconds": CACHE_WARMING_INTERVAL,
                "cache_ttl_seconds": CACHE_TTL
            },
            "cache_stats": cache_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting cache status: {e}")
        return {
            "error": str(e),
            "worker_status": {
                "running": cache_update_running,
                "task_alive": False
            }
        }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time market data updates"""
    await manager.connect(websocket)
    try:
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message.get("type") == "subscribe":
                symbols = message.get("symbols", [])
                await manager.subscribe(websocket, symbols)
                
                # Send confirmation
                await manager.send_personal_message(
                    json.dumps({
                        "type": "subscription_confirmed",
                        "symbols": symbols,
                        "timestamp": datetime.now().isoformat()
                    }),
                    websocket
                )
                
                # Send initial data for subscribed symbols
                for symbol in symbols:
                    if symbol == "*":
                        # Send all available data
                        cache_data = await redis_client.get("gold_analysis_all")
                        if cache_data:
                            gold_data = json.loads(cache_data)
                            for stock in gold_data.get("data", []):
                                await manager.send_personal_message(
                                    json.dumps({
                                        "type": "initial_data",
                                        "symbol": stock["symbol"],
                                        "data": stock,
                                        "timestamp": datetime.now().isoformat()
                                    }),
                                    websocket
                                )
                        break
                    else:
                        # Send specific symbol data
                        market_data = await get_market_data(symbol)
                        if market_data:
                            await manager.send_personal_message(
                                json.dumps({
                                    "type": "initial_data",
                                    "symbol": symbol,
                                    "data": market_data,
                                    "timestamp": datetime.now().isoformat()
                                }),
                                websocket
                            )
            
            elif message.get("type") == "ping":
                # Respond to ping with pong
                await manager.send_personal_message(
                    json.dumps({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }),
                    websocket
                )
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/api/market-tools/chart/{symbol}")
async def get_chart_data(
    symbol: str,
    period: str = Query("1 M", description="Time period (1 W, 1 M, 3 M, 6 M, 1 Y, 5 Y)"),
    chart_type: str = Query("price", description="Chart type: price, gold, volume")
):
    """Get chart data for a specific symbol"""
    try:
        # Get historical data
        hist_df = await get_historical_data(symbol, period)
        if hist_df.empty:
            raise HTTPException(status_code=404, detail=f"No historical data available for {symbol}")
        
        # Get current FX rates for gold conversion if needed
        fx_rates = await get_fx_rates()
        gold_price = fx_rates.get("XAUUSD", 0)
        
        # Determine country for FX conversion
        country_code = 'JP' if symbol.endswith('.T') else 'BR' if any(symbol.startswith(prefix) for prefix in ['VALE', 'GGBR', 'SUZB', 'PETR', 'PRIO', 'JBSS', 'SLCE', 'SMTO', 'KEPL', 'RAIL', 'TUPY', 'AGRO', 'SOJA', 'EMBR', 'BRAV', 'BBDC', 'MRFG', 'BRFS', 'KLBN', 'FIBR', 'CCRO', 'CMIG', 'ELET', 'CPFE', 'TAEE', 'COCE']) else 'US'
        
        # Prepare chart data
        chart_data = []
        
        for index, row in hist_df.iterrows():
            timestamp = index.isoformat()
            local_price = row['close']
            
            # Convert to USD if needed
            usd_price = local_price
            if country_code == 'JP' and 'USDJPY' in fx_rates:
                usd_price = local_price / fx_rates['USDJPY']
            elif country_code == 'BR' and 'USDBRL' in fx_rates:
                usd_price = local_price / fx_rates['USDBRL']
            
            # Calculate gold-denominated price
            gold_denominated_price = usd_price / gold_price if gold_price > 0 else 0
            
            data_point = {
                "timestamp": timestamp,
                "date": index.strftime('%Y-%m-%d'),
                "price": round(local_price, 2),
                "usdPrice": round(usd_price, 2),
                "goldPrice": round(gold_denominated_price, 6),
                "volume": int(row.get('volume', 0)),
                "high": round(row.get('high', local_price), 2),
                "low": round(row.get('low', local_price), 2),
                "open": round(row.get('open', local_price), 2)
            }
            chart_data.append(data_point)
        
        # Calculate additional metrics
        if len(chart_data) > 1:
            first_price = chart_data[0]['price']
            last_price = chart_data[-1]['price']
            price_change = last_price - first_price
            price_change_percent = (price_change / first_price) * 100 if first_price > 0 else 0
        else:
            price_change = 0
            price_change_percent = 0
        
        return {
            "symbol": symbol,
            "period": period,
            "chartType": chart_type,
            "data": chart_data,
            "metadata": {
                "dataPoints": len(chart_data),
                "startDate": chart_data[0]["date"] if chart_data else None,
                "endDate": chart_data[-1]["date"] if chart_data else None,
                "priceChange": round(price_change, 2),
                "priceChangePercent": round(price_change_percent, 2),
                "currency": "JPY" if country_code == 'JP' else "BRL" if country_code == 'BR' else "USD",
                "goldPriceUsed": round(gold_price, 2),
                "lastUpdated": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chart data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market-tools/chart/{symbol}/compare")
async def get_comparison_chart_data(
    symbol: str,
    compare_symbols: str = Query(..., description="Comma-separated list of symbols to compare"),
    period: str = Query("1 M", description="Time period"),
    normalize: bool = Query(True, description="Normalize to percentage changes")
):
    """Get chart data for comparing multiple symbols"""
    try:
        symbols = [symbol] + [s.strip() for s in compare_symbols.split(',')]
        all_data = {}
        
        # Get data for all symbols
        for sym in symbols:
            try:
                hist_df = await get_historical_data(sym, period)
                if not hist_df.empty:
                    all_data[sym] = hist_df
            except Exception as e:
                logger.warning(f"Failed to get data for {sym}: {e}")
        
        if not all_data:
            raise HTTPException(status_code=404, detail="No data available for any symbols")
        
        # Find common date range
        common_dates = None
        for df in all_data.values():
            dates = set(df.index.date)
            if common_dates is None:
                common_dates = dates
            else:
                common_dates = common_dates.intersection(dates)
        
        if not common_dates:
            raise HTTPException(status_code=404, detail="No common dates found for comparison")
        
        # Prepare comparison data
        chart_data = []
        sorted_dates = sorted(common_dates)
        
        for date in sorted_dates:
            data_point = {
                "date": date.isoformat(),
                "timestamp": pd.Timestamp(date).isoformat()
            }
            
            for sym, df in all_data.items():
                try:
                    # Find the row for this date
                    date_data = df[df.index.date == date]
                    if not date_data.empty:
                        price = date_data.iloc[0]['close']
                        
                        if normalize:
                            # Calculate percentage change from first value
                            first_price = df.iloc[0]['close']
                            pct_change = ((price - first_price) / first_price) * 100
                            data_point[sym] = round(pct_change, 2)
                        else:
                            data_point[sym] = round(price, 2)
                except Exception:
                    data_point[sym] = None
            
            chart_data.append(data_point)
        
        return {
            "symbols": symbols,
            "period": period,
            "normalized": normalize,
            "data": chart_data,
            "metadata": {
                "dataPoints": len(chart_data),
                "startDate": chart_data[0]["date"] if chart_data else None,
                "endDate": chart_data[-1]["date"] if chart_data else None,
                "commonDates": len(common_dates),
                "lastUpdated": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting comparison chart data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)