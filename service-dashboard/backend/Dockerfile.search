FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy search API code
COPY api/search_api.py ./api/

# Expose port
EXPOSE 8028

# Run the search API
CMD ["python", "api/search_api.py"]