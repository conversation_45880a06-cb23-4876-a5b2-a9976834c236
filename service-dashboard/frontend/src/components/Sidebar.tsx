'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import {
  Home,
  FileText,
  Activity,
  BarChart3,
  Settings,
  Database,
  TrendingUp,
  TrendingDown,
  Code2,
  FileCode,
  LogOut,
  Download,
  FileSearch,
  Monitor,
  Bell,
  Tag,
  Brain,
  Network,
  Cpu,
  Layers,
  Eye,
  Shield,
  HardDrive,
  Menu,
  ChevronLeft,
  Building2,
  Search,
  MapPin,
  Calculator,
  Briefcase,
  LineChart,
  DollarSign,
  FileSpreadsheet,
  Target,
  Coins,
  BarChart2,
  List,
  ArrowLeftRight
} from 'lucide-react'
import { useTheme } from '@/contexts/ThemeContext'
import { useDashboardMode } from '@/contexts/DashboardModeContext'

// Investment mode navigation
const investmentSystemNavigation = [
  { name: 'Overview', href: '/', icon: Home },
  { name: 'System', href: '/system-overview', icon: Monitor },
  { name: 'Service Health', href: '/service-health', icon: Activity },
  { name: 'Performance', href: '/performance', icon: Activity },
  { name: 'Alerts', href: '/alerts', icon: Bell },
  { name: 'Monitoring', href: '/monitoring', icon: Cpu },
  { name: 'Documents', href: '/documents', icon: FileText },
  { name: 'Queues', href: '/queues', icon: Activity },
  { name: 'Validation Monitor', href: '/validation', icon: Shield },
  { name: 'Quality', href: '/quality', icon: BarChart3 },
  { name: 'Analytics', href: '/analytics', icon: TrendingUp },
  { name: 'Storage', href: '/storage', icon: Database },
  { name: 'Backups', href: '/backups', icon: HardDrive },
  { name: 'Prompts', href: '/prompts', icon: Code2 },
  { name: 'Logs', href: '/logs', icon: FileCode },
  { name: 'Settings', href: '/settings', icon: Settings },
]

const investmentServiceNavigation = [
  { name: 'Scraper', href: '/scraper', icon: Download },
  { name: 'Extractor', href: '/extractor-enhanced', icon: FileSearch },
  { name: 'Categorizer', href: '/categorizer', icon: Tag },
  { name: 'Doc Analyst', href: '/doc-analyst', icon: Brain },
  { name: 'Market Analyst', href: '/market-analyst', icon: TrendingUp },
  { name: 'Trading Signals', href: '/trading-signals', icon: Activity },
  { name: 'Neo4j Graph', href: '/neo4j', icon: Network },
  { name: 'ChromaDB', href: '/chromadb', icon: Database },
  { name: 'Embeddings', href: '/embeddings', icon: Layers },
]

const investmentToolsNavigation = [
  { name: 'Portfolio', href: '/portfolio', icon: BarChart3 },
  { name: 'Stop Calculator', href: '/stop-calculator', icon: Target },
  { name: 'IBKR Trading', href: '/trading', icon: TrendingUp },
]

const investmentMarketToolsNavigation = [
  { name: 'Currency Analysis', href: '/currency-analysis', icon: ArrowLeftRight },
  { name: 'Currency Impact', href: '/currency-impact', icon: TrendingDown },
  { name: 'Multi-Asset View', href: '/multi-asset', icon: BarChart3 },
]

const investmentAnalysisNavigation = [
  { name: 'Watchlist', href: '/watchlist', icon: List },
  { name: 'Company Comparison', href: '/company-comparison', icon: BarChart2 },
  { name: 'Intrinsic Value', href: '/intrinsic-value', icon: Calculator },
  { name: 'Short Squeeze', href: '/short-squeeze', icon: TrendingUp },
  { name: 'Economy', href: '/economy', icon: DollarSign },
]

// Real Estate mode navigation
const realEstateSystemNavigation = [
  { name: 'Overview', href: '/real-estate', icon: Home },
  { name: 'Properties', href: '/real-estate/properties', icon: Building2 },
  { name: 'Property Search', href: '/property-search', icon: Search },
  { name: 'Services', href: '/real-estate/services', icon: Activity },
  { name: 'Settings', href: '/settings', icon: Settings },
]

const realEstateServiceNavigation = [
  { name: 'Valuations', href: '/valuations', icon: Calculator },
  { name: 'ROI Analysis', href: '/roi-analysis', icon: LineChart },
  { name: 'Market Trends', href: '/market-trends', icon: TrendingUp },
  { name: 'Comparables', href: '/comparables', icon: FileSpreadsheet },
  { name: 'Financial Reports', href: '/financial-reports', icon: DollarSign },
]

export default function Sidebar() {
  const pathname = usePathname()
  const { theme } = useTheme()
  const { mode } = useDashboardMode()
  const [isCollapsed, setIsCollapsed] = useState(() => {
    // Initialize from localStorage if available
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('sidebar-collapsed')
      return saved === 'true'
    }
    return false
  })

  const toggleSidebar = () => {
    const newCollapsed = !isCollapsed
    setIsCollapsed(newCollapsed)
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebar-collapsed', newCollapsed.toString())
    }
  }

  const renderNavigationItem = (item: { name: string; href: string; icon: React.ElementType }) => {
    const isActive = pathname === item.href
    return (
      <Link
        key={item.name}
        href={item.href}
        className={`
          group relative flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors
          ${isActive
            ? 'bg-gray-800 text-white dark:bg-gray-800'
            : 'text-gray-300 hover:bg-gray-700 hover:text-white dark:hover:bg-gray-800'
          }
        `}
        title={isCollapsed ? item.name : undefined}
      >
        <item.icon className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'}`} />
        {!isCollapsed && <span>{item.name}</span>}
        {isCollapsed && (
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50">
            {item.name}
          </div>
        )}
      </Link>
    )
  }

  return (
    <div className={`flex h-full flex-col bg-gray-900 dark:bg-gray-950 transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'}`}>
      <div className={`flex h-16 items-center ${isCollapsed ? 'justify-center px-2' : 'justify-between px-6'}`}>
        {!isCollapsed && (
          <div className="flex items-center">
            <img
              src={theme === 'dark' ? "/images/br1dge_logo_white_full.png" : "/images/br1dge_logo.png"}
              alt="BR1DGE"
              className="h-12 w-auto"
            />
          </div>
        )}
        <button
          onClick={toggleSidebar}
          className="p-1.5 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
          title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? <Menu className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
        </button>
      </div>

      <nav className="flex-1 px-3 py-4 overflow-y-auto">
        {mode === 'investment' ? (
          <>
            {/* Investment: System Management Section */}
            <div className="mb-6">
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  System Management
                </h3>
              )}
              <div className="space-y-1">
                {investmentSystemNavigation.map(renderNavigationItem)}
              </div>
            </div>

            {/* Investment: Service Monitoring Section */}
            <div className="mb-6">
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Service Monitoring
                </h3>
              )}
              <div className="space-y-1">
                {investmentServiceNavigation.map(renderNavigationItem)}
              </div>
            </div>

            {/* Investment: Tools Section */}
            <div className="mb-6">
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Tools
                </h3>
              )}
              <div className="space-y-1">
                {investmentToolsNavigation.map(renderNavigationItem)}
              </div>
            </div>

            {/* Investment: Market Tools Section */}
            <div className="mb-6">
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Market Tools
                </h3>
              )}
              <div className="space-y-1">
                {investmentMarketToolsNavigation.map(renderNavigationItem)}
              </div>
            </div>

            {/* Investment: Financial Analysis Section */}
            <div>
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Financial Analysis
                </h3>
              )}
              <div className="space-y-1">
                {investmentAnalysisNavigation.map(renderNavigationItem)}
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Real Estate: Property Management Section */}
            <div className="mb-6">
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Property Management
                </h3>
              )}
              <div className="space-y-1">
                {realEstateSystemNavigation.map(renderNavigationItem)}
              </div>
            </div>

            {/* Real Estate: Analysis Tools Section */}
            <div>
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Analysis Tools
                </h3>
              )}
              <div className="space-y-1">
                {realEstateServiceNavigation.map(renderNavigationItem)}
              </div>
            </div>
          </>
        )}
      </nav>

      <div className="p-4 border-t border-gray-800">
        <button
          className={`group relative flex items-center w-full px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors ${isCollapsed ? 'justify-center' : ''}`}
          title={isCollapsed ? 'Sign Out' : undefined}
        >
          <LogOut className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'}`} />
          {!isCollapsed && <span>Sign Out</span>}
          {isCollapsed && (
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50">
              Sign Out
            </div>
          )}
        </button>
      </div>
    </div>
  )
}
