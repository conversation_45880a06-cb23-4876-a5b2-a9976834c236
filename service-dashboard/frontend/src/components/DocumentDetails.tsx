'use client'

import { useState, useEffect } from 'react'
import { FileText, Calendar, Tag, Building2, AlertCircle, Clock, CheckCircle, Eye, Download } from 'lucide-react'

interface DocumentData {
  id: string
  document_type: string
  company_code: string
  created_at: string
  updated_at: string
  discovery: any
  extraction: any
  categorization: any
  enrichment: any
  analysis: any
  market_analysis: any
  signals: any
  pipeline: any
}

interface DocumentDetailsResponse {
  document: DocumentData
}

interface DocumentDetailsProps {
  documentId: string
  onCompanyClick?: (companyCode: string) => void
}

export default function DocumentDetails({ documentId, onCompanyClick }: DocumentDetailsProps) {
  const [data, setData] = useState<DocumentDetailsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    const fetchDocumentData = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/search/document/${documentId}`)
        if (response.ok) {
          const result = await response.json()
          setData(result)
          setError(null)
        } else {
          setError('Failed to fetch document data')
        }
      } catch (err) {
        setError('Network error')
      } finally {
        setLoading(false)
      }
    }

    if (documentId) {
      fetchDocumentData()
    }
  }, [documentId])

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStageColor = (stage: string) => {
    const colors: Record<string, string> = {
      'discovery': 'bg-blue-500',
      'extraction': 'bg-green-500',
      'categorization': 'bg-purple-500',
      'enrichment': 'bg-cyan-500',
      'analysis': 'bg-orange-500',
      'market_intelligence': 'bg-indigo-500',
      'trading_signals': 'bg-pink-500',
      'storage': 'bg-gray-500',
      'prediction': 'bg-rose-500',
      'completed': 'bg-emerald-500'
    }
    return colors[stage] || 'bg-gray-400'
  }

  const renderJsonData = (data: any, title: string) => {
    if (!data) return null
    
    return (
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{title}</h4>
        <pre className="text-sm text-gray-600 dark:text-gray-400 overflow-x-auto whitespace-pre-wrap max-h-96 overflow-y-auto">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    )
  }

  const handleCompanyClick = (companyCode: string) => {
    if (onCompanyClick) {
      onCompanyClick(companyCode)
    } else {
      window.location.href = `/company/${companyCode}`
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-6"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            <div className="h-4 bg-gray-300 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
          <AlertCircle className="h-5 w-5" />
          <span>{error || 'Document not found'}</span>
        </div>
      </div>
    )
  }

  const { document } = data
  const pipeline = document.pipeline || {}
  const validationGates = pipeline.validation_gates_passed || []

  return (
    <div className="space-y-6">
      {/* Document Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
              <FileText className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {document.discovery?.title || `Document ${document.id.substring(0, 8)}`}
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
                ID: {document.id}
              </p>
              <div className="flex flex-wrap gap-4 mt-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-gray-500" />
                  <span 
                    className="text-sm text-blue-600 dark:text-blue-400 cursor-pointer hover:underline"
                    onClick={() => document.company_code && handleCompanyClick(document.company_code)}
                  >
                    {document.company_code || 'Unknown Company'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {document.document_type || 'Unknown Type'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Created: {formatDate(document.created_at)}
                  </span>
                </div>
                {document.categorization?.primary_category && (
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {document.categorization.primary_category}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white ${getStageColor(pipeline.current_stage)}`}
            >
              {pipeline.current_stage || 'unknown'}
            </span>
          </div>
        </div>
      </div>

      {/* Pipeline Status */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Pipeline Status
        </h2>
        <div className="flex flex-wrap gap-2">
          {['discovery', 'extraction', 'categorization', 'enrichment', 'analysis', 'market_analysis', 'signals', 'storage', 'prediction'].map((stage) => {
            const isPassed = validationGates.includes(stage)
            const isCurrent = pipeline.current_stage === stage
            return (
              <div
                key={stage}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium
                  ${isPassed 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : isCurrent
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  }`}
              >
                {isPassed ? (
                  <CheckCircle className="h-4 w-4" />
                ) : isCurrent ? (
                  <Clock className="h-4 w-4" />
                ) : (
                  <div className="h-4 w-4 rounded-full border-2 border-gray-400" />
                )}
                {stage.replace('_', ' ')}
              </div>
            )
          })}
        </div>
      </div>

      {/* Document Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'discovery', label: 'Discovery' },
              { id: 'extraction', label: 'Extraction' },
              { id: 'categorization', label: 'Categorization' },
              { id: 'enrichment', label: 'Enrichment' },
              { id: 'analysis', label: 'Analysis' },
              { id: 'market_analysis', label: 'Market Analysis' },
              { id: 'signals', label: 'Trading Signals' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Document Info</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">ID:</span>
                      <span className="text-gray-900 dark:text-white font-mono">{document.id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Type:</span>
                      <span className="text-gray-900 dark:text-white">{document.document_type || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Company:</span>
                      <span 
                        className="text-blue-600 dark:text-blue-400 cursor-pointer hover:underline"
                        onClick={() => document.company_code && handleCompanyClick(document.company_code)}
                      >
                        {document.company_code || 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Created:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(document.created_at)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Updated:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(document.updated_at)}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Pipeline Status</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Current Stage:</span>
                      <span className="text-gray-900 dark:text-white">{pipeline.current_stage || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Current Queue:</span>
                      <span className="text-gray-900 dark:text-white">{pipeline.current_queue || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Gates Passed:</span>
                      <span className="text-gray-900 dark:text-white">{validationGates.length}/9</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'discovery' && renderJsonData(document.discovery, 'Discovery Data')}
          {activeTab === 'extraction' && renderJsonData(document.extraction, 'Extraction Data')}
          {activeTab === 'categorization' && renderJsonData(document.categorization, 'Categorization Data')}
          {activeTab === 'enrichment' && renderJsonData(document.enrichment, 'Enrichment Data')}
          {activeTab === 'analysis' && renderJsonData(document.analysis, 'Analysis Data')}
          {activeTab === 'market_analysis' && renderJsonData(document.market_analysis, 'Market Analysis Data')}
          {activeTab === 'signals' && renderJsonData(document.signals, 'Trading Signals Data')}
        </div>
      </div>
    </div>
  )
}