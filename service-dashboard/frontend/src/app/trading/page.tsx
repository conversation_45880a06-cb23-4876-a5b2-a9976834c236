// ABOUTME: IBKR Trading Dashboard - Order execution and portfolio management interface
// ABOUTME: Secure trading interface with real-time position tracking and order management

'use client'

import React, { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, DollarSign, Activity, AlertTriangle, Shield } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface Position {
  symbol: string
  position: number
  market_price: number
  market_value: number
  average_cost: number
  unrealized_pnl: number
  realized_pnl: number
  currency: string
  exchange: string
}

interface Order {
  order_id: number
  symbol: string
  action: string
  quantity: number
  order_type: string
  status: string
  filled_quantity: number
  remaining_quantity: number
  limit_price?: number
  stop_price?: number
  avg_fill_price?: number
  commission?: number
  timestamp: string
}

interface AccountSummary {
  account_id: string
  total_cash_value: number
  net_liquidation: number
  buying_power: number
  day_trades_remaining: number
  currency: string
  positions: Position[]
}

export default function TradingPage() {
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown')
  const [positions, setPositions] = useState<Position[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [account, setAccount] = useState<AccountSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [apiKey, setApiKey] = useState('dev-trading-key')
  const [authenticated, setAuthenticated] = useState(false)

  // Trading form state
  const [orderForm, setOrderForm] = useState({
    symbol: '',
    action: 'BUY',
    quantity: 100,
    order_type: 'LMT',
    limit_price: '',
    time_in_force: 'DAY'
  })

  const checkConnection = async () => {
    try {
      const response = await fetch('/api/trading/health', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      })
      
      if (response.status === 401) {
        setAuthenticated(false)
        return
      }
      
      const data = await response.json()
      setConnectionStatus(data.connected ? 'connected' : 'disconnected')
      setAuthenticated(true)
    } catch (err) {
      setConnectionStatus('disconnected')
      setError('Failed to connect to trading service')
    }
  }

  const loadAccountData = async () => {
    if (!authenticated) return
    
    try {
      setLoading(true)
      
      // Load positions
      const positionsResponse = await fetch('/api/trading/positions', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      })
      if (positionsResponse.ok) {
        const positionsData = await positionsResponse.json()
        setPositions(positionsData.positions || [])
      }

      // Load orders
      const ordersResponse = await fetch('/api/trading/orders', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      })
      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json()
        setOrders(ordersData.orders || [])
      }

      // Load account summary
      const accountResponse = await fetch('/api/trading/account', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      })
      if (accountResponse.ok) {
        const accountData = await accountResponse.json()
        setAccount(accountData.account)
      }

    } catch (err) {
      setError('Failed to load trading data')
    } finally {
      setLoading(false)
    }
  }

  const placeOrder = async () => {
    if (!authenticated) return

    try {
      const response = await fetch('/api/trading/order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          symbol: orderForm.symbol.toUpperCase(),
          action: orderForm.action,
          quantity: orderForm.quantity,
          order_type: orderForm.order_type,
          limit_price: orderForm.limit_price ? parseFloat(orderForm.limit_price) : null,
          time_in_force: orderForm.time_in_force
        })
      })

      if (response.ok) {
        const result = await response.json()
        setOrderForm({
          symbol: '',
          action: 'BUY',
          quantity: 100,
          order_type: 'LMT',
          limit_price: '',
          time_in_force: 'DAY'
        })
        loadAccountData() // Refresh data
      } else {
        const error = await response.json()
        setError(`Order failed: ${error.detail}`)
      }
    } catch (err) {
      setError('Failed to place order')
    }
  }

  const cancelOrder = async (orderId: number) => {
    if (!authenticated) return

    try {
      const response = await fetch(`/api/trading/order/${orderId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${apiKey}` }
      })

      if (response.ok) {
        loadAccountData() // Refresh data
      }
    } catch (err) {
      setError('Failed to cancel order')
    }
  }

  useEffect(() => {
    checkConnection()
  }, [apiKey])

  useEffect(() => {
    if (authenticated) {
      loadAccountData()
      const interval = setInterval(loadAccountData, 10000) // Refresh every 10 seconds
      return () => clearInterval(interval)
    }
  }, [authenticated])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value)
  }

  if (!authenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Trading Authentication</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">Trading API Key</Label>
              <Input
                id="apiKey"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter trading API key"
              />
            </div>
            <Button onClick={checkConnection} className="w-full">
              Connect to Trading
            </Button>
            {error && (
              <div className="text-red-500 text-sm text-center">{error}</div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <TrendingUp className="h-8 w-8 text-green-500" />
          <div>
            <h1 className="text-3xl font-bold">IBKR Trading</h1>
            <p className="text-gray-500 dark:text-gray-400">
              Live trading and portfolio management
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'}>
            {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
          </Badge>
          <Button onClick={loadAccountData} disabled={loading}>
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Account Summary */}
      {account && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Liquidation</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(account.net_liquidation)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Buying Power</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(account.buying_power)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cash</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(account.total_cash_value)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Day Trades Left</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{account.day_trades_remaining}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
          <CardContent className="flex items-center space-x-2 pt-6">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 lg:grid-cols-[2fr_1fr]">
        {/* Main Content */}
        <div className="space-y-6">
          {/* Positions */}
          <Card>
            <CardHeader>
              <CardTitle>Positions</CardTitle>
            </CardHeader>
            <CardContent>
              {positions.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Symbol</th>
                        <th className="text-right p-2">Position</th>
                        <th className="text-right p-2">Market Price</th>
                        <th className="text-right p-2">Market Value</th>
                        <th className="text-right p-2">Unrealized P&L</th>
                      </tr>
                    </thead>
                    <tbody>
                      {positions.map((position, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="p-2 font-mono font-semibold">{position.symbol}</td>
                          <td className="text-right p-2">{formatNumber(position.position)}</td>
                          <td className="text-right p-2">{formatCurrency(position.market_price)}</td>
                          <td className="text-right p-2">{formatCurrency(position.market_value)}</td>
                          <td className={`text-right p-2 ${position.unrealized_pnl >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {formatCurrency(position.unrealized_pnl)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">No positions</div>
              )}
            </CardContent>
          </Card>

          {/* Orders */}
          <Card>
            <CardHeader>
              <CardTitle>Open Orders</CardTitle>
            </CardHeader>
            <CardContent>
              {orders.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Symbol</th>
                        <th className="text-left p-2">Action</th>
                        <th className="text-right p-2">Quantity</th>
                        <th className="text-left p-2">Type</th>
                        <th className="text-right p-2">Limit Price</th>
                        <th className="text-left p-2">Status</th>
                        <th className="text-center p-2">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {orders.map((order) => (
                        <tr key={order.order_id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="p-2 font-mono font-semibold">{order.symbol}</td>
                          <td className="p-2">
                            <Badge variant={order.action === 'BUY' ? 'default' : 'secondary'}>
                              {order.action}
                            </Badge>
                          </td>
                          <td className="text-right p-2">{formatNumber(order.quantity)}</td>
                          <td className="p-2">{order.order_type}</td>
                          <td className="text-right p-2">
                            {order.limit_price ? formatCurrency(order.limit_price) : '-'}
                          </td>
                          <td className="p-2">
                            <Badge variant="outline">{order.status}</Badge>
                          </td>
                          <td className="text-center p-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => cancelOrder(order.order_id)}
                            >
                              Cancel
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">No open orders</div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Order Entry Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Place Order</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="symbol">Symbol</Label>
                <Input
                  id="symbol"
                  value={orderForm.symbol}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, symbol: e.target.value.toUpperCase() }))}
                  placeholder="e.g., AAPL"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="action">Action</Label>
                <select
                  id="action"
                  value={orderForm.action}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, action: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                >
                  <option value="BUY">Buy</option>
                  <option value="SELL">Sell</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  value={orderForm.quantity}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="orderType">Order Type</Label>
                <select
                  id="orderType"
                  value={orderForm.order_type}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, order_type: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                >
                  <option value="MKT">Market</option>
                  <option value="LMT">Limit</option>
                </select>
              </div>
              
              {orderForm.order_type === 'LMT' && (
                <div className="space-y-2">
                  <Label htmlFor="limitPrice">Limit Price</Label>
                  <Input
                    id="limitPrice"
                    type="number"
                    step="0.01"
                    value={orderForm.limit_price}
                    onChange={(e) => setOrderForm(prev => ({ ...prev, limit_price: e.target.value }))}
                    placeholder="0.00"
                  />
                </div>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="timeInForce">Time in Force</Label>
                <select
                  id="timeInForce"
                  value={orderForm.time_in_force}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, time_in_force: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                >
                  <option value="DAY">Day</option>
                  <option value="GTC">Good Till Cancelled</option>
                </select>
              </div>
              
              <Button
                onClick={placeOrder}
                className="w-full"
                disabled={!orderForm.symbol || !orderForm.quantity || connectionStatus !== 'connected'}
              >
                Place Order
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}