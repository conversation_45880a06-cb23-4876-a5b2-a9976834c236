'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { FileText, Calendar, Tag, Building2, AlertCircle, Clock, CheckCircle, Eye, Download } from 'lucide-react'

interface DocumentData {
  id: string
  document_type: string
  company_code: string
  created_at: string
  updated_at: string
  discovery: any
  extraction: any
  categorization: any
  enrichment: any
  analysis: any
  market_analysis: any
  signals: any
  pipeline: any
}

interface DocumentDetailsResponse {
  document: DocumentData
}

export default function DocumentDetailsPage() {
  const params = useParams()
  const documentId = params.id as string
  const [data, setData] = useState<DocumentDetailsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    const fetchDocumentData = async () => {
      try {
        const response = await fetch(`/api/search/document/${documentId}`)
        if (response.ok) {
          const result = await response.json()
          setData(result)
        } else {
          setError('Failed to fetch document data')
        }
      } catch (err) {
        setError('Network error')
      } finally {
        setLoading(false)
      }
    }

    if (documentId) {
      fetchDocumentData()
    }
  }, [documentId])

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStageColor = (stage: string) => {
    const colors: Record<string, string> = {
      'discovery': 'bg-blue-500',
      'extraction': 'bg-green-500',
      'categorization': 'bg-purple-500',
      'enrichment': 'bg-cyan-500',
      'analysis': 'bg-orange-500',
      'market_intelligence': 'bg-indigo-500',
      'trading_signals': 'bg-pink-500',
      'storage': 'bg-gray-500',
      'prediction': 'bg-rose-500',
      'completed': 'bg-emerald-500'
    }
    return colors[stage] || 'bg-gray-400'
  }

  const renderJsonData = (data: any, title: string) => {
    if (!data) return null
    
    return (
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{title}</h4>
        <pre className="text-sm text-gray-600 dark:text-gray-400 overflow-x-auto whitespace-pre-wrap">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-6"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            <div className="h-4 bg-gray-300 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
          <AlertCircle className="h-5 w-5" />
          <span>{error || 'Document not found'}</span>
        </div>
      </div>
    )
  }

  const { document } = data
  const pipeline = document.pipeline || {}
  const validationGates = pipeline.validation_gates_passed || []

  return (
    <div className="p-6 space-y-6">
      {/* Document Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
              <FileText className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {document.discovery?.title || `Document ${document.id.substring(0, 8)}`}
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
                ID: {document.id}
              </p>
              <div className="flex flex-wrap gap-4 mt-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {document.company_code || 'Unknown Company'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {document.document_type || 'Unknown Type'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Created: {formatDate(document.created_at)}
                  </span>
                </div>
                {document.updated_at && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Updated: {formatDate(document.updated_at)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white ${getStageColor(pipeline.current_stage)}`}
            >
              {pipeline.current_stage || 'unknown'}
            </span>
          </div>
        </div>
      </div>

      {/* Pipeline Status */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Pipeline Status
        </h2>
        <div className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Current Stage</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {pipeline.current_stage || 'Unknown'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Current Queue</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {pipeline.current_queue || 'Unknown'}
              </p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Validation Gates Passed</p>
            <div className="flex flex-wrap gap-2">
              {['discovery', 'extraction', 'enrichment', 'categorization', 'analysis', 'market_analysis', 'signals', 'storage', 'prediction'].map((gate) => (
                <span
                  key={gate}
                  className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                    validationGates.includes(gate)
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  }`}
                >
                  {validationGates.includes(gate) ? (
                    <CheckCircle className="h-3 w-3 mr-1" />
                  ) : (
                    <Clock className="h-3 w-3 mr-1" />
                  )}
                  {gate}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Document Content Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'extraction', label: 'Extraction' },
              { id: 'categorization', label: 'Categorization' },
              { id: 'analysis', label: 'Analysis' },
              { id: 'signals', label: 'Trading Signals' },
              { id: 'raw', label: 'Raw Data' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {document.categorization && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Document Category
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Primary Category</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {document.categorization.primary_category || 'Unknown'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Confidence</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {document.categorization.confidence ? `${(document.categorization.confidence * 100).toFixed(1)}%` : 'Unknown'}
                      </p>
                    </div>
                  </div>
                </div>
              )}
              
              {document.analysis?.Executive_Summary && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Executive Summary
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    {document.analysis.Executive_Summary}
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'extraction' && (
            <div className="space-y-4">
              {document.extraction?.text && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Extracted Text
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {document.extraction.text.substring(0, 2000)}
                      {document.extraction.text.length > 2000 && '...'}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'categorization' && renderJsonData(document.categorization, 'Categorization Data')}
          {activeTab === 'analysis' && renderJsonData(document.analysis, 'Analysis Data')}
          {activeTab === 'signals' && renderJsonData(document.signals, 'Trading Signals Data')}
          {activeTab === 'raw' && renderJsonData(document, 'Complete Document Data')}
        </div>
      </div>
    </div>
  )
}