// ABOUTME: IBKR options API endpoint for retrieving options chain and flow data
// ABOUTME: Proxies requests to real IBKR data API service

import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

const IBKR_DATA_API_URL = process.env.IBKR_DATA_API_URL || 'http://localhost:8028'

export async function GET(
  request: NextRequest,
  { params }: { params: { symbol: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = params.symbol.toUpperCase()
    const expiry = searchParams.get('expiry') || ''
    const type = searchParams.get('type') || 'all'

    // Forward request to IBKR data API
    const response = await fetch(
      `${IBKR_DATA_API_URL}/api/ibkr/options/${symbol}?expiry=${expiry}&type=${type}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )

    if (!response.ok) {
      throw new Error(`IBKR API responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)

  } catch (error) {
    console.error('IBKR options API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch options data from IBKR', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}