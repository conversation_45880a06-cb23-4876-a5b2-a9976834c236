// ABOUTME: IBKR price API endpoint for retrieving current stock price data
// ABOUTME: Proxies requests to real IBKR data API service

import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

const IBKR_DATA_API_URL = process.env.IBKR_DATA_API_URL || 'http://localhost:8028'

export async function GET(
  request: NextRequest,
  { params }: { params: { symbol: string } }
) {
  try {
    const symbol = params.symbol.toUpperCase()

    // Forward request to IBKR data API
    const response = await fetch(`${IBKR_DATA_API_URL}/api/ibkr/price/${symbol}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`IBKR API responded with status: ${response.status}`)
    }

    const data = await response.json()
    
    // Map IBKR data format to expected format
    const priceData = {
      symbol,
      last: data.price || 0,
      bid: data.bid || 0,
      ask: data.ask || 0,
      open: data.dayOpen || 0,
      high: data.dayHigh || 0,
      low: data.dayLow || 0,
      previousClose: data.previousClose || 0,
      change: data.change || 0,
      changePercent: data.changePercent || 0,
      volume: data.volume || 0,
      timestamp: data.timestamp || new Date().toISOString()
    }

    return NextResponse.json(priceData)

  } catch (error) {
    console.error('IBKR price API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch price from IBKR', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}