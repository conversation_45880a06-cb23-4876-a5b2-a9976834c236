// ABOUTME: IBKR historical price API endpoint for retrieving historical stock data
// ABOUTME: Proxies requests to real IBKR data API service

import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

const IBKR_DATA_API_URL = process.env.IBKR_DATA_API_URL || 'http://localhost:8028'

export async function GET(
  request: NextRequest,
  { params }: { params: { symbol: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '3M'
    const interval = searchParams.get('interval') || '1d'
    const symbol = params.symbol.toUpperCase()

    // Forward request to IBKR data API
    const response = await fetch(
      `${IBKR_DATA_API_URL}/api/ibkr/historical/${symbol}?period=${period}&interval=${interval}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )

    if (!response.ok) {
      throw new Error(`IBKR API responded with status: ${response.status}`)
    }

    const data = await response.json()
    
    // Transform data to match expected format if needed
    const historicalData = {
      symbol: data.symbol || symbol,
      period: data.period || period,
      data: data.data || [],
      count: data.data ? data.data.length : 0
    }

    return NextResponse.json(historicalData)

  } catch (error) {
    console.error('IBKR historical API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch historical data from IBKR', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}