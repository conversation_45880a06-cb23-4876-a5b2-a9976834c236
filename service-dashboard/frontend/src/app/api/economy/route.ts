import { NextResponse } from 'next/server'

const FINANCIAL_DATA_API_URL = process.env.FINANCIAL_DATA_API_URL || 'http://localhost:8090'

export async function GET() {
  try {
    // Fetch Japanese economic indicators from our financial data API
    const response = await fetch(`${FINANCIAL_DATA_API_URL}/api/financial-data/economic-indicators/JP`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add timeout
      signal: AbortSignal.timeout(30000)
    })
    
    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json({
          error: 'Economic data not available',
          message: 'Japanese economic indicators are not currently available from configured sources',
          status: 'not_available',
          financial_api_status: response.status
        }, { status: 404 })
      }
      
      throw new Error(`Financial Data API responded with status: ${response.status}`)
    }
    
    const economicData = await response.json()
    
    // Transform the data into the format expected by the frontend
    const result = {
      japan_economy: [
        {
          name: 'Policy Rate',
          value: economicData.policy_rate ?? 'N/A',
          unit: '%',
          description: 'Bank of Japan policy rate',
          lastUpdated: economicData.timestamp
        },
        {
          name: 'USD/JPY Exchange Rate', 
          value: economicData.currency_rate ?? 'N/A',
          unit: 'JPY',
          description: 'Japanese Yen vs US Dollar',
          lastUpdated: economicData.timestamp
        },
        {
          name: economicData.stock_index_name || 'Stock Index',
          value: economicData.stock_index ?? 'N/A',
          unit: 'points',
          description: 'Japanese stock market index',
          lastUpdated: economicData.timestamp
        },
        {
          name: '10-Year Bond Yield',
          value: economicData.bond_yield_10y ?? 'N/A', 
          unit: '%',
          description: 'Japanese government bond yield',
          lastUpdated: economicData.timestamp
        }
      ],
      last_updated: economicData.timestamp || new Date().toISOString(),
      data_sources: economicData.source_urls || [],
      provider: economicData.provider || 'Unknown'
    }
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error('Error fetching economic data:', error)
    
    // Check if it's a timeout or connection error
    if (error instanceof Error && (error.name === 'AbortError' || error.message.includes('timeout'))) {
      return NextResponse.json({
        error: 'Request timeout',
        message: 'Financial Data API request timed out. Service may be starting up or overloaded.',
        status: 'timeout'
      }, { status: 504 })
    }
    
    return NextResponse.json({
      error: 'Failed to fetch economic data',
      message: error instanceof Error ? error.message : 'Unknown error',
      status: 'error',
      details: 'Check that Financial Data API is running on port 8090'
    }, { status: 500 })
  }
}

// Example of what the response would look like when properly implemented:
/*
interface EconomicDataResponse {
  us_economy: {
    gdp_growth: number
    inflation_rate: number
    unemployment_rate: number
    fed_funds_rate: number
    treasury_10y: number
    sp500_level: number
    dollar_index: number
  }
  japan_economy: {
    gdp_growth: number
    inflation_rate: number
    unemployment_rate: number
    boj_rate: number
    jgb_10y: number
    nikkei_level: number
    usdjpy_rate: number
  }
  global_markets: {
    oil_wti: number
    gold: number
    bitcoin: number
    euro_usd: number
  }
  last_updated: string
  data_sources: string[]
}
*/