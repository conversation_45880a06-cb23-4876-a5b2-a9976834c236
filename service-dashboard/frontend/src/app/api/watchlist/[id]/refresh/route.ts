// ABOUTME: Market data refresh API for updating cached market data for watchlist items
// ABOUTME: Fetches fresh market data and updates the market cache table

import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

const pool = new Pool({
  host: process.env.POSTGRES_HOST || process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || process.env.DB_PORT || '5435'),
  database: process.env.POSTGRES_DB || process.env.DB_NAME || 'omotesamba',
  user: process.env.POSTGRES_USER || process.env.DB_USER || 'omotesamba',
  password: process.env.POSTGRES_PASSWORD || process.env.DB_PASSWORD || 'omotesamba',
})

// Helper function to determine data source for a symbol
function getDataSourceForSymbol(symbol: string): string {
  // Japanese stocks
  if (symbol.endsWith('.T') || (symbol.length === 4 && /^\d+$/.test(symbol))) {
    return 'jquants'
  }
  // Default to yahoo for international stocks
  return 'yahoo'
}

// Helper function to fetch market data from external APIs
async function fetchMarketData(symbol: string, dataSource: string) {
  const baseUrl = process.env.FINANCIAL_DATA_API_URL || 'http://localhost:8090'
  
  try {
    let url: string
    
    switch (dataSource) {
      case 'jquants':
        url = `${baseUrl}/jquants/stocks/${symbol}/price`
        break
      case 'ibkr':
        url = `${baseUrl}/ibkr/price/${symbol}`
        break
      case 'yahoo':
      default:
        url = `${baseUrl}/yahoo/stock-info/${symbol}`
        break
    }

    const response = await fetch(url, { 
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    
    // Normalize data structure across different sources
    return {
      price: data.price || data.regularMarketPrice || data.last || 0,
      change_amount: data.change || data.regularMarketChange || 0,
      change_percent: data.changePercent || (data.regularMarketChangePercent * 100) || 0,
      market_cap: data.marketCap || 0,
      pe_ratio: data.pe || data.peRatio || 0,
      dividend_yield: (data.dividendYield || 0) * (data.dividendYield < 1 ? 100 : 1), // Convert to percentage
      currency: data.currency || (dataSource === 'jquants' ? 'JPY' : 'USD'),
      error_message: null
    }
  } catch (error) {
    console.error(`Error fetching market data for ${symbol}:`, error)
    return {
      price: null,
      change_amount: null,
      change_percent: null,
      market_cap: null,
      pe_ratio: null,
      dividend_yield: null,
      currency: dataSource === 'jquants' ? 'JPY' : 'USD',
      error_message: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// POST /api/watchlist/[id]/refresh - Refresh market data for watchlist
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id') || 'default_user'

    const client = await pool.connect()
    
    try {
      // Verify watchlist ownership
      const watchlistCheck = await client.query(
        'SELECT id, name FROM user_watchlists WHERE id = $1 AND user_id = $2',
        [params.id, userId]
      )

      if (watchlistCheck.rows.length === 0) {
        return NextResponse.json(
          { error: 'Watchlist not found or access denied' },
          { status: 404 }
        )
      }

      const watchlistName = watchlistCheck.rows[0].name

      // Get all symbols in this watchlist
      const itemsResult = await client.query(
        'SELECT symbol, data_source FROM watchlist_items WHERE watchlist_id = $1',
        [params.id]
      )

      if (itemsResult.rows.length === 0) {
        return NextResponse.json({
          message: 'No items to refresh',
          watchlist_name: watchlistName,
          updated_count: 0,
          failed_count: 0,
          details: []
        })
      }

      const results = []
      let updatedCount = 0
      let failedCount = 0

      // Fetch market data for each symbol
      for (const item of itemsResult.rows) {
        const { symbol, data_source } = item
        const effectiveDataSource = data_source || getDataSourceForSymbol(symbol)
        
        const marketData = await fetchMarketData(symbol, effectiveDataSource)
        
        try {
          // Upsert market data into cache
          await client.query(`
            INSERT INTO watchlist_market_cache 
            (symbol, price, change_amount, change_percent, market_cap, pe_ratio, dividend_yield, currency, data_source, error_message)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            ON CONFLICT (symbol) DO UPDATE SET
              price = EXCLUDED.price,
              change_amount = EXCLUDED.change_amount,
              change_percent = EXCLUDED.change_percent,
              market_cap = EXCLUDED.market_cap,
              pe_ratio = EXCLUDED.pe_ratio,
              dividend_yield = EXCLUDED.dividend_yield,
              currency = EXCLUDED.currency,
              data_source = EXCLUDED.data_source,
              error_message = EXCLUDED.error_message,
              last_updated = CURRENT_TIMESTAMP
          `, [
            symbol,
            marketData.price,
            marketData.change_amount,
            marketData.change_percent,
            marketData.market_cap,
            marketData.pe_ratio,
            marketData.dividend_yield,
            marketData.currency,
            effectiveDataSource,
            marketData.error_message
          ])

          if (marketData.error_message) {
            failedCount++
            results.push({
              symbol,
              status: 'failed',
              error: marketData.error_message
            })
          } else {
            updatedCount++
            results.push({
              symbol,
              status: 'success',
              price: marketData.price,
              change_percent: marketData.change_percent
            })
          }
        } catch (dbError) {
          failedCount++
          results.push({
            symbol,
            status: 'failed',
            error: 'Database update failed'
          })
          console.error(`Database update failed for ${symbol}:`, dbError)
        }
      }

      return NextResponse.json({
        message: `Refreshed market data for ${itemsResult.rows.length} symbols`,
        watchlist_name: watchlistName,
        updated_count: updatedCount,
        failed_count: failedCount,
        details: results
      })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error refreshing watchlist market data:', error)
    return NextResponse.json(
      { error: 'Failed to refresh market data' },
      { status: 500 }
    )
  }
}