// ABOUTME: Individual watchlist management API for specific watchlist operations
// ABOUTME: Handles GET (with items), PUT (update), DELETE for specific watchlist

import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

const pool = new Pool({
  host: process.env.POSTGRES_HOST || process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || process.env.DB_PORT || '5435'),
  database: process.env.POSTGRES_DB || process.env.DB_NAME || 'omotesamba',
  user: process.env.POSTGRES_USER || process.env.DB_USER || 'omotesamba',
  password: process.env.POSTGRES_PASSWORD || process.env.DB_PASSWORD || 'omotesamba',
})

// GET /api/watchlist/[id] - Get specific watchlist with items
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id') || 'default_user'
    const includeMarketData = searchParams.get('include_market_data') === 'true'

    const client = await pool.connect()
    
    try {
      // Get watchlist details
      const watchlistResult = await client.query(`
        SELECT id, name, description, is_default, created_at, updated_at
        FROM user_watchlists 
        WHERE id = $1 AND user_id = $2
      `, [params.id, userId])

      if (watchlistResult.rows.length === 0) {
        return NextResponse.json(
          { error: 'Watchlist not found' },
          { status: 404 }
        )
      }

      const watchlist = watchlistResult.rows[0]

      // Get watchlist items
      let itemsQuery = `
        SELECT 
          wi.id,
          wi.symbol,
          wi.company_name,
          wi.data_source,
          wi.added_at,
          wi.notes,
          wi.sort_order
        FROM watchlist_items wi
        WHERE wi.watchlist_id = $1
        ORDER BY wi.sort_order ASC, wi.added_at ASC
      `

      // Include market data if requested
      if (includeMarketData) {
        itemsQuery = `
          SELECT 
            wi.id,
            wi.symbol,
            wi.company_name,
            wi.data_source,
            wi.added_at,
            wi.notes,
            wi.sort_order,
            wmc.price,
            wmc.change_amount,
            wmc.change_percent,
            wmc.market_cap,
            wmc.pe_ratio,
            wmc.dividend_yield,
            wmc.currency,
            wmc.last_updated as market_data_updated,
            wmc.error_message
          FROM watchlist_items wi
          LEFT JOIN watchlist_market_cache wmc ON wi.symbol = wmc.symbol
          WHERE wi.watchlist_id = $1
          ORDER BY wi.sort_order ASC, wi.added_at ASC
        `
      }

      const itemsResult = await client.query(itemsQuery, [params.id])

      return NextResponse.json({
        watchlist: {
          ...watchlist,
          items: itemsResult.rows
        }
      })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching watchlist:', error)
    return NextResponse.json(
      { error: 'Failed to fetch watchlist' },
      { status: 500 }
    )
  }
}

// PUT /api/watchlist/[id] - Update watchlist details
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()
    const { name, description, user_id = 'default_user' } = body

    if (name && (typeof name !== 'string' || name.trim().length === 0)) {
      return NextResponse.json(
        { error: 'Name must be non-empty if provided' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      // Verify ownership
      const ownershipCheck = await client.query(
        'SELECT id FROM user_watchlists WHERE id = $1 AND user_id = $2',
        [params.id, user_id]
      )

      if (ownershipCheck.rows.length === 0) {
        return NextResponse.json(
          { error: 'Watchlist not found or access denied' },
          { status: 404 }
        )
      }

      // Check for name conflicts if name is being updated
      if (name) {
        const conflictCheck = await client.query(
          'SELECT id FROM user_watchlists WHERE user_id = $1 AND name = $2 AND id != $3',
          [user_id, name.trim(), params.id]
        )

        if (conflictCheck.rows.length > 0) {
          return NextResponse.json(
            { error: 'Watchlist with this name already exists' },
            { status: 409 }
          )
        }
      }

      // Build dynamic update query
      const updates = []
      const values = []
      let paramIndex = 1

      if (name !== undefined) {
        updates.push(`name = $${paramIndex}`)
        values.push(name.trim())
        paramIndex++
      }

      if (description !== undefined) {
        updates.push(`description = $${paramIndex}`)
        values.push(description || '')
        paramIndex++
      }

      if (updates.length === 0) {
        return NextResponse.json(
          { error: 'No fields to update' },
          { status: 400 }
        )
      }

      values.push(params.id)
      const updateQuery = `
        UPDATE user_watchlists 
        SET ${updates.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, name, description, is_default, created_at, updated_at
      `

      const result = await client.query(updateQuery, values)

      return NextResponse.json({
        watchlist: result.rows[0],
        message: 'Watchlist updated successfully'
      })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error updating watchlist:', error)
    return NextResponse.json(
      { error: 'Failed to update watchlist' },
      { status: 500 }
    )
  }
}

// DELETE /api/watchlist/[id] - Delete watchlist
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id') || 'default_user'

    const client = await pool.connect()
    
    try {
      // Verify ownership and check if it's default
      const watchlistCheck = await client.query(
        'SELECT is_default FROM user_watchlists WHERE id = $1 AND user_id = $2',
        [params.id, userId]
      )

      if (watchlistCheck.rows.length === 0) {
        return NextResponse.json(
          { error: 'Watchlist not found or access denied' },
          { status: 404 }
        )
      }

      if (watchlistCheck.rows[0].is_default) {
        return NextResponse.json(
          { error: 'Cannot delete default watchlist' },
          { status: 400 }
        )
      }

      // Delete watchlist (items will be deleted via CASCADE)
      await client.query(
        'DELETE FROM user_watchlists WHERE id = $1',
        [params.id]
      )

      return NextResponse.json({
        message: 'Watchlist deleted successfully'
      })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error deleting watchlist:', error)
    return NextResponse.json(
      { error: 'Failed to delete watchlist' },
      { status: 500 }
    )
  }
}