// ABOUTME: Watchlist items management API for adding/removing symbols from watchlists
// ABOUTME: Handles POST (add item), DELETE (remove item), PUT (reorder items)

import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

const pool = new Pool({
  host: process.env.POSTGRES_HOST || process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || process.env.DB_PORT || '5435'),
  database: process.env.POSTGRES_DB || process.env.DB_NAME || 'omotesamba',
  user: process.env.POSTGRES_USER || process.env.DB_USER || 'omotesamba',
  password: process.env.POSTGRES_PASSWORD || process.env.DB_PASSWORD || 'omotesamba',
})

// POST /api/watchlist/[id]/items - Add item to watchlist
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()
    const { symbol, company_name, data_source = 'yahoo', notes = '', user_id = 'default_user' } = body

    if (!symbol || typeof symbol !== 'string' || symbol.trim().length === 0) {
      return NextResponse.json(
        { error: 'Symbol is required and must be non-empty' },
        { status: 400 }
      )
    }

    if (data_source && !['jquants', 'yahoo', 'ibkr'].includes(data_source)) {
      return NextResponse.json(
        { error: 'Invalid data source. Must be one of: jquants, yahoo, ibkr' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      // Verify watchlist ownership
      const watchlistCheck = await client.query(
        'SELECT id FROM user_watchlists WHERE id = $1 AND user_id = $2',
        [params.id, user_id]
      )

      if (watchlistCheck.rows.length === 0) {
        return NextResponse.json(
          { error: 'Watchlist not found or access denied' },
          { status: 404 }
        )
      }

      // Check if symbol already exists in this watchlist
      const duplicateCheck = await client.query(
        'SELECT id FROM watchlist_items WHERE watchlist_id = $1 AND symbol = $2',
        [params.id, symbol.trim().toUpperCase()]
      )

      if (duplicateCheck.rows.length > 0) {
        return NextResponse.json(
          { error: 'Symbol already exists in this watchlist' },
          { status: 409 }
        )
      }

      // Get next sort order
      const sortOrderResult = await client.query(
        'SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM watchlist_items WHERE watchlist_id = $1',
        [params.id]
      )
      const nextSortOrder = sortOrderResult.rows[0].next_order

      // Add item to watchlist
      const result = await client.query(`
        INSERT INTO watchlist_items (watchlist_id, symbol, company_name, data_source, notes, sort_order)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, symbol, company_name, data_source, added_at, notes, sort_order
      `, [
        params.id, 
        symbol.trim().toUpperCase(), 
        company_name || '', 
        data_source, 
        notes, 
        nextSortOrder
      ])

      return NextResponse.json({
        item: result.rows[0],
        message: 'Item added to watchlist successfully'
      }, { status: 201 })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error adding item to watchlist:', error)
    return NextResponse.json(
      { error: 'Failed to add item to watchlist' },
      { status: 500 }
    )
  }
}

// PUT /api/watchlist/[id]/items - Reorder items in watchlist
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()
    const { items, user_id = 'default_user' } = body

    if (!Array.isArray(items)) {
      return NextResponse.json(
        { error: 'Items must be an array' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      // Verify watchlist ownership
      const watchlistCheck = await client.query(
        'SELECT id FROM user_watchlists WHERE id = $1 AND user_id = $2',
        [params.id, user_id]
      )

      if (watchlistCheck.rows.length === 0) {
        return NextResponse.json(
          { error: 'Watchlist not found or access denied' },
          { status: 404 }
        )
      }

      // Update sort orders in transaction
      await client.query('BEGIN')

      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (!item.id) {
          await client.query('ROLLBACK')
          return NextResponse.json(
            { error: 'All items must have an ID' },
            { status: 400 }
          )
        }

        await client.query(
          'UPDATE watchlist_items SET sort_order = $1 WHERE id = $2 AND watchlist_id = $3',
          [i + 1, item.id, params.id]
        )
      }

      await client.query('COMMIT')

      return NextResponse.json({
        message: 'Items reordered successfully'
      })
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error reordering items:', error)
    return NextResponse.json(
      { error: 'Failed to reorder items' },
      { status: 500 }
    )
  }
}