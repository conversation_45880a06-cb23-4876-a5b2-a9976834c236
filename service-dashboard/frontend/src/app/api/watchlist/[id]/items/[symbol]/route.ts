// ABOUTME: Individual watchlist item management for removing specific symbols
// ABOUTME: Handles DELETE operations for specific symbols in a watchlist

import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

const pool = new Pool({
  host: process.env.POSTGRES_HOST || process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || process.env.DB_PORT || '5435'),
  database: process.env.POSTGRES_DB || process.env.DB_NAME || 'omotesamba',
  user: process.env.POSTGRES_USER || process.env.DB_USER || 'omotesamba',
  password: process.env.POSTGRES_PASSWORD || process.env.DB_PASSWORD || 'omotesamba',
})

// DELETE /api/watchlist/[id]/items/[symbol] - Remove item from watchlist
export async function DELETE(
  request: NextRequest, 
  { params }: { params: { id: string; symbol: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id') || 'default_user'

    const client = await pool.connect()
    
    try {
      // Verify watchlist ownership
      const watchlistCheck = await client.query(
        'SELECT id FROM user_watchlists WHERE id = $1 AND user_id = $2',
        [params.id, userId]
      )

      if (watchlistCheck.rows.length === 0) {
        return NextResponse.json(
          { error: 'Watchlist not found or access denied' },
          { status: 404 }
        )
      }

      // Check if item exists in this watchlist
      const itemCheck = await client.query(
        'SELECT id FROM watchlist_items WHERE watchlist_id = $1 AND symbol = $2',
        [params.id, params.symbol.toUpperCase()]
      )

      if (itemCheck.rows.length === 0) {
        return NextResponse.json(
          { error: 'Item not found in this watchlist' },
          { status: 404 }
        )
      }

      // Delete the item
      await client.query(
        'DELETE FROM watchlist_items WHERE watchlist_id = $1 AND symbol = $2',
        [params.id, params.symbol.toUpperCase()]
      )

      return NextResponse.json({
        message: 'Item removed from watchlist successfully'
      })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error removing item from watchlist:', error)
    return NextResponse.json(
      { error: 'Failed to remove item from watchlist' },
      { status: 500 }
    )
  }
}