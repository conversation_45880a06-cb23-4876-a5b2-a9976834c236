// ABOUTME: Main watchlist API for CRUD operations on user watchlists
// ABOUTME: Handles GET (list all), POST (create new), PUT (update), DELETE operations

import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

// Database connection
const pool = new Pool({
  host: process.env.POSTGRES_HOST || process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || process.env.DB_PORT || '5435'),
  database: process.env.POSTGRES_DB || process.env.DB_NAME || 'omotesamba',
  user: process.env.POSTGRES_USER || process.env.DB_USER || 'omotesamba',
  password: process.env.POSTGRES_PASSWORD || process.env.DB_PASSWORD || 'omotesamba',
})

// GET /api/watchlist - Get all watchlists for user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id') || 'default_user'

    const client = await pool.connect()
    
    try {
      // Get watchlists with item counts
      const result = await client.query(`
        SELECT 
          w.id,
          w.name,
          w.description,
          w.is_default,
          w.created_at,
          w.updated_at,
          COUNT(wi.id) as item_count
        FROM user_watchlists w
        LEFT JOIN watchlist_items wi ON w.id = wi.watchlist_id
        WHERE w.user_id = $1
        GROUP BY w.id, w.name, w.description, w.is_default, w.created_at, w.updated_at
        ORDER BY w.is_default DESC, w.created_at ASC
      `, [userId])

      return NextResponse.json({
        watchlists: result.rows,
        total: result.rows.length
      })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching watchlists:', error)
    return NextResponse.json(
      { error: 'Failed to fetch watchlists' },
      { status: 500 }
    )
  }
}

// POST /api/watchlist - Create new watchlist
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, user_id = 'default_user' } = body

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Name is required and must be non-empty' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      // Check if watchlist name already exists for user
      const existingCheck = await client.query(
        'SELECT id FROM user_watchlists WHERE user_id = $1 AND name = $2',
        [user_id, name.trim()]
      )

      if (existingCheck.rows.length > 0) {
        return NextResponse.json(
          { error: 'Watchlist with this name already exists' },
          { status: 409 }
        )
      }

      // Create new watchlist
      const result = await client.query(`
        INSERT INTO user_watchlists (user_id, name, description)
        VALUES ($1, $2, $3)
        RETURNING id, name, description, is_default, created_at, updated_at
      `, [user_id, name.trim(), description || ''])

      return NextResponse.json({
        watchlist: result.rows[0],
        message: 'Watchlist created successfully'
      }, { status: 201 })
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error creating watchlist:', error)
    return NextResponse.json(
      { error: 'Failed to create watchlist' },
      { status: 500 }
    )
  }
}