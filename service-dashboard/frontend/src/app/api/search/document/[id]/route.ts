import { NextRequest, NextResponse } from 'next/server'

const SEARCH_API_URL = process.env.SEARCH_API_URL || process.env.NEXT_PUBLIC_SEARCH_API_URL || 'http://localhost:8028'

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json({ error: 'Document ID required' }, { status: 400 })
    }

    // Forward the request to our search API
    const response = await fetch(`${SEARCH_API_URL}/api/search/document/${encodeURIComponent(id)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json({ error: 'Document not found' }, { status: 404 })
      }
      throw new Error(`Search API responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Document details API error:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}