import { NextRequest, NextResponse } from 'next/server'

const SEARCH_API_URL = process.env.SEARCH_API_URL || process.env.NEXT_PUBLIC_SEARCH_API_URL || 'http://localhost:8028'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    if (!query) {
      return NextResponse.json({ error: 'Query parameter required', results: [] }, { status: 400 })
    }

    // Forward the request to our search API
    const response = await fetch(`${SEARCH_API_URL}/api/search?q=${encodeURIComponent(query)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Search API responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      results: [] 
    }, { status: 500 })
  }
}