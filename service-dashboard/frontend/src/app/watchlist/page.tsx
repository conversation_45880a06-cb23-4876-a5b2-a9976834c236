'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow 
} from '@/components/ui/table'
import { 
  BarChart3, TrendingUp, TrendingDown, Plus, Trash2, Edit3, 
  RefreshC<PERSON>, Download, Search, Info, Star, List, Eye, Settings
} from 'lucide-react'
import { dataService, type CompanyData } from '@/lib/data-service'

// Database-compatible interfaces
interface WatchlistItem {
  id: string
  symbol: string
  company_name: string
  data_source: 'jquants' | 'yahoo' | 'ibkr'
  added_at: string
  notes?: string
  sort_order: number
  // Market data (from cache table)
  price?: number
  change_amount?: number
  change_percent?: number
  market_cap?: number
  pe_ratio?: number
  dividend_yield?: number
  currency?: string
  market_data_updated?: string
  error_message?: string
}

interface Watchlist {
  id: string
  name: string
  description: string
  is_default: boolean
  created_at: string
  updated_at: string
  item_count?: number
  items?: WatchlistItem[]
}

export default function WatchlistPage() {
  const [watchlists, setWatchlists] = useState<Watchlist[]>([])
  const [activeWatchlist, setActiveWatchlist] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [newSymbol, setNewSymbol] = useState('')
  const [isCreatingWatchlist, setIsCreatingWatchlist] = useState(false)
  const [newWatchlistName, setNewWatchlistName] = useState('')
  const [newWatchlistDescription, setNewWatchlistDescription] = useState('')
  
  useEffect(() => {
    loadWatchlists()
  }, [])

  const loadWatchlists = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await dataService.getWatchlists()
      setWatchlists(response.watchlists)
      
      // Set default active watchlist (first one, preferring default)
      if (response.watchlists.length > 0) {
        const defaultWatchlist = response.watchlists.find(w => w.is_default) || response.watchlists[0]
        setActiveWatchlist(defaultWatchlist.id)
      }
    } catch (error) {
      console.error('Error loading watchlists:', error)
      setError('Failed to load watchlists')
    } finally {
      setLoading(false)
    }
  }

  const loadWatchlistData = async (watchlistId: string) => {
    try {
      const response = await dataService.getWatchlist(watchlistId, true)
      
      // Update the specific watchlist in our state
      setWatchlists(prev => prev.map(wl => 
        wl.id === watchlistId 
          ? { ...response.watchlist, item_count: response.watchlist.items?.length || 0 }
          : wl
      ))
    } catch (error) {
      console.error('Error loading watchlist data:', error)
      setError('Failed to load watchlist data')
    }
  }

  const addToWatchlist = async (watchlistId: string, symbol: string, shouldRefresh: boolean = true) => {
    setLoading(true)
    setError(null)

    try {
      await dataService.addToWatchlist(watchlistId, symbol)
      
      // Reload the specific watchlist to get updated data
      await loadWatchlistData(watchlistId)

      if (shouldRefresh) {
        setNewSymbol('')
      }
    } catch (error) {
      console.error('Error adding to watchlist:', error)
      setError(error instanceof Error ? error.message : `Failed to add ${symbol}`)
    } finally {
      setLoading(false)
    }
  }

  const removeFromWatchlist = async (watchlistId: string, symbol: string) => {
    setLoading(true)
    setError(null)

    try {
      await dataService.removeFromWatchlist(watchlistId, symbol)
      
      // Reload the specific watchlist to get updated data
      await loadWatchlistData(watchlistId)
    } catch (error) {
      console.error('Error removing from watchlist:', error)
      setError(error instanceof Error ? error.message : `Failed to remove ${symbol}`)
    } finally {
      setLoading(false)
    }
  }

  const refreshWatchlist = async (watchlistId: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await dataService.refreshWatchlistData(watchlistId)
      
      // Show refresh results
      if (response.failed_count > 0) {
        setError(`Refreshed ${response.updated_count} symbols, ${response.failed_count} failed`)
      }
      
      // Reload the watchlist to get fresh data
      await loadWatchlistData(watchlistId)
    } catch (error) {
      console.error('Error refreshing watchlist:', error)
      setError(error instanceof Error ? error.message : 'Failed to refresh watchlist')
    } finally {
      setLoading(false)
    }
  }

  const createWatchlist = async () => {
    if (!newWatchlistName.trim()) return

    setLoading(true)
    setError(null)

    try {
      const response = await dataService.createWatchlist(newWatchlistName.trim(), newWatchlistDescription)
      
      // Reload watchlists to get the new one
      await loadWatchlists()
      setActiveWatchlist(response.watchlist.id)
      
      setIsCreatingWatchlist(false)
      setNewWatchlistName('')
      setNewWatchlistDescription('')
    } catch (error) {
      console.error('Error creating watchlist:', error)
      setError(error instanceof Error ? error.message : 'Failed to create watchlist')
    } finally {
      setLoading(false)
    }
  }

  const exportWatchlist = (watchlistId: string) => {
    const watchlist = watchlists.find(w => w.id === watchlistId)
    if (!watchlist?.items) return

    const csv = [
      ['Symbol', 'Name', 'Price', 'Change', 'Change %', 'Market Cap', 'P/E', 'Dividend Yield', 'Currency', 'Data Source'],
      ...watchlist.items.map(item => [
        item.symbol,
        item.company_name,
        item.price?.toString() || '',
        item.change_amount?.toString() || '',
        item.change_percent?.toFixed(2) || '',
        item.market_cap?.toString() || '',
        item.pe_ratio?.toString() || '',
        item.dividend_yield?.toString() || '',
        item.currency || '',
        item.data_source
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${watchlist.name.replace(/\s+/g, '-')}-watchlist-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
  }

  const formatCurrency = (value: number, currency: string): string => {
    const symbol = currency === 'JPY' ? '¥' : '$'
    if (value >= 1e12) return `${symbol}${(value / 1e12).toFixed(2)}T`
    if (value >= 1e9) return `${symbol}${(value / 1e9).toFixed(2)}B`
    if (value >= 1e6) return `${symbol}${(value / 1e6).toFixed(2)}M`
    return `${symbol}${value.toFixed(2)}`
  }

  const getDataSourceBadge = (dataSource: string) => {
    switch (dataSource) {
      case 'jquants':
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200 text-xs">🇯🇵 JQ</Badge>
      case 'yahoo':
        return <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200 text-xs">🌍 YF</Badge>
      case 'ibkr':
        return <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 text-xs">📊 IBKR</Badge>
      default:
        return <Badge variant="outline" className="text-xs">?</Badge>
    }
  }

  const handleWatchlistChange = async (watchlistId: string) => {
    setActiveWatchlist(watchlistId)
    
    // Load items if not already loaded
    const watchlist = watchlists.find(w => w.id === watchlistId)
    if (watchlist && !watchlist.items) {
      await loadWatchlistData(watchlistId)
    }
  }

  const activeWatchlistData = watchlists.find(w => w.id === activeWatchlist)

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Stock Watchlists</h1>
          <p className="text-muted-foreground mt-1">
            Monitor your favorite stocks with real-time data from JQuants (Japanese) + Yahoo Finance & IBKR (Global)
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isCreatingWatchlist} onOpenChange={setIsCreatingWatchlist}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                New Watchlist
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Watchlist</DialogTitle>
                <DialogDescription>
                  Create a custom watchlist to track specific stocks
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="watchlist-name">Watchlist Name</Label>
                  <Input
                    id="watchlist-name"
                    placeholder="e.g., High Growth Stocks"
                    value={newWatchlistName}
                    onChange={(e) => setNewWatchlistName(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="watchlist-description">Description</Label>
                  <Input
                    id="watchlist-description"
                    placeholder="Brief description of this watchlist"
                    value={newWatchlistDescription}
                    onChange={(e) => setNewWatchlistDescription(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={createWatchlist} disabled={!newWatchlistName.trim()}>
                    Create
                  </Button>
                  <Button variant="outline" onClick={() => setIsCreatingWatchlist(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Data Source Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200 text-xs">🇯🇵 JQ</Badge>
              <span>Japanese stocks via JQuants</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200 text-xs">🌍 YF</Badge>
              <span>Global stocks via Yahoo Finance</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 text-xs">📊 IBKR</Badge>
              <span>Real-time via IBKR</span>
            </div>
          </div>
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Watchlists</CardTitle>
              <CardDescription>Manage and monitor your stock portfolios</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={() => refreshWatchlist(activeWatchlist)} 
                disabled={loading || !activeWatchlist}
                size="sm"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button 
                onClick={() => exportWatchlist(activeWatchlist)} 
                disabled={!activeWatchlistData?.items.length}
                size="sm"
                variant="outline"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeWatchlist} onValueChange={handleWatchlistChange}>
            <TabsList className="grid w-full grid-cols-2">
              {watchlists.map(watchlist => (
                <TabsTrigger key={watchlist.id} value={watchlist.id}>
                  {watchlist.name} ({watchlist.item_count || watchlist.items?.length || 0})
                </TabsTrigger>
              ))}
            </TabsList>

            {watchlists.map(watchlist => (
              <TabsContent key={watchlist.id} value={watchlist.id} className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-semibold">{watchlist.name}</h3>
                    <p className="text-sm text-muted-foreground">{watchlist.description}</p>
                  </div>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add symbol (e.g., AAPL, 7203.T)"
                      value={newSymbol}
                      onChange={(e) => setNewSymbol(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addToWatchlist(watchlist.id, newSymbol)}
                      className="w-64"
                    />
                    <Button 
                      onClick={() => addToWatchlist(watchlist.id, newSymbol)}
                      disabled={loading || !newSymbol.trim()}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add
                    </Button>
                  </div>
                </div>

                {!watchlist.items || watchlist.items.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <List className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No stocks in this watchlist yet</p>
                    <p className="text-sm">Add some symbols to get started</p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Symbol</TableHead>
                        <TableHead>Company</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Change</TableHead>
                        <TableHead>Market Cap</TableHead>
                        <TableHead>P/E</TableHead>
                        <TableHead>Dividend</TableHead>
                        <TableHead>Source</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {watchlist.items.map(item => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">{item.symbol}</TableCell>
                          <TableCell>{item.company_name}</TableCell>
                          <TableCell>
                            {item.price ? formatCurrency(item.price, item.currency || 'USD') : 'N/A'}
                            {item.error_message && <span className="text-red-500 text-xs ml-1">!</span>}
                          </TableCell>
                          <TableCell>
                            {item.change_amount !== undefined && item.change_percent !== undefined ? (
                              <div className={`flex items-center gap-1 ${item.change_amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {item.change_amount >= 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                                {formatCurrency(Math.abs(item.change_amount), item.currency || 'USD')} ({item.change_percent.toFixed(2)}%)
                              </div>
                            ) : 'N/A'}
                          </TableCell>
                          <TableCell>
                            {item.market_cap ? formatCurrency(item.market_cap, item.currency || 'USD') : 'N/A'}
                          </TableCell>
                          <TableCell>{item.pe_ratio ? item.pe_ratio.toFixed(2) : 'N/A'}</TableCell>
                          <TableCell>{item.dividend_yield ? `${item.dividend_yield.toFixed(2)}%` : 'N/A'}</TableCell>
                          <TableCell>{getDataSourceBadge(item.data_source)}</TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => removeFromWatchlist(watchlist.id, item.symbol)}
                              disabled={loading}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}