'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Building2, Calendar, FileText, TrendingUp, Tag, AlertCircle, CheckCircle, Clock } from 'lucide-react'

interface CompanyData {
  code: string
  company_name: string
  company_name_english: string
  sector_17_name: string
  market_name: string
  last_updated: string
  document_count: number
  last_document_date: string | null
  symbol: string
  country: string
  currency: string
  scale_category: string
  first_listed_date: string
  data_source: string
}

interface DocumentData {
  id: string
  document_type: string
  created_at: string
  current_stage: string
  category: string
  title: string
}

interface CompanyDetailsResponse {
  company: CompanyData
  recent_documents: DocumentData[]
}

export default function CompanyDetailsPage() {
  const params = useParams()
  const companyCode = params.code as string
  const [data, setData] = useState<CompanyDetailsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        const response = await fetch(`/api/search/company/${companyCode}`)
        if (response.ok) {
          const result = await response.json()
          setData(result)
        } else {
          setError('Failed to fetch company data')
        }
      } catch (err) {
        setError('Network error')
      } finally {
        setLoading(false)
      }
    }

    if (companyCode) {
      fetchCompanyData()
    }
  }, [companyCode])

  const getStageColor = (stage: string) => {
    const colors: Record<string, string> = {
      'discovery': 'bg-blue-500',
      'extraction': 'bg-green-500',
      'categorization': 'bg-purple-500',
      'enrichment': 'bg-cyan-500',
      'analysis': 'bg-orange-500',
      'market_intelligence': 'bg-indigo-500',
      'trading_signals': 'bg-pink-500',
      'storage': 'bg-gray-500',
      'prediction': 'bg-rose-500',
      'completed': 'bg-emerald-500'
    }
    return colors[stage] || 'bg-gray-400'
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-6"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            <div className="h-4 bg-gray-300 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
          <AlertCircle className="h-5 w-5" />
          <span>{error || 'Company not found'}</span>
        </div>
      </div>
    )
  }

  const { company, recent_documents } = data

  return (
    <div className="p-6 space-y-6">
      {/* Company Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex items-start gap-4">
          <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Building2 className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {company.company_name}
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
              {company.code} • {company.symbol}
            </p>
            {company.company_name_english && (
              <p className="text-md text-gray-500 dark:text-gray-500 mt-1">
                {company.company_name_english}
              </p>
            )}
            <div className="flex flex-wrap gap-4 mt-4">
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {company.sector_17_name}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {company.market_name}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {company.document_count} documents
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Last updated: {formatDate(company.last_updated)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Documents */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Recent Documents
        </h2>
        
        {recent_documents.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No documents found for this company
          </div>
        ) : (
          <div className="space-y-3">
            {recent_documents.map((doc) => (
              <div
                key={doc.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                onClick={() => window.location.href = `/document/${doc.id}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {doc.title}
                    </h3>
                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        {doc.document_type}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(doc.created_at)}
                      </span>
                      {doc.category && (
                        <span className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          {doc.category}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white ${getStageColor(doc.current_stage)}`}
                    >
                      {doc.current_stage || 'unknown'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Company Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Documents</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {company.document_count}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center gap-3">
            <Calendar className="h-6 w-6 text-green-600 dark:text-green-400" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">First Listed</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatDate(company.first_listed_date)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Market</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {company.market_name}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Company Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Company Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Symbol:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{company.symbol}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Country:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{company.country}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Currency:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{company.currency}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Scale Category:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{company.scale_category}</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Data Source:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{company.data_source}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Sector:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{company.sector_17_name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Market:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{company.market_name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Last Updated:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{formatDate(company.last_updated)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}