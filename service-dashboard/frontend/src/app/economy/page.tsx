'use client'

import { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  BarChart3,
  Globe,
  Calendar,
  AlertCircle,
  RefreshCw
} from 'lucide-react'

interface EconomicIndicator {
  name: string
  value: number | string
  change?: number
  changeDirection?: 'up' | 'down' | 'neutral'
  lastUpdated?: string
  unit?: string
  description?: string
}

interface EconomicData {
  us_economy: EconomicIndicator[]
  japan_economy: EconomicIndicator[]
  global_markets: EconomicIndicator[]
  last_updated: string
}

export default function EconomyPage() {
  const [economicData, setEconomicData] = useState<EconomicData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  const fetchEconomicData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/economy')
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      // Transform the data into the expected format
      const economicData: EconomicData = {
        us_economy: [], // Not implemented yet
        japan_economy: data.japan_economy || [],
        global_markets: [], // Not implemented yet  
        last_updated: data.last_updated || new Date().toISOString()
      }
      
      setEconomicData(economicData)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch economic data')
      setEconomicData(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchEconomicData()
  }, [])

  const handleRefresh = () => {
    setLastRefresh(new Date())
    fetchEconomicData()
  }

  const renderIndicatorCard = (indicator: EconomicIndicator) => {
    const getChangeIcon = (direction?: string) => {
      switch (direction) {
        case 'up':
          return <TrendingUp className="h-4 w-4 text-green-500" />
        case 'down':
          return <TrendingDown className="h-4 w-4 text-red-500" />
        default:
          return <BarChart3 className="h-4 w-4 text-gray-500" />
      }
    }

    const getChangeColor = (direction?: string) => {
      switch (direction) {
        case 'up':
          return 'text-green-600 dark:text-green-400'
        case 'down':
          return 'text-red-600 dark:text-red-400'
        default:
          return 'text-gray-600 dark:text-gray-400'
      }
    }

    return (
      <div key={indicator.name} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            {indicator.name}
          </h4>
          {getChangeIcon(indicator.changeDirection)}
        </div>
        
        <div className="mb-2">
          <span className="text-2xl font-bold text-gray-900 dark:text-white">
            {typeof indicator.value === 'number' ? indicator.value.toFixed(2) : indicator.value}
            {indicator.unit && <span className="text-sm text-gray-500 ml-1">{indicator.unit}</span>}
          </span>
        </div>
        
        {indicator.change !== undefined && (
          <div className={`text-sm ${getChangeColor(indicator.changeDirection)}`}>
            {indicator.change > 0 ? '+' : ''}{indicator.change.toFixed(2)}%
          </div>
        )}
        
        {indicator.description && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            {indicator.description}
          </p>
        )}
        
        {indicator.lastUpdated && (
          <div className="text-xs text-gray-400 dark:text-gray-500 mt-2 flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            {indicator.lastUpdated}
          </div>
        )}
      </div>
    )
  }

  const renderSection = (title: string, indicators: EconomicIndicator[], icon: React.ReactNode) => (
    <div className="mb-8">
      <div className="flex items-center mb-4">
        {icon}
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-2">
          {title}
        </h3>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {indicators.map(renderIndicatorCard)}
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Economic Data</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Real-time economic indicators for US and Japan markets
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Last refresh: {lastRefresh.toLocaleTimeString()}
          </div>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading economic data...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Economic Data Unavailable
              </h3>
              <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <p>{error}</p>
                <p className="mt-2">
                  To enable economic data, please configure connections to:
                </p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Federal Reserve Economic Data (FRED) API</li>
                  <li>Bank of Japan Statistics API</li>
                  <li>Yahoo Finance or other market data provider</li>
                  <li>Trading Economics API</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Economic Data Sections */}
      {economicData && (
        <>
          {/* US Economy */}
          {renderSection(
            'United States Economy',
            economicData.us_economy,
            <DollarSign className="h-5 w-5 text-blue-600" />
          )}

          {/* Japan Economy */}
          {renderSection(
            'Japan Economy',
            economicData.japan_economy,
            <BarChart3 className="h-5 w-5 text-red-600" />
          )}

          {/* Global Markets */}
          {renderSection(
            'Global Markets',
            economicData.global_markets,
            <Globe className="h-5 w-5 text-green-600" />
          )}

          {/* Last Updated */}
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Data last updated: {economicData.last_updated}
            </p>
          </div>
        </>
      )}

      {/* Data Sources Info */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
          Economic Data Sources
        </h3>
        <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <p>• <strong>US Data:</strong> Federal Reserve Economic Data (FRED), Bureau of Labor Statistics</p>
          <p>• <strong>Japan Data:</strong> Bank of Japan, Cabinet Office Economic and Social Research Institute</p>
          <p>• <strong>Global Markets:</strong> Major exchanges, commodity markets, currency data</p>
          <p>• <strong>Update Frequency:</strong> Real-time to daily depending on indicator</p>
        </div>
      </div>
    </div>
  )
}