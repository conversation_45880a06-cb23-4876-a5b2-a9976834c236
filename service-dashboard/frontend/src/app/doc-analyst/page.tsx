'use client'

import { useState, useEffect, useC<PERSON>back, memo, useMemo } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import {
  BarChart, Bar, PieChart, Pie, Cell, LineChart, Line, AreaChart, Area,
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend
} from 'recharts'
import {
  Brain, Settings, Activity, Zap, AlertCircle,
  PlayCircle, PauseCircle, RefreshCw, Save,
  TrendingUp, Clock, FileText, DollarSign, Building2, Search
} from 'lucide-react'
import CompanyDetails from '@/components/CompanyDetails'
import DocumentDetails from '@/components/DocumentDetails'

interface RateLimitConfig {
  model: string
  calls_per_minute: number
  tokens_per_minute: number
  max_tokens_per_call: number
}

interface AnalystMetrics {
  total_analyzed: number
  analysis_rate: number
  avg_processing_time: number
  success_rate: number
  models_used: {
    model: string
    count: number
    percentage: number
    avg_tokens: number
  }[]
  impact_distribution: {
    high: number
    medium: number
    low: number
  }
  sentiment_distribution: {
    positive: number
    neutral: number
    negative: number
  }
  recent_analyses: {
    timestamp: string
    document_id: string
    processing_time: number
    tokens_used: number
    model: string
    impact_level: string
    success: boolean
  }[]
  cost_metrics: {
    total_cost: number
    cost_by_model: { [key: string]: number }
    cost_trend: {
      date: string
      cost: number
    }[]
  }
}

interface QueueStatus {
  enabled: boolean
  queue_size: number
  processing_rate: number
  estimated_completion: string
  priority_breakdown: {
    high: number
    medium: number
    low: number
  }
}

const IMPACT_COLORS = {
  high: '#ef4444',
  medium: '#f59e0b',
  low: '#10b981'
}

const SENTIMENT_COLORS = {
  positive: '#10b981',
  neutral: '#6b7280',
  negative: '#ef4444'
}

export default function DocAnalystPage() {
  const [selectedModel, setSelectedModel] = useState('gemini-2.5-pro')
  const [config, setConfig] = useState<RateLimitConfig>({
    model: 'gemini-2.5-pro',
    calls_per_minute: 15,
    tokens_per_minute: 2000000,
    max_tokens_per_call: 200000
  })
  const [tempConfig, setTempConfig] = useState<RateLimitConfig>(config)
  const [metrics, setMetrics] = useState<AnalystMetrics | null>(null)
  const [queueStatus, setQueueStatus] = useState<QueueStatus>({
    enabled: true,
    queue_size: 0,
    processing_rate: 0,
    estimated_completion: 'N/A',
    priority_breakdown: { high: 0, medium: 0, low: 0 }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companyCode, setCompanyCode] = useState('')
  const [documentId, setDocumentId] = useState('')
  const [companyInput, setCompanyInput] = useState('')
  const [documentInput, setDocumentInput] = useState('')

  // Fetch current configuration and metrics
  const fetchData = useCallback(async () => {
    try {
      // Fetch rate limit config for selected model
      const configRes = await fetch(`/api/rate-limits/${selectedModel}`)
      if (configRes.ok) {
        const configData = await configRes.json()
        setConfig({ ...configData, model: selectedModel })
        setTempConfig({ ...configData, model: selectedModel })
      }

      // Fetch analyst metrics
      const metricsRes = await fetch('/api/analyst/metrics')
      if (metricsRes.ok) {
        const metricsData = await metricsRes.json()
        setMetrics(metricsData)
      }

      // Fetch queue status
      const queueRes = await fetch('/api/analyst/queue-status')
      if (queueRes.ok) {
        const queueData = await queueRes.json()
        setQueueStatus(queueData)
      }

      setError(null)
    } catch (err) {
      setError('Failed to fetch analyst data')
      console.error('Error fetching data:', err)
    } finally {
      setLoading(false)
    }
  }, [selectedModel])

  useEffect(() => {
    fetchData()
    const interval = setInterval(fetchData, 30000) // Reduced to 30 seconds for better performance
    return () => clearInterval(interval)
  }, [fetchData])

  const handleSaveConfig = async () => {
    setSaving(true)
    try {
      const response = await fetch(`/api/rate-limits/${selectedModel}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          calls_per_minute: tempConfig.calls_per_minute,
          tokens_per_minute: tempConfig.tokens_per_minute,
          max_tokens_per_call: tempConfig.max_tokens_per_call
        })
      })

      if (response.ok) {
        setConfig(tempConfig)
        setError(null)
      } else {
        throw new Error('Failed to save configuration')
      }
    } catch (err) {
      setError('Failed to save rate limit configuration')
      console.error('Error saving config:', err)
    } finally {
      setSaving(false)
    }
  }

  const handleToggleQueue = async () => {
    try {
      const response = await fetch('/api/analyst/queue-control', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: !queueStatus.enabled })
      })

      if (response.ok) {
        setQueueStatus(prev => ({ ...prev, enabled: !prev.enabled }))
      }
    } catch (err) {
      setError('Failed to toggle queue processing')
      console.error('Error toggling queue:', err)
    }
  }

  // Memoize expensive calculations
  const chartData = useMemo(() => {
    if (!metrics) return null

    return {
      modelUsage: metrics.models_used,
      impactDistribution: metrics.impact_distribution,
      sentimentDistribution: metrics.sentiment_distribution,
      recentAnalyses: metrics.recent_analyses,
      costMetrics: metrics.cost_metrics
    }
  }, [metrics])

  const hasConfigChanges = useMemo(() =>
    JSON.stringify(config) !== JSON.stringify(tempConfig),
    [config, tempConfig]
  )

  const handleCompanySearch = () => {
    if (companyInput.trim()) {
      setCompanyCode(companyInput.trim())
    }
  }

  const handleDocumentSearch = () => {
    if (documentInput.trim()) {
      setDocumentId(documentInput.trim())
    }
  }

  const handleDocumentClickFromCompany = (docId: string) => {
    setDocumentId(docId)
    setDocumentInput(docId)
    // Switch to document tab
    const documentTab = document.querySelector('[value="document"]') as HTMLButtonElement
    if (documentTab) {
      documentTab.click()
    }
  }

  const handleCompanyClickFromDocument = (compCode: string) => {
    setCompanyCode(compCode)
    setCompanyInput(compCode)
    // Switch to company tab
    const companyTab = document.querySelector('[value="company"]') as HTMLButtonElement
    if (companyTab) {
      companyTab.click()
    }
  }

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-lg text-gray-900 dark:text-white">Loading document analyst...</div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Brain className="h-8 w-8" />
            Document Analyst
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Advanced AI document analysis with multiple models
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={queueStatus.enabled ? "success" : "secondary"}>
            {queueStatus.enabled ? 'Analyzing' : 'Paused'}
          </Badge>
          <Button
            variant="outline"
            onClick={fetchData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Queue Control Card */}
      <Card>
        <CardHeader>
          <CardTitle>Analysis Queue Control</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Label htmlFor="queue-toggle">Queue Processing</Label>
                <Badge variant="outline">{queueStatus.queue_size} pending</Badge>
              </div>
              <p className="text-sm text-gray-500">
                {queueStatus.enabled
                  ? `Processing at ${queueStatus.processing_rate} docs/min`
                  : 'Analysis queue is paused'}
              </p>
            </div>
            <Switch
              id="queue-toggle"
              checked={queueStatus.enabled}
              onCheckedChange={handleToggleQueue}
            />
          </div>

          {/* Priority Breakdown */}
          <div className="grid grid-cols-3 gap-4 mt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{queueStatus.priority_breakdown.high}</div>
              <p className="text-sm text-gray-500">High Priority</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{queueStatus.priority_breakdown.medium}</div>
              <p className="text-sm text-gray-500">Medium Priority</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{queueStatus.priority_breakdown.low}</div>
              <p className="text-sm text-gray-500">Low Priority</p>
            </div>
          </div>

          {queueStatus.enabled && queueStatus.queue_size > 0 && (
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Queue Progress</span>
                <span>ETA: {queueStatus.estimated_completion}</span>
              </div>
              <Progress value={65} className="h-2" />
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid grid-cols-4 w-full max-w-2xl">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="company">Company</TabsTrigger>
          <TabsTrigger value="document">Document</TabsTrigger>
        </TabsList>

        {/* Overview Tab - Contains Metrics, Analysis, and Costs */}
        <TabsContent value="overview" className="space-y-6">
          {metrics && (
            <>
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Analyzed</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.total_analyzed.toLocaleString()}</div>
                    <p className="text-sm text-gray-500 mt-1">documents</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Analysis Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.analysis_rate}</div>
                    <p className="text-sm text-gray-500 mt-1">docs/minute</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Avg Processing</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.avg_processing_time.toFixed(1)}s</div>
                    <p className="text-sm text-gray-500 mt-1">per document</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.success_rate.toFixed(1)}%</div>
                    <Progress value={metrics.success_rate} className="h-1 mt-2" />
                  </CardContent>
                </Card>
              </div>

              {/* Model Usage & Impact Distribution */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Model Usage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <BarChart data={metrics.models_used}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="model" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="count" fill="#3b82f6">
                          {metrics.models_used.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={index % 2 === 0 ? '#3b82f6' : '#10b981'} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                    <div className="mt-4 space-y-2">
                      {metrics.models_used.map(model => (
                        <div key={model.model} className="flex justify-between text-sm">
                          <span>{model.model}</span>
                          <span className="text-gray-500">Avg {model.avg_tokens.toLocaleString()} tokens</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Impact Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'High', value: metrics.impact_distribution.high, color: IMPACT_COLORS.high },
                            { name: 'Medium', value: metrics.impact_distribution.medium, color: IMPACT_COLORS.medium },
                            { name: 'Low', value: metrics.impact_distribution.low, color: IMPACT_COLORS.low }
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {Object.entries(metrics.impact_distribution).map(([key], index) => (
                            <Cell key={`cell-${index}`} fill={IMPACT_COLORS[key as keyof typeof IMPACT_COLORS]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Analyses Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Analysis Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <LineChart data={metrics.recent_analyses}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="timestamp"
                        tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                      />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="processing_time"
                        stroke="#3b82f6"
                        name="Processing Time (s)"
                        dot={false}
                      />
                      <Line
                        type="monotone"
                        dataKey="tokens_used"
                        stroke="#10b981"
                        name="Tokens Used"
                        yAxisId="right"
                        dot={false}
                      />
                      <YAxis yAxisId="right" orientation="right" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Analysis Section */}
              {/* Sentiment Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Sentiment Analysis Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Positive', value: metrics.sentiment_distribution.positive, color: SENTIMENT_COLORS.positive },
                          { name: 'Neutral', value: metrics.sentiment_distribution.neutral, color: SENTIMENT_COLORS.neutral },
                          { name: 'Negative', value: metrics.sentiment_distribution.negative, color: SENTIMENT_COLORS.negative }
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {Object.entries(metrics.sentiment_distribution).map(([key], index) => (
                          <Cell key={`cell-${index}`} fill={SENTIMENT_COLORS[key as keyof typeof SENTIMENT_COLORS]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Analysis Quality Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Analysis Quality Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Completeness Score</span>
                        <span>92%</span>
                      </div>
                      <Progress value={92} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Accuracy Score</span>
                        <span>88%</span>
                      </div>
                      <Progress value={88} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Relevance Score</span>
                        <span>95%</span>
                      </div>
                      <Progress value={95} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Costs Section */}
              {/* Cost Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Cost (Month)</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${metrics.cost_metrics.total_cost.toFixed(2)}</div>
                    <p className="text-sm text-gray-500 mt-1">current month</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Avg Cost per Doc</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${(metrics.cost_metrics.total_cost / metrics.total_analyzed).toFixed(4)}
                    </div>
                    <p className="text-sm text-gray-500 mt-1">per document</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Projected Monthly</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${(metrics.cost_metrics.total_cost * 1.5).toFixed(2)}</div>
                    <p className="text-sm text-gray-500 mt-1">at current rate</p>
                  </CardContent>
                </Card>
              </div>

              {/* Cost by Model */}
              <Card>
                <CardHeader>
                  <CardTitle>Cost by Model</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={Object.entries(metrics.cost_metrics.cost_by_model).map(([model, cost]) => ({
                      model,
                      cost
                    }))}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="model" />
                      <YAxis />
                      <Tooltip formatter={(value: number) => `$${value.toFixed(2)}`} />
                      <Bar dataKey="cost" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Cost Trend */}
              <Card>
                <CardHeader>
                  <CardTitle>Daily Cost Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <AreaChart data={metrics.cost_metrics.cost_trend}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(value) => new Date(value).toLocaleDateString()}
                      />
                      <YAxis tickFormatter={(value) => `$${value}`} />
                      <Tooltip formatter={(value: number) => `$${value.toFixed(2)}`} />
                      <Area
                        type="monotone"
                        dataKey="cost"
                        stroke="#3b82f6"
                        fill="#3b82f680"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="config" className="space-y-6">
          {/* Model Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Model Selection</CardTitle>
            </CardHeader>
            <CardContent>
              <select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="gemini-2.5-pro">gemini-2.5-pro</option>
                <option value="gemini-2.5-flash">gemini-2.5-flash</option>
                <option value="deepseek-chat">deepseek-chat</option>
                <option value="ernie-4.0-turbo-8k">ernie-4.0-turbo-8k</option>
              </select>
            </CardContent>
          </Card>

          {/* Rate Limit Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Rate Limit Configuration - {selectedModel}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="calls-per-minute">Calls per Minute</Label>
                  <Input
                    id="calls-per-minute"
                    type="number"
                    value={tempConfig.calls_per_minute}
                    onChange={(e) => setTempConfig(prev => ({
                      ...prev,
                      calls_per_minute: parseInt(e.target.value) || 0
                    }))}
                  />
                  <p className="text-sm text-gray-500">API calls allowed per minute</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokens-per-minute">Tokens per Minute</Label>
                  <Input
                    id="tokens-per-minute"
                    type="number"
                    value={tempConfig.tokens_per_minute}
                    onChange={(e) => setTempConfig(prev => ({
                      ...prev,
                      tokens_per_minute: parseInt(e.target.value) || 0
                    }))}
                  />
                  <p className="text-sm text-gray-500">Total tokens allowed per minute</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-tokens">Max Tokens per Call</Label>
                  <Input
                    id="max-tokens"
                    type="number"
                    value={tempConfig.max_tokens_per_call}
                    onChange={(e) => setTempConfig(prev => ({
                      ...prev,
                      max_tokens_per_call: parseInt(e.target.value) || 0
                    }))}
                  />
                  <p className="text-sm text-gray-500">Maximum tokens in a single call</p>
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setTempConfig(config)}
                  disabled={!hasConfigChanges}
                >
                  Reset
                </Button>
                <Button
                  onClick={handleSaveConfig}
                  disabled={!hasConfigChanges || saving}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Company Tab */}
        <TabsContent value="company" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Company Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2 mb-6">
                <Input
                  placeholder="Enter company code (e.g., 7203, 6758)"
                  value={companyInput}
                  onChange={(e) => setCompanyInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleCompanySearch()}
                  className="flex-1"
                />
                <Button onClick={handleCompanySearch} disabled={!companyInput.trim()}>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
              
              {companyCode ? (
                <CompanyDetails 
                  companyCode={companyCode} 
                  onDocumentClick={handleDocumentClickFromCompany}
                />
              ) : (
                <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                  <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Enter a company code to view detailed company information</p>
                  <p className="text-sm mt-2">Example: 7203 (Toyota), 6758 (Sony), 9984 (SoftBank)</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Document Tab */}
        <TabsContent value="document" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2 mb-6">
                <Input
                  placeholder="Enter document ID"
                  value={documentInput}
                  onChange={(e) => setDocumentInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleDocumentSearch()}
                  className="flex-1"
                />
                <Button onClick={handleDocumentSearch} disabled={!documentInput.trim()}>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
              
              {documentId ? (
                <DocumentDetails 
                  documentId={documentId} 
                  onCompanyClick={handleCompanyClickFromDocument}
                />
              ) : (
                <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Enter a document ID to view detailed document information</p>
                  <p className="text-sm mt-2">You can get document IDs from company pages or search results</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
