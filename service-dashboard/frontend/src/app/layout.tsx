import type { Metadata } from 'next'
import './globals.css'
import Sidebar from '@/components/Sidebar'
import AlertBell from '@/components/AlertBell'
import ThemeToggle from '@/components/ThemeToggle'
import DashboardModeToggle from '@/components/DashboardModeToggle'
import IBKRStatusWidget from '@/components/IBKRStatusWidget'
import SearchWidget from '@/components/SearchWidget'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { DashboardModeProvider } from '@/contexts/DashboardModeContext'
import { CacheMonitor } from '@/components/CacheMonitor'

export const metadata: Metadata = {
  title: 'BR1DGE Dashboard',
  description: 'Unified Financial Intelligence Dashboard',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="min-h-screen bg-background text-foreground">
        <ThemeProvider>
          <DashboardModeProvider>
            <div className="flex h-screen bg-background">
              <Sidebar />
              <div className="flex-1 flex flex-col">
                <header className="h-16 bg-gray-900 dark:bg-gray-950 flex items-center justify-between px-6">
                  <div className="flex-1 flex items-center justify-center max-w-md mx-auto">
                    <SearchWidget />
                  </div>
                  <div className="flex items-center gap-4">
                    <IBKRStatusWidget />
                    <div className="w-px h-8 bg-gray-700" />
                    <DashboardModeToggle />
                    <div className="w-px h-8 bg-gray-700" />
                    <ThemeToggle />
                    <AlertBell />
                    <div className="w-px h-8 bg-gray-700" />
                    <button className="flex items-center gap-2 hover:bg-gray-800 dark:hover:bg-gray-900 rounded-lg p-2 transition-colors">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
                        U
                      </div>
                    </button>
                  </div>
                </header>
                <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
                  {children}
                </main>
              </div>
            </div>
            {process.env.NODE_ENV === 'development' && <CacheMonitor />}
          </DashboardModeProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
