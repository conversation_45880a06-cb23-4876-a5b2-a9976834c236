#!/usr/bin/env python3
"""
ABOUTME: IBKR Trading Service - Order execution and portfolio management via Interactive Brokers
ABOUTME: Provides secure trading operations with comprehensive risk management and position tracking
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager
from decimal import Decimal
from enum import Enum

from fastapi import FastAPI, HTTPException, Query, Body, Depends, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
import redis.asyncio as redis
import uvicorn

from ib_insync import IB, Stock, Contract, Order, Trade, Position, AccountValue, util

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6381))
PORT = int(os.getenv('PORT', 8095))
IB_HOST = os.getenv('IB_HOST', 'tws')
IB_PORT = int(os.getenv('IB_PORT', 7497))
IB_CLIENT_ID = int(os.getenv('IB_CLIENT_ID', 10))  # Unique client ID for trading
TRADING_ACCOUNT = os.getenv('TRADING_ACCOUNT', '')  # Required for live trading
API_KEY = os.getenv('TRADING_API_KEY', 'dev-trading-key')  # Basic auth protection

# Enums
class OrderType(str, Enum):
    MARKET = "MKT"
    LIMIT = "LMT"
    STOP = "STP" 
    STOP_LIMIT = "STP LMT"

class OrderAction(str, Enum):
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(str, Enum):
    PENDING = "PendingSubmit"
    SUBMITTED = "Submitted"
    FILLED = "Filled"
    CANCELLED = "Cancelled"
    REJECTED = "ApiCancelled"

class TimeInForce(str, Enum):
    DAY = "DAY"
    GTC = "GTC"  # Good Till Cancelled
    IOC = "IOC"  # Immediate or Cancel
    FOK = "FOK"  # Fill or Kill

# Request/Response Models
class OrderRequest(BaseModel):
    symbol: str = Field(..., description="Stock symbol")
    action: OrderAction = Field(..., description="Buy or Sell")
    quantity: int = Field(..., gt=0, description="Number of shares")
    order_type: OrderType = Field(..., description="Order type")
    limit_price: Optional[float] = Field(None, description="Limit price (for LMT orders)")
    stop_price: Optional[float] = Field(None, description="Stop price (for STP orders)")
    time_in_force: TimeInForce = Field(TimeInForce.DAY, description="Time in force")
    account: Optional[str] = Field(None, description="Trading account (if multiple)")
    
    @validator('limit_price')
    def validate_limit_price(cls, v, values):
        if values.get('order_type') in [OrderType.LIMIT, OrderType.STOP_LIMIT] and v is None:
            raise ValueError("Limit price required for limit orders")
        return v
    
    @validator('stop_price')  
    def validate_stop_price(cls, v, values):
        if values.get('order_type') in [OrderType.STOP, OrderType.STOP_LIMIT] and v is None:
            raise ValueError("Stop price required for stop orders")
        return v

class BracketOrderRequest(BaseModel):
    symbol: str = Field(..., description="Stock symbol")
    action: OrderAction = Field(..., description="Buy or Sell")
    quantity: int = Field(..., gt=0, description="Number of shares")
    entry_price: float = Field(..., description="Entry limit price")
    profit_target: float = Field(..., description="Profit target price")
    stop_loss: float = Field(..., description="Stop loss price")
    account: Optional[str] = Field(None, description="Trading account")

class PositionSummary(BaseModel):
    symbol: str
    position: int
    market_price: float
    market_value: float
    average_cost: float
    unrealized_pnl: float
    realized_pnl: float
    currency: str
    exchange: str

class AccountSummary(BaseModel):
    account_id: str
    total_cash_value: float
    net_liquidation: float
    buying_power: float
    day_trades_remaining: int
    currency: str
    positions: List[PositionSummary]

class OrderSummary(BaseModel):
    order_id: int
    symbol: str
    action: str
    quantity: int
    order_type: str
    status: str
    filled_quantity: int
    remaining_quantity: int
    limit_price: Optional[float]
    stop_price: Optional[float]
    avg_fill_price: Optional[float]
    commission: Optional[float]
    timestamp: datetime

# Global instances
ib_client: Optional[IB] = None
redis_client: Optional[redis.Redis] = None
security = HTTPBearer()

# Authentication
async def verify_trading_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify trading API token"""
    if credentials.credentials != API_KEY:
        raise HTTPException(
            status_code=401,
            detail="Invalid trading API key"
        )
    return credentials.credentials

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle"""
    global ib_client, redis_client
    
    # Initialize Redis
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        decode_responses=True
    )
    
    # Initialize IB connection
    ib_client = IB()
    
    try:
        await ib_client.connectAsync(IB_HOST, IB_PORT, clientId=IB_CLIENT_ID)
        logger.info(f"Connected to IBKR Trading at {IB_HOST}:{IB_PORT}")
        
        # Request account updates
        if TRADING_ACCOUNT:
            ib_client.reqAccountUpdates(True, TRADING_ACCOUNT)
            
    except Exception as e:
        logger.error(f"Failed to connect to IBKR: {e}")
        # Continue without connection for health checks
    
    logger.info("IBKR Trading Service initialized")
    
    yield
    
    # Cleanup
    if ib_client and ib_client.isConnected():
        ib_client.disconnect()
    if redis_client:
        await redis_client.close()

app = FastAPI(
    title="IBKR Trading Service",
    description="Order execution and portfolio management via Interactive Brokers",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def _create_contract(symbol: str) -> Contract:
    """Create appropriate contract based on symbol format"""
    if symbol.endswith('.T'):
        # Japanese stock
        clean_symbol = symbol[:-2]
        return Stock(clean_symbol, 'TSEJ', 'JPY')
    elif symbol.endswith('.HK'):
        # Hong Kong stock
        clean_symbol = symbol[:-3]
        return Stock(clean_symbol, 'SEHK', 'HKD')
    elif symbol.endswith('.L'):
        # London stock
        clean_symbol = symbol[:-2]
        return Stock(clean_symbol, 'LSE', 'GBP')
    else:
        # Default to US stock
        return Stock(symbol, 'SMART', 'USD')

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    if not ib_client:
        return {"status": "unhealthy", "error": "Service not initialized"}
    
    connected = ib_client.isConnected()
    
    return {
        "status": "healthy" if connected else "degraded",
        "connected": connected,
        "host": IB_HOST,
        "port": IB_PORT,
        "client_id": IB_CLIENT_ID,
        "trading_account": TRADING_ACCOUNT or "Not configured",
        "redis_connected": await redis_client.ping() if redis_client else False
    }

@app.post("/api/trading/order")
async def place_order(
    request: OrderRequest,
    _: str = Depends(verify_trading_token)
):
    """Place a trading order"""
    if not ib_client or not ib_client.isConnected():
        raise HTTPException(status_code=503, detail="Not connected to IBKR")
    
    try:
        # Create contract
        contract = _create_contract(request.symbol)
        
        # Qualify contract
        contracts = await ib_client.qualifyContractsAsync(contract)
        if not contracts:
            raise HTTPException(status_code=400, detail=f"Cannot find contract for {request.symbol}")
        
        qualified = contracts[0]
        
        # Create order
        order = Order()
        order.action = request.action.value
        order.orderType = request.order_type.value
        order.totalQuantity = request.quantity
        order.tif = request.time_in_force.value
        
        if request.limit_price:
            order.lmtPrice = request.limit_price
        if request.stop_price:
            order.auxPrice = request.stop_price
        if request.account:
            order.account = request.account
        elif TRADING_ACCOUNT:
            order.account = TRADING_ACCOUNT
        
        # Place order
        trade = ib_client.placeOrder(qualified, order)
        
        # Wait for order acknowledgment
        await asyncio.sleep(1)
        
        # Log order placement
        logger.info(f"Order placed: {request.symbol} {request.action.value} {request.quantity} @ {request.order_type.value}")
        
        return {
            "success": True,
            "order_id": trade.order.orderId,
            "symbol": request.symbol,
            "action": request.action.value,
            "quantity": request.quantity,
            "order_type": request.order_type.value,
            "status": trade.orderStatus.status if trade.orderStatus else "Submitted",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Order placement error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to place order: {e}")

@app.post("/api/trading/bracket-order")
async def place_bracket_order(
    request: BracketOrderRequest,
    _: str = Depends(verify_trading_token)
):
    """Place a bracket order (entry + profit target + stop loss)"""
    if not ib_client or not ib_client.isConnected():
        raise HTTPException(status_code=503, detail="Not connected to IBKR")
    
    try:
        # Create contract
        contract = _create_contract(request.symbol)
        
        # Qualify contract
        contracts = await ib_client.qualifyContractsAsync(contract)
        if not contracts:
            raise HTTPException(status_code=400, detail=f"Cannot find contract for {request.symbol}")
        
        qualified = contracts[0]
        
        # Create parent order (entry)
        parent = Order()
        parent.action = request.action.value
        parent.orderType = "LMT"
        parent.totalQuantity = request.quantity
        parent.lmtPrice = request.entry_price
        parent.tif = "GTC"
        parent.transmit = False  # Don't transmit until all orders are ready
        
        if request.account:
            parent.account = request.account
        elif TRADING_ACCOUNT:
            parent.account = TRADING_ACCOUNT
        
        # Create profit target order
        profit_action = "SELL" if request.action == OrderAction.BUY else "BUY"
        profit_target = Order()
        profit_target.action = profit_action
        profit_target.orderType = "LMT"
        profit_target.totalQuantity = request.quantity
        profit_target.lmtPrice = request.profit_target
        profit_target.parentId = parent.orderId
        profit_target.transmit = False
        
        # Create stop loss order
        stop_loss = Order()
        stop_loss.action = profit_action
        stop_loss.orderType = "STP"
        stop_loss.totalQuantity = request.quantity
        stop_loss.auxPrice = request.stop_loss
        stop_loss.parentId = parent.orderId
        stop_loss.transmit = True  # This will transmit all orders
        
        # Place bracket orders
        parent_trade = ib_client.placeOrder(qualified, parent)
        profit_trade = ib_client.placeOrder(qualified, profit_target)
        stop_trade = ib_client.placeOrder(qualified, stop_loss)
        
        await asyncio.sleep(1)
        
        logger.info(f"Bracket order placed: {request.symbol} entry@{request.entry_price} target@{request.profit_target} stop@{request.stop_loss}")
        
        return {
            "success": True,
            "parent_order_id": parent_trade.order.orderId,
            "profit_order_id": profit_trade.order.orderId,
            "stop_order_id": stop_trade.order.orderId,
            "symbol": request.symbol,
            "entry_price": request.entry_price,
            "profit_target": request.profit_target,
            "stop_loss": request.stop_loss,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Bracket order error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to place bracket order: {e}")

@app.get("/api/trading/orders")
async def get_open_orders(_: str = Depends(verify_trading_token)):
    """Get all open orders"""
    if not ib_client or not ib_client.isConnected():
        raise HTTPException(status_code=503, detail="Not connected to IBKR")
    
    try:
        trades = ib_client.trades()
        open_orders = []
        
        for trade in trades:
            if trade.orderStatus.status in ["Submitted", "PreSubmitted", "PendingSubmit"]:
                open_orders.append(OrderSummary(
                    order_id=trade.order.orderId,
                    symbol=trade.contract.symbol,
                    action=trade.order.action,
                    quantity=trade.order.totalQuantity,
                    order_type=trade.order.orderType,
                    status=trade.orderStatus.status,
                    filled_quantity=trade.orderStatus.filled,
                    remaining_quantity=trade.orderStatus.remaining,
                    limit_price=trade.order.lmtPrice if trade.order.lmtPrice != float('inf') else None,
                    stop_price=trade.order.auxPrice if trade.order.auxPrice != float('inf') else None,
                    avg_fill_price=trade.orderStatus.avgFillPrice if trade.orderStatus.avgFillPrice else None,
                    commission=trade.commissionReport.commission if trade.commissionReport else None,
                    timestamp=datetime.now()
                ))
        
        return {
            "orders": [order.dict() for order in open_orders],
            "count": len(open_orders),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Get orders error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get orders: {e}")

@app.delete("/api/trading/order/{order_id}")
async def cancel_order(
    order_id: int,
    _: str = Depends(verify_trading_token)
):
    """Cancel an open order"""
    if not ib_client or not ib_client.isConnected():
        raise HTTPException(status_code=503, detail="Not connected to IBKR")
    
    try:
        # Find the trade
        trade = None
        for t in ib_client.trades():
            if t.order.orderId == order_id:
                trade = t
                break
        
        if not trade:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found")
        
        # Cancel the order
        ib_client.cancelOrder(trade.order)
        
        await asyncio.sleep(1)
        
        logger.info(f"Order cancelled: {order_id}")
        
        return {
            "success": True,
            "order_id": order_id,
            "message": "Order cancellation requested",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Cancel order error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel order: {e}")

@app.get("/api/trading/positions")
async def get_positions(_: str = Depends(verify_trading_token)):
    """Get current positions"""
    if not ib_client or not ib_client.isConnected():
        raise HTTPException(status_code=503, detail="Not connected to IBKR")
    
    try:
        positions = ib_client.positions()
        position_summaries = []
        
        for position in positions:
            if position.position != 0:  # Only show non-zero positions
                # Get current market price
                ticker = ib_client.reqMktData(position.contract, '', False, False)
                await asyncio.sleep(1)
                
                market_price = ticker.last if ticker.last and not util.isNan(ticker.last) else position.marketPrice
                market_value = position.position * market_price
                
                ib_client.cancelMktData(position.contract)
                
                position_summaries.append(PositionSummary(
                    symbol=position.contract.symbol,
                    position=int(position.position),
                    market_price=float(market_price),
                    market_value=float(market_value),
                    average_cost=float(position.avgCost),
                    unrealized_pnl=float(position.unrealizedPNL),
                    realized_pnl=float(position.realizedPNL),
                    currency=position.contract.currency,
                    exchange=position.contract.exchange
                ))
        
        return {
            "positions": [pos.dict() for pos in position_summaries],
            "count": len(position_summaries),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Get positions error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get positions: {e}")

@app.get("/api/trading/account")
async def get_account_summary(_: str = Depends(verify_trading_token)):
    """Get account summary and portfolio"""
    if not ib_client or not ib_client.isConnected():
        raise HTTPException(status_code=503, detail="Not connected to IBKR")
    
    try:
        # Get account values
        account_values = ib_client.accountValues()
        
        # Extract key account metrics
        account_data = {}
        for av in account_values:
            if av.tag in ['TotalCashValue', 'NetLiquidation', 'BuyingPower', 'DayTradesRemaining']:
                account_data[av.tag] = float(av.value) if av.value.replace('.', '').replace('-', '').isdigit() else av.value
        
        # Get positions
        positions_response = await get_positions(_)
        positions = positions_response.get("positions", [])
        
        account_summary = AccountSummary(
            account_id=TRADING_ACCOUNT or "Default",
            total_cash_value=account_data.get('TotalCashValue', 0.0),
            net_liquidation=account_data.get('NetLiquidation', 0.0),
            buying_power=account_data.get('BuyingPower', 0.0),
            day_trades_remaining=int(account_data.get('DayTradesRemaining', 0)),
            currency='USD',  # Default currency
            positions=[PositionSummary(**pos) for pos in positions]
        )
        
        return {
            "account": account_summary.dict(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Get account error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get account: {e}")

@app.get("/api/trading/trade-history")
async def get_trade_history(
    days: int = Query(7, description="Number of days to look back"),
    _: str = Depends(verify_trading_token)
):
    """Get trade execution history"""
    if not ib_client or not ib_client.isConnected():
        raise HTTPException(status_code=503, detail="Not connected to IBKR")
    
    try:
        trades = ib_client.trades()
        filled_trades = []
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        for trade in trades:
            if (trade.orderStatus.status == "Filled" and 
                trade.fills and 
                len(trade.fills) > 0):
                
                # Check if trade is within date range
                last_fill = trade.fills[-1]
                if hasattr(last_fill, 'time') and last_fill.time > cutoff_date:
                    filled_trades.append({
                        "order_id": trade.order.orderId,
                        "symbol": trade.contract.symbol,
                        "action": trade.order.action,
                        "quantity": trade.orderStatus.filled,
                        "avg_price": trade.orderStatus.avgFillPrice,
                        "commission": trade.commissionReport.commission if trade.commissionReport else None,
                        "fill_time": last_fill.time.isoformat() if hasattr(last_fill, 'time') else None,
                        "order_type": trade.order.orderType
                    })
        
        return {
            "trades": filled_trades,
            "count": len(filled_trades),
            "days": days,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Get trade history error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get trade history: {e}")

if __name__ == "__main__":
    uvicorn.run(
        "service:app",
        host="0.0.0.0",
        port=PORT,
        reload=True
    )