"""
ABOUTME: Company data enricher that fetches real-time financial information from IBKR
ABOUTME: Adds comprehensive company snapshot data to corporate documents at disclosure timestamp
"""

import asyncio
import json
import logging
import os
import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Any, Dict, List, Optional

import redis.asyncio as redis
from ib_insync import IB, Stock, util

logger = logging.getLogger(__name__)


class CompanySnapshot:
    """Company financial data snapshot at a specific timestamp"""

    def __init__(self, symbol: str, timestamp: datetime):
        self.symbol = symbol
        self.timestamp = timestamp
        self.data_source = "unknown"
        self.company_info = {}
        self.market_data = {}
        self.fundamentals = {}
        self.data_quality = {"completeness_score": 0.0, "missing_fields": []}

    def to_dict(self) -> Dict[str, Any]:
        """Convert snapshot to dictionary format"""
        return {
            "snapshot_timestamp": self.timestamp.isoformat(),
            "data_source": self.data_source,
            "company_info": self.company_info,
            "market_data": self.market_data,
            "fundamentals": self.fundamentals,
        }


class CompanyDataEnricher:
    """
    Enriches documents with company financial data or economic context
    """

    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.redis_client = redis_client
        self.ib_connection = None
        self.ib_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=3)

        # Database connections
        self.db_pool = None

        # Configuration
        self.ib_host = os.getenv("IB_HOST", "tws")
        self.ib_port = int(os.getenv("IB_PORT", "7499"))  # Paper trading port
        self.ib_client_id = int(os.getenv("IB_CLIENT_ID", "2"))  # Different from stop calculator

        # Database configuration
        self.db_url = os.getenv(
            "DATABASE_URL", "postgresql://omotesamba:development@localhost:5435/omotesamba"
        )

        # Caching
        self.cache_ttl = int(os.getenv("COMPANY_DATA_CACHE_TTL", "300"))  # 5 minutes
        self.request_timeout = int(os.getenv("IBKR_HISTORICAL_DATA_TIMEOUT", "30"))

        # Quality thresholds
        self.min_completeness_score = float(os.getenv("MIN_COMPANY_DATA_COMPLETENESS", "0.6"))

    async def startup(self):
        """Initialize IBKR connection and database connections"""
        try:
            await self._initialize_ibkr_connection()
            await self._initialize_database_connections()
            logger.info("✅ Company enricher startup complete")
        except Exception as e:
            logger.error(f"❌ Error during enricher startup: {e}")
            raise

    async def shutdown(self):
        """Cleanup resources"""
        try:
            if self.ib_connection and self.ib_connection.isConnected():
                self.ib_connection.disconnect()

            # Shutdown database connections
            if self.db_pool:
                await self.db_pool.close()
                logger.info("✅ Database pool closed")

            self.executor.shutdown(wait=True)
            logger.info("✅ Company enricher shutdown complete")
        except Exception as e:
            logger.error(f"❌ Error during enricher shutdown: {e}")

    async def _initialize_ibkr_connection(self):
        """Initialize IBKR connection with fallback"""
        try:
            # For now, skip IBKR connection in containerized environment
            # Use fallback mode that returns cached/mock data
            logger.info("📊 Initializing company enricher in fallback mode")
            self.ib_available = False
            return

            # TODO: Fix IBKR async connection when TWS container networking is stable
            def connect_ibkr():
                try:
                    with self.ib_lock:
                        if self.ib_connection is None or not self.ib_connection.isConnected():
                            self.ib_connection = IB()
                            self.ib_connection.connect(
                                self.ib_host, self.ib_port, clientId=self.ib_client_id
                            )
                            logger.info(f"✅ Connected to IBKR at {self.ib_host}:{self.ib_port}")
                    return True
                except Exception as e:
                    logger.error(f"❌ Failed to connect to IBKR: {e}")
                    return False

            # Run connection in executor
            future = self.executor.submit(connect_ibkr)
            success = await asyncio.get_event_loop().run_in_executor(None, future.result, 10)

            if not success:
                logger.warning("🔄 IBKR connection failed, using fallback mode")
                self.ib_available = False
            else:
                self.ib_available = True

        except Exception as e:
            logger.warning(f"🔄 IBKR initialization failed: {e}, using fallback mode")
            self.ib_available = False

    async def _initialize_database_connections(self):
        """Initialize database connections for internal data sources"""
        try:
            # TimescaleDB connection pool
            import asyncpg

            self.db_pool = await asyncpg.create_pool(
                self.db_url, min_size=2, max_size=8, command_timeout=30
            )
            logger.info("✅ Connected to TimescaleDB")

            # Test the connection
            async with self.db_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")

        except Exception as e:
            logger.warning(f"⚠️ Database connection failed: {e}, continuing without internal data")
            self.db_pool = None

    def _get_cache_key(self, symbol: str, timestamp: datetime) -> str:
        """Generate cache key for company data"""
        date_str = timestamp.strftime("%Y%m%d")
        return f"company_data:{symbol}:{date_str}"

    async def _get_cached_data(self, cache_key: str) -> Optional[Dict]:
        """Retrieve cached company data"""
        try:
            if self.redis_client:
                cached_data = await self.redis_client.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)
        except Exception as e:
            logger.warning(f"Cache read error: {e}")
        return None

    async def _cache_data(self, cache_key: str, data: Dict):
        """Cache company data"""
        try:
            if self.redis_client:
                await self.redis_client.setex(
                    cache_key, self.cache_ttl, json.dumps(data, default=str)
                )
        except Exception as e:
            logger.warning(f"Cache write error: {e}")

    def _parse_company_code(self, document: Dict) -> Optional[str]:
        """Extract and validate company code from document"""
        # Try multiple sources for company code
        company_code = None

        # From company field
        company = document.get("company", {})
        if isinstance(company, dict):
            company_code = company.get("code")

        # From company_code field
        if not company_code:
            company_code = document.get("company_code")

        # From categorization result
        if not company_code:
            categorization = document.get("categorization", {})
            if isinstance(categorization, dict):
                company_code = categorization.get("company_code")

        # Validate company code format (Japanese stock codes)
        if company_code and isinstance(company_code, str):
            # Clean and validate
            code = company_code.strip()
            if code.isdigit() and len(code) in [4, 5]:
                return code

        return None

    def _create_stock_contract(self, company_code: str):
        """Create IBKR stock contract for Japanese stock"""
        try:
            # Japanese stocks use TSEJ exchange with JPY currency
            contract = Stock(company_code, "TSEJ", "JPY")
            logger.info(f"Created contract for {company_code}: {contract}")
            return contract
        except Exception as e:
            logger.error(f"Error creating contract for {company_code}: {e}")
            return None

    async def _fetch_ibkr_data(self, company_code: str, timestamp: datetime) -> CompanySnapshot:
        """Fetch comprehensive company data from IBKR"""
        snapshot = CompanySnapshot(company_code, timestamp)

        def fetch_data():
            try:
                # Get IBKR connection
                if not self.ib_connection or not self.ib_connection.isConnected():
                    self._initialize_ibkr_connection()

                if not self.ib_connection or not self.ib_connection.isConnected():
                    raise Exception("IBKR connection not available")

                # Create contract
                contract = self._create_stock_contract(company_code)
                if not contract:
                    raise Exception(f"Could not create contract for {company_code}")

                # Qualify contract to get full details
                qualified_contracts = self.ib_connection.qualifyContracts(contract)
                if not qualified_contracts:
                    raise Exception(f"Could not qualify contract for {company_code}")

                qualified_contract = qualified_contracts[0]

                # Get company information from contract
                snapshot.company_info = {
                    "symbol": f"{company_code}.T",
                    "name": getattr(qualified_contract, "longName", "")
                    or getattr(qualified_contract, "tradingClass", company_code),
                    "sector": getattr(qualified_contract, "industry", ""),
                    "exchange": qualified_contract.exchange or "TSEJ",
                    "currency": qualified_contract.currency or "JPY",
                    "contract_id": qualified_contract.conId,
                }

                # Get market data ticker
                ticker = self.ib_connection.reqMktData(qualified_contract, "", False, False)
                self.ib_connection.sleep(2)  # Wait for market data

                if ticker:
                    # Extract market data
                    snapshot.market_data = {
                        "price": float(ticker.last) if ticker.last and ticker.last > 0 else None,
                        "currency": qualified_contract.currency,
                        "bid": float(ticker.bid) if ticker.bid and ticker.bid > 0 else None,
                        "ask": float(ticker.ask) if ticker.ask and ticker.ask > 0 else None,
                        "volume": int(ticker.volume)
                        if ticker.volume and ticker.volume > 0
                        else None,
                        "day_change": float(ticker.change) if ticker.change else None,
                        "day_change_percent": float(ticker.changePercent)
                        if ticker.changePercent
                        else None,
                        "day_range": {
                            "low": float(ticker.low) if ticker.low and ticker.low > 0 else None,
                            "high": float(ticker.high) if ticker.high and ticker.high > 0 else None,
                        },
                    }

                # Get fundamental data
                try:
                    fundamental_data = self.ib_connection.reqFundamentalData(
                        qualified_contract, "ReportsFinSummary"
                    )
                    if fundamental_data:
                        # Parse fundamental data (XML format)
                        snapshot.fundamentals = self._parse_fundamental_data(fundamental_data)
                except Exception as e:
                    logger.warning(f"Could not fetch fundamentals for {company_code}: {e}")
                    snapshot.fundamentals = {}

                # Try to get historical data for additional context
                try:
                    end_date = timestamp
                    bars = self.ib_connection.reqHistoricalData(
                        qualified_contract,
                        endDateTime=end_date,
                        durationStr="5 D",
                        barSizeSetting="1 day",
                        whatToShow="TRADES",
                        useRTH=True,
                        formatDate=1,
                        timeout=self.request_timeout,
                    )

                    if bars:
                        df = util.df(bars)
                        if not df.empty:
                            # Add 52-week range and volume context
                            snapshot.market_data.update(
                                {
                                    "52_week_range": {
                                        "low": float(df["low"].min()),
                                        "high": float(df["high"].max()),
                                    },
                                    "avg_volume_5d": int(df["volume"].mean())
                                    if "volume" in df
                                    else None,
                                }
                            )

                except Exception as e:
                    logger.warning(f"Could not fetch historical data for {company_code}: {e}")

                snapshot.data_source = "ibkr"

                # Calculate data quality
                snapshot.data_quality = self._calculate_data_quality(snapshot)

                return snapshot

            except Exception as e:
                logger.error(f"Error fetching IBKR data for {company_code}: {e}")
                # Return snapshot with error info
                snapshot.data_source = "error"
                snapshot.data_quality = {
                    "completeness_score": 0.0,
                    "error": str(e),
                    "missing_fields": ["all"],
                }
                return snapshot

        # Execute in thread pool
        try:
            future = self.executor.submit(fetch_data)
            return await asyncio.get_event_loop().run_in_executor(
                None, future.result, self.request_timeout
            )
        except Exception as e:
            logger.error(f"Timeout or error in IBKR data fetch for {company_code}: {e}")
            snapshot.data_source = "timeout"
            snapshot.data_quality = {
                "completeness_score": 0.0,
                "error": str(e),
                "missing_fields": ["all"],
            }
            return snapshot

    def _parse_fundamental_data(self, fundamental_xml: str) -> Dict:
        """Parse fundamental data from IBKR XML response"""
        try:
            # Basic parsing - this could be enhanced with proper XML parsing
            fundamentals = {}

            # Extract basic ratios if available
            if "PERatio" in fundamental_xml:
                # Simple regex extraction - improve as needed
                import re

                pe_match = re.search(r"PERatio[^>]*>([^<]+)", fundamental_xml)
                if pe_match:
                    try:
                        fundamentals["pe_ratio"] = float(pe_match.group(1))
                    except:
                        pass

            return fundamentals

        except Exception as e:
            logger.warning(f"Error parsing fundamental data: {e}")
            return {}

    def _calculate_data_quality(self, snapshot: CompanySnapshot) -> Dict:
        """Calculate data quality score based on available fields"""
        required_fields = [
            ("company_info", "name"),
            ("company_info", "symbol"),
            ("market_data", "price"),
            ("market_data", "currency"),
        ]

        optional_fields = [
            ("market_data", "volume"),
            ("market_data", "day_range"),
            ("market_data", "52_week_range"),
            ("fundamentals", "pe_ratio"),
        ]

        present_required = 0
        present_optional = 0
        missing_fields = []

        # Check required fields
        for section, field in required_fields:
            section_data = getattr(snapshot, section, {})
            if (
                isinstance(section_data, dict)
                and field in section_data
                and section_data[field] is not None
            ):
                present_required += 1
            else:
                missing_fields.append(f"{section}.{field}")

        # Check optional fields
        for section, field in optional_fields:
            section_data = getattr(snapshot, section, {})
            if (
                isinstance(section_data, dict)
                and field in section_data
                and section_data[field] is not None
            ):
                present_optional += 1
            else:
                missing_fields.append(f"{section}.{field}")

        # Calculate score (required fields weighted more heavily)
        required_score = present_required / len(required_fields) * 0.7
        optional_score = present_optional / len(optional_fields) * 0.3
        total_score = required_score + optional_score

        return {
            "completeness_score": round(total_score, 3),
            "data_age_seconds": 0,  # Real-time data
            "missing_fields": missing_fields[:10],  # Limit to 10 for brevity
        }

    async def _enhance_company_data(
        self, snapshot: CompanySnapshot, company_code: str, document: Dict
    ):
        """Enhance company snapshot with additional data from our sources"""
        try:
            # Add data from categorization
            categorization = document.get("categorization", {})
            entities = categorization.get("entities", {})
            primary_company = entities.get("primary_company", {})

            if primary_company:
                # Enhance company info with categorization data
                snapshot.company_info.update(
                    {
                        "english_name": primary_company.get(
                            "english_name", snapshot.company_info.get("name", "")
                        ),
                        "ticker": primary_company.get("ticker", f"{company_code}.T"),
                        "is_data_accessible": primary_company.get("is_data_accessible", True),
                        "business_description": primary_company.get("business_description", ""),
                    }
                )

            # Add document metadata enrichment
            discovery_metadata = document.get("discovery", {}).get("metadata", {})
            if discovery_metadata:
                company_metadata = discovery_metadata.get("company", {})
                if company_metadata:
                    snapshot.company_info.update(
                        {
                            "market_cap_tier": company_metadata.get("market_cap_tier", ""),
                            "listing_section": company_metadata.get("listing_section", ""),
                            "fiscal_year_end": company_metadata.get("fiscal_year_end", ""),
                        }
                    )

            # Add sector peers from our knowledge
            sector_peers = await self._get_sector_peers(
                company_code, snapshot.company_info.get("sector", "")
            )
            if sector_peers:
                snapshot.company_info["sector_peers"] = sector_peers

            # Add recent document history
            recent_docs = await self._get_recent_documents(company_code)
            if recent_docs:
                snapshot.company_info["recent_disclosures"] = recent_docs

            # Add document frequency analysis
            doc_stats = await self._get_document_frequency_stats(company_code)
            if doc_stats:
                snapshot.company_info["disclosure_patterns"] = doc_stats

            # Add company profile from database
            company_profile = await self._get_company_profile(company_code)
            if company_profile:
                snapshot.company_info.update(company_profile)

        except Exception as e:
            logger.warning(f"Error enhancing company data: {e}")

    async def _get_sector_peers(self, company_code: str, sector: str) -> List[Dict]:
        """Get peer companies in the same sector from internal database"""
        if not self.db_pool:
            return []

        try:
            query = """
            WITH target_company AS (
                SELECT sector_17_code, sector_17_name, scale_category
                FROM companies
                WHERE code = $1 AND country = 'JP' AND is_active = true
            )
            SELECT c.code, c.name, c.name_english, c.sector_17_name, c.scale_category,
                   c.market_product_category, c.margin_code_long, c.margin_code_short
            FROM companies c, target_company t
            WHERE c.sector_17_code = t.sector_17_code
            AND c.country = 'JP'
            AND c.is_active = true
            AND c.code != $1
            AND c.scale_category = t.scale_category
            ORDER BY c.market_product_category DESC, c.scale_category DESC
            LIMIT 10
            """

            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, company_code)

            peers = []
            for row in rows:
                peers.append(
                    {
                        "code": row["code"],
                        "name": row["name"],
                        "name_english": row["name_english"],
                        "sector": row["sector_17_name"],
                        "scale_category": row["scale_category"],
                        "market_category": row["market_product_category"],
                        "margin_trading": {
                            "long": row["margin_code_long"],
                            "short": row["margin_code_short"],
                        },
                    }
                )

            logger.info(f"Found {len(peers)} sector peers for {company_code}")
            return peers

        except Exception as e:
            logger.warning(f"Error fetching sector peers for {company_code}: {e}")
            return []

    async def _get_recent_documents(self, company_code: str) -> List[Dict]:
        """Get recent document analysis history for the company"""
        if not self.db_pool:
            return []

        try:
            query = """
            SELECT
                id, title, category, published_date,
                CASE WHEN analysis IS NOT NULL THEN
                    COALESCE(analysis->>'Executive_Summary', analysis->>'summary', 'Analysis available')
                ELSE 'No analysis' END as summary,
                CASE WHEN market_analysis IS NOT NULL THEN
                    market_analysis->>'Market_Context'
                ELSE NULL END as market_context,
                confidence,
                processed_at
            FROM documents
            WHERE company_code = $1
            AND published_date > NOW() - INTERVAL '6 months'
            AND category IS NOT NULL
            ORDER BY published_date DESC
            LIMIT 8
            """

            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, company_code)

            recent_docs = []
            for row in rows:
                recent_docs.append(
                    {
                        "id": row["id"],
                        "title": row["title"],
                        "category": row["category"],
                        "published_date": row["published_date"].isoformat()
                        if row["published_date"]
                        else None,
                        "summary": row["summary"],
                        "market_context": row["market_context"],
                        "confidence": float(row["confidence"]) if row["confidence"] else None,
                        "processed_at": row["processed_at"].isoformat()
                        if row["processed_at"]
                        else None,
                    }
                )

            logger.info(f"Found {len(recent_docs)} recent documents for {company_code}")
            return recent_docs

        except Exception as e:
            logger.warning(f"Error fetching recent documents for {company_code}: {e}")
            return []

    async def _get_document_frequency_stats(self, company_code: str) -> Dict[str, Any]:
        """Get document frequency and category statistics for the company"""
        if not self.db_pool:
            return {}

        try:
            query = """
            SELECT
                category,
                COUNT(*) as document_count,
                AVG(confidence) as avg_confidence,
                MAX(published_date) as latest_document,
                MIN(published_date) as earliest_document
            FROM documents
            WHERE company_code = $1
            AND published_date > NOW() - INTERVAL '1 year'
            AND category IS NOT NULL
            GROUP BY category
            ORDER BY document_count DESC
            """

            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, company_code)

            stats = {
                "total_documents_12m": sum(row["document_count"] for row in rows),
                "categories": [],
            }

            for row in rows:
                stats["categories"].append(
                    {
                        "category": row["category"],
                        "count": row["document_count"],
                        "avg_confidence": float(row["avg_confidence"])
                        if row["avg_confidence"]
                        else None,
                        "latest_date": row["latest_document"].isoformat()
                        if row["latest_document"]
                        else None,
                        "earliest_date": row["earliest_document"].isoformat()
                        if row["earliest_document"]
                        else None,
                    }
                )

            logger.info(
                f"Document frequency stats for {company_code}: {stats['total_documents_12m']} docs in {len(stats['categories'])} categories"
            )
            return stats

        except Exception as e:
            logger.warning(f"Error fetching document frequency stats for {company_code}: {e}")
            return {}

    async def _get_company_profile(self, company_code: str) -> Dict[str, Any]:
        """Get comprehensive company profile from internal database"""
        if not self.db_pool:
            return {}

        try:
            query = """
            SELECT
                code, name, name_english, name_english_short,
                sector_17_code, sector_17_name, sector_33_code, sector_33_name,
                scale_category, market_product_category,
                listing_date, delisting_date, is_active,
                margin_code_long, margin_code_short,
                market_segment, market_section_name
            FROM companies
            WHERE code = $1 AND country = 'JP'
            LIMIT 1
            """

            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(query, company_code)

            if not row:
                return {}

            profile = {
                "database_profile": {
                    "code": row["code"],
                    "official_name": row["name"],
                    "english_name": row["name_english"],
                    "english_name_short": row["name_english_short"],
                    "sector_classification": {
                        "sector_17": {"code": row["sector_17_code"], "name": row["sector_17_name"]},
                        "sector_33": {"code": row["sector_33_code"], "name": row["sector_33_name"]},
                    },
                    "market_info": {
                        "scale_category": row["scale_category"],
                        "product_category": row["market_product_category"],
                        "segment": row["market_segment"],
                        "section_name": row["market_section_name"],
                    },
                    "listing_info": {
                        "listing_date": row["listing_date"].isoformat()
                        if row["listing_date"]
                        else None,
                        "delisting_date": row["delisting_date"].isoformat()
                        if row["delisting_date"]
                        else None,
                        "is_active": row["is_active"],
                    },
                    "margin_trading": {
                        "long_eligible": row["margin_code_long"],
                        "short_eligible": row["margin_code_short"],
                    },
                }
            }

            logger.info(f"Company profile loaded for {company_code}: {row['name']}")
            return profile

        except Exception as e:
            logger.warning(f"Error fetching company profile for {company_code}: {e}")
            return {}

    def _create_fallback_snapshot(
        self, document: Dict, company_code: str, timestamp: datetime
    ) -> CompanySnapshot:
        """Create basic snapshot when IBKR data is unavailable"""
        snapshot = CompanySnapshot(company_code, timestamp)
        snapshot.data_source = "fallback"

        # Extract basic company info from document
        company = document.get("company", {})
        if isinstance(company, dict):
            snapshot.company_info = {
                "symbol": f"{company_code}.T",
                "name": company.get("name", "Unknown Company"),
                "sector": company.get("sector", ""),
                "exchange": company.get("market", "TSE"),
                "currency": "JPY",
            }
        else:
            snapshot.company_info = {
                "symbol": f"{company_code}.T",
                "name": "Unknown Company",
                "sector": "",
                "exchange": "TSE",
                "currency": "JPY",
            }

        # No market data available
        snapshot.market_data = {"currency": "JPY"}
        snapshot.fundamentals = {}

        snapshot.data_quality = {
            "completeness_score": 0.2,  # Low score for fallback
            "missing_fields": ["market_data", "fundamentals"],
            "note": "Fallback data - IBKR unavailable",
        }

        return snapshot

    async def enrich_document(self, document: Dict) -> Dict:
        """
        Main enrichment function - adds contextual data based on document type
        """
        try:
            # Get document category
            categorization = document.get("categorization", {})
            primary_category = categorization.get("primary_category", "")

            # Get timestamp
            timestamp_str = document.get("published_at")
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
                except:
                    timestamp = datetime.now()
            else:
                timestamp = datetime.now()

            # Check if this is a corporate document
            if primary_category in ["corporate_filing", "regulatory_disclosure"]:
                # Parse company code for corporate documents
                company_code = self._parse_company_code(document)
                if not company_code:
                    logger.warning(
                        f"No valid company code found in document {document.get('id', 'unknown')}"
                    )
                    return document

                # Corporate document - add company data
                # Check cache first
                cache_key = self._get_cache_key(company_code, timestamp)
                cached_data = await self._get_cached_data(cache_key)

                if cached_data:
                    logger.info(f"Using cached data for {company_code}")
                    document["company_disclosure_snapshot"] = cached_data
                else:
                    # Fetch data from IBKR
                    logger.info(f"Fetching IBKR data for company {company_code} at {timestamp}")
                    snapshot = await self._fetch_ibkr_data(company_code, timestamp)

                    # Fall back to basic data if IBKR failed
                    if snapshot.data_quality["completeness_score"] < self.min_completeness_score:
                        logger.warning(
                            f"IBKR data quality too low for {company_code}, using fallback"
                        )
                        snapshot = self._create_fallback_snapshot(document, company_code, timestamp)

                    # Add additional data from our sources
                    await self._enhance_company_data(snapshot, company_code, document)

                    # Add snapshot to document
                    snapshot_data = snapshot.to_dict()
                    document["company_disclosure_snapshot"] = snapshot_data

                    # Cache the result
                    await self._cache_data(cache_key, snapshot_data)

                    logger.info(f"✅ Enriched corporate document with {snapshot.data_source} data")

            else:
                # Non-corporate document - fail as no real economic data source available
                logger.error(
                    f"No enrichment available for {primary_category} documents - real economic data source not implemented"
                )
                raise ValueError(
                    f"Cannot enrich {primary_category} documents: Real economic data source not available"
                )

            return document

        except Exception as e:
            logger.error(f"Error enriching document {document.get('id', 'unknown')}: {e}")
            return document
