#!/usr/bin/env python3
"""
ABOUTME: Yahoo Finance provider for global market data
ABOUTME: Implements comprehensive stock data access via yfinance library
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import yfinance as yf
import pandas as pd
from functools import lru_cache

from .base import (
    BaseProvider, ProviderCapabilities, DataType, StockPrice, 
    Fundamentals, CompanyInfo, HistoricalData, EconomicIndicators,
    ProviderError, RateLimitError, DataNotAvailableError
)

logger = logging.getLogger(__name__)


class YahooProvider(BaseProvider):
    """Yahoo Finance data provider for global stocks"""
    
    def __init__(self):
        super().__init__()
        self._rate_limit_delay = 1.0  # seconds between requests
        self._last_request_time = None
    
    def _get_capabilities(self) -> ProviderCapabilities:
        caps = ProviderCapabilities("yahoo")
        caps.supported_data_types = [
            DataType.PRICE, 
            DataType.FUNDAMENTALS,
            DataType.HISTORICAL,
            DataType.COMPANY_INFO,
            DataType.FINANCIALS,
            DataType.DIVIDENDS,
            DataType.OPTIONS,
            DataType.RECOMMENDATIONS,
            DataType.ECONOMIC_INDICATORS
        ]
        caps.supported_countries = []  # Empty means all countries
        caps.rate_limit = 30  # Conservative to avoid 429 errors
        caps.requires_auth = False
        caps.supports_batch = True
        caps.reliability_score = 0.85  # Good but can have rate limit issues
        return caps
    
    async def _rate_limit(self):
        """Enforce rate limiting"""
        if self._last_request_time:
            elapsed = datetime.now().timestamp() - self._last_request_time
            if elapsed < self._rate_limit_delay:
                await asyncio.sleep(self._rate_limit_delay - elapsed)
        self._last_request_time = datetime.now().timestamp()
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for Yahoo Finance"""
        # Yahoo uses .T for Tokyo, no changes needed
        return symbol
    
    @lru_cache(maxsize=100)
    def _get_ticker(self, symbol: str):
        """Get cached ticker object"""
        return yf.Ticker(symbol)
    
    async def get_price(self, symbol: str) -> Optional[StockPrice]:
        """Get current stock price from Yahoo Finance"""
        await self._rate_limit()
        
        try:
            ticker = self._get_ticker(self._normalize_symbol(symbol))
            
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            info = await loop.run_in_executor(None, lambda: ticker.info)
            
            if not info:
                logger.warning(f"No info available for {symbol}")
                return None
            
            # Try to get real-time price first
            price = info.get('currentPrice') or info.get('regularMarketPrice')
            if not price:
                # Fallback to previous close
                price = info.get('previousClose')
            
            if price:
                return StockPrice(
                    symbol=symbol,
                    price=float(price),
                    currency=info.get('currency', 'USD'),
                    change=info.get('regularMarketChange'),
                    change_percent=info.get('regularMarketChangePercent'),
                    volume=info.get('volume'),
                    high=info.get('dayHigh'),
                    low=info.get('dayLow'),
                    open=info.get('open'),
                    close=info.get('previousClose'),
                    bid=info.get('bid'),
                    ask=info.get('ask'),
                    timestamp=datetime.now(),
                    provider="yahoo"
                )
                
        except Exception as e:
            if "429" in str(e):
                raise RateLimitError("yahoo", "Rate limit exceeded")
            logger.error(f"Yahoo price error for {symbol}: {e}")
        
        return None
    
    async def get_fundamentals(self, symbol: str) -> Optional[Fundamentals]:
        """Get fundamental data from Yahoo Finance"""
        await self._rate_limit()
        
        try:
            ticker = self._get_ticker(self._normalize_symbol(symbol))
            
            loop = asyncio.get_event_loop()
            info = await loop.run_in_executor(None, lambda: ticker.info)
            
            # Get cash flow data for FCF
            free_cash_flow = info.get('freeCashflow')
            
            return Fundamentals(
                symbol=symbol,
                market_cap=info.get('marketCap'),
                pe_ratio=info.get('trailingPE'),
                pb_ratio=info.get('priceToBook'),
                ps_ratio=info.get('priceToSalesTrailing12Months'),
                ev_ebitda=info.get('enterpriseToEbitda'),
                dividend_yield=info.get('dividendYield'),
                roe=info.get('returnOnEquity'),
                roa=info.get('returnOnAssets'),
                debt_to_equity=info.get('debtToEquity'),
                current_ratio=info.get('currentRatio'),
                free_cash_flow=free_cash_flow,
                revenue=info.get('totalRevenue'),
                net_income=info.get('netIncomeToCommon'),
                timestamp=datetime.now(),
                provider="yahoo"
            )
            
        except Exception as e:
            if "429" in str(e):
                raise RateLimitError("yahoo", "Rate limit exceeded")
            logger.error(f"Yahoo fundamentals error for {symbol}: {e}")
        
        return None
    
    async def get_company_info(self, symbol: str) -> Optional[CompanyInfo]:
        """Get company information from Yahoo Finance"""
        await self._rate_limit()
        
        try:
            ticker = self._get_ticker(self._normalize_symbol(symbol))
            
            loop = asyncio.get_event_loop()
            info = await loop.run_in_executor(None, lambda: ticker.info)
            
            return CompanyInfo(
                symbol=symbol,
                name=info.get('longName', info.get('shortName', '')),
                sector=info.get('sector'),
                industry=info.get('industry'),
                country=info.get('country'),
                currency=info.get('currency'),
                exchange=info.get('exchange'),
                employees=info.get('fullTimeEmployees'),
                website=info.get('website'),
                description=info.get('longBusinessSummary'),
                timestamp=datetime.now(),
                provider="yahoo"
            )
            
        except Exception as e:
            if "429" in str(e):
                raise RateLimitError("yahoo", "Rate limit exceeded")
            logger.error(f"Yahoo company info error for {symbol}: {e}")
        
        return None
    
    async def get_historical(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        interval: str = "1d"
    ) -> Optional[List[HistoricalData]]:
        """Get historical price data from Yahoo Finance"""
        await self._rate_limit()
        
        try:
            ticker = self._get_ticker(self._normalize_symbol(symbol))
            
            # Convert interval format
            yf_interval = interval
            if interval == "1m":
                yf_interval = "1mo"
            
            loop = asyncio.get_event_loop()
            hist = await loop.run_in_executor(
                None,
                lambda: ticker.history(
                    start=start_date,
                    end=end_date,
                    interval=yf_interval
                )
            )
            
            if hist.empty:
                return None
            
            historical_data = []
            for date, row in hist.iterrows():
                historical_data.append(HistoricalData(
                    date=date.to_pydatetime(),
                    open=float(row['Open']),
                    high=float(row['High']),
                    low=float(row['Low']),
                    close=float(row['Close']),
                    volume=int(row['Volume']),
                    adjusted_close=float(row.get('Close')),  # Yahoo adjusts close by default
                    dividends=float(row.get('Dividends', 0)),
                    splits=float(row.get('Stock Splits', 0))
                ))
            
            return historical_data
            
        except Exception as e:
            if "429" in str(e):
                raise RateLimitError("yahoo", "Rate limit exceeded")
            logger.error(f"Yahoo historical error for {symbol}: {e}")
        
        return None
    
    async def _get_batch_prices_native(self, symbols: List[str]) -> Dict[str, Optional[StockPrice]]:
        """Get prices for multiple symbols using yfinance batch"""
        results = {}
        
        # Yahoo supports batch download
        try:
            normalized_symbols = [self._normalize_symbol(s) for s in symbols]
            symbols_str = " ".join(normalized_symbols)
            
            loop = asyncio.get_event_loop()
            tickers = await loop.run_in_executor(
                None,
                lambda: yf.download(
                    symbols_str,
                    period="1d",
                    interval="1d",
                    group_by=None,
                    auto_adjust=True,
                    prepost=True,
                    threads=True
                )
            )
            
            # Also get info for all tickers
            ticker_objects = yf.Tickers(symbols_str)
            
            for symbol, normalized in zip(symbols, normalized_symbols):
                try:
                    if len(symbols) == 1:
                        # Single symbol returns Series
                        last_row = tickers.iloc[-1] if not tickers.empty else None
                    else:
                        # Multiple symbols returns MultiIndex DataFrame
                        if normalized in tickers.columns.levels[1]:
                            ticker_data = tickers.xs(normalized, level=1, axis=1)
                            last_row = ticker_data.iloc[-1] if not ticker_data.empty else None
                        else:
                            last_row = None
                    
                    if last_row is not None and not pd.isna(last_row['Close']):
                        # Get additional info
                        ticker_obj = getattr(ticker_objects.tickers, normalized, None)
                        info = ticker_obj.info if ticker_obj else {}
                        
                        results[symbol] = StockPrice(
                            symbol=symbol,
                            price=float(last_row['Close']),
                            currency=info.get('currency', 'USD'),
                            volume=int(last_row['Volume']) if not pd.isna(last_row['Volume']) else None,
                            high=float(last_row['High']) if not pd.isna(last_row['High']) else None,
                            low=float(last_row['Low']) if not pd.isna(last_row['Low']) else None,
                            open=float(last_row['Open']) if not pd.isna(last_row['Open']) else None,
                            close=float(last_row['Close']),
                            timestamp=datetime.now(),
                            provider="yahoo"
                        )
                    else:
                        results[symbol] = None
                        
                except Exception as e:
                    logger.error(f"Error processing {symbol} in batch: {e}")
                    results[symbol] = None
                    
        except Exception as e:
            if "429" in str(e):
                raise RateLimitError("yahoo", "Rate limit exceeded")
            logger.error(f"Yahoo batch price error: {e}")
            # Return None for all symbols on error
            for symbol in symbols:
                results[symbol] = None
        
        return results
    
    async def get_dividends(self, symbol: str) -> Optional[List[Dict[str, Any]]]:
        """Get dividend history"""
        await self._rate_limit()
        
        try:
            ticker = self._get_ticker(self._normalize_symbol(symbol))
            
            loop = asyncio.get_event_loop()
            dividends = await loop.run_in_executor(None, lambda: ticker.dividends)
            
            if dividends.empty:
                return None
            
            dividend_list = []
            for date, amount in dividends.items():
                dividend_list.append({
                    "date": date.to_pydatetime(),
                    "amount": float(amount),
                    "symbol": symbol
                })
            
            return dividend_list
            
        except Exception as e:
            logger.error(f"Yahoo dividends error for {symbol}: {e}")
        
        return None
    
    async def get_options(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get options chain data"""
        await self._rate_limit()
        
        try:
            ticker = self._get_ticker(self._normalize_symbol(symbol))
            
            loop = asyncio.get_event_loop()
            
            # Get expiration dates
            expirations = await loop.run_in_executor(None, lambda: ticker.options)
            
            if not expirations:
                return None
            
            # Get options for next expiration
            opt_chain = await loop.run_in_executor(
                None, 
                lambda: ticker.option_chain(expirations[0])
            )
            
            return {
                "symbol": symbol,
                "expirations": list(expirations),
                "calls": opt_chain.calls.to_dict('records') if not opt_chain.calls.empty else [],
                "puts": opt_chain.puts.to_dict('records') if not opt_chain.puts.empty else [],
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Yahoo options error for {symbol}: {e}")
        
        return None
    
    async def get_economic_indicators(self, country: str) -> Optional[EconomicIndicators]:
        """Get economic indicators using Yahoo Finance symbols"""
        await self._rate_limit()
        
        country = country.upper()
        
        try:
            indicators = EconomicIndicators(
                country=country,
                timestamp=datetime.now(),
                provider="yahoo",
                source_urls=[]
            )
            
            # Define symbols for different countries
            symbols_map = {
                "JP": {
                    "stock_index": "^N225",  # Nikkei 225
                    "currency": "JPYUSD=X",  # JPY/USD
                    "bond_10y": "^TNX",  # US 10-year (fallback, JP bond not available)
                },
                "US": {
                    "stock_index": "^GSPC",  # S&P 500
                    "currency": "DX-Y.NYB",  # Dollar Index
                    "bond_10y": "^TNX",  # 10-year Treasury
                }
            }
            
            if country not in symbols_map:
                logger.warning(f"No Yahoo Finance symbols defined for country: {country}")
                return None
            
            symbols = symbols_map[country]
            
            # Get stock index data
            if "stock_index" in symbols:
                try:
                    ticker = self._get_ticker(symbols["stock_index"])
                    loop = asyncio.get_event_loop()
                    info = await loop.run_in_executor(None, lambda: ticker.info)
                    
                    if info:
                        price = info.get('regularMarketPrice') or info.get('previousClose')
                        if price:
                            indicators.stock_index = float(price)
                            if country == "JP":
                                indicators.stock_index_name = "Nikkei 225"
                            elif country == "US":
                                indicators.stock_index_name = "S&P 500"
                                
                        indicators.source_urls.append(f"https://finance.yahoo.com/quote/{symbols['stock_index']}")
                        
                except Exception as e:
                    logger.warning(f"Failed to get stock index for {country}: {e}")
            
            # Get currency data
            if "currency" in symbols:
                try:
                    ticker = self._get_ticker(symbols["currency"])
                    loop = asyncio.get_event_loop()
                    info = await loop.run_in_executor(None, lambda: ticker.info)
                    
                    if info:
                        rate = info.get('regularMarketPrice') or info.get('previousClose')
                        if rate:
                            if country == "JP":
                                # For JPYUSD=X, we get JPY per 1 USD, convert to USD per JPY
                                indicators.currency_rate = 1.0 / float(rate)
                            else:
                                indicators.currency_rate = float(rate)
                                
                        indicators.source_urls.append(f"https://finance.yahoo.com/quote/{symbols['currency']}")
                        
                except Exception as e:
                    logger.warning(f"Failed to get currency rate for {country}: {e}")
            
            # Get bond yield data
            if "bond_10y" in symbols:
                try:
                    ticker = self._get_ticker(symbols["bond_10y"])
                    loop = asyncio.get_event_loop()
                    info = await loop.run_in_executor(None, lambda: ticker.info)
                    
                    if info:
                        yield_val = info.get('regularMarketPrice') or info.get('previousClose')
                        if yield_val:
                            indicators.bond_yield_10y = float(yield_val)
                            
                        indicators.source_urls.append(f"https://finance.yahoo.com/quote/{symbols['bond_10y']}")
                        
                except Exception as e:
                    logger.warning(f"Failed to get bond yield for {country}: {e}")
            
            # Check if we got any data
            has_data = any([
                indicators.stock_index is not None,
                indicators.currency_rate is not None,
                indicators.bond_yield_10y is not None
            ])
            
            if not has_data:
                raise DataNotAvailableError(
                    "yahoo", 
                    f"No economic indicators available from Yahoo Finance for {country}"
                )
            
            return indicators
            
        except Exception as e:
            if isinstance(e, DataNotAvailableError):
                raise
            if "429" in str(e):
                raise RateLimitError("yahoo", "Rate limit exceeded")
            logger.error(f"Yahoo economic indicators error for {country}: {e}")
            raise DataNotAvailableError("yahoo", f"Failed to fetch economic data: {str(e)}")

    async def health_check(self) -> Dict[str, Any]:
        """Check Yahoo Finance connectivity"""
        try:
            # Try to get a well-known stock
            price = await self.get_price("AAPL")
            healthy = price is not None
            
            return {
                "provider": "yahoo",
                "healthy": healthy,
                "last_request": self._last_request_time,
                "capabilities": {
                    "data_types": [dt.value for dt in self.capabilities.supported_data_types],
                    "countries": "global",
                    "batch_support": self.capabilities.supports_batch,
                    "rate_limit": self.capabilities.rate_limit
                }
            }
        except Exception as e:
            return {
                "provider": "yahoo",
                "healthy": False,
                "error": str(e),
                "capabilities": {
                    "data_types": [dt.value for dt in self.capabilities.supported_data_types],
                    "countries": "global",
                    "batch_support": self.capabilities.supports_batch,
                    "rate_limit": self.capabilities.rate_limit
                }
            }