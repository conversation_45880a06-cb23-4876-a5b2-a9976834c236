#!/usr/bin/env python3
"""
ABOUTME: Interactive Brokers provider for professional market data
ABOUTME: Implements real-time and historical data access via ib_insync
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import numpy as np
from ib_insync import IB, Stock, Contract, Forex, util

from .base import (
    BaseProvider, ProviderCapabilities, DataType, StockPrice, 
    Fundamentals, CompanyInfo, HistoricalData, ProviderError,
    DataNotAvailableError
)

logger = logging.getLogger(__name__)


class IBKRProvider(BaseProvider):
    """Interactive Brokers data provider"""
    
    def __init__(self):
        super().__init__()
        self.ib = IB()
        self.host = os.getenv('IB_HOST', 'localhost')
        self.port = int(os.getenv('IB_PORT', 7497))
        self.client_id = int(os.getenv('IB_CLIENT_ID', 1))
        self._connected = False
    
    def _get_capabilities(self) -> ProviderCapabilities:
        caps = ProviderCapabilities("ibkr")
        caps.supported_data_types = [
            DataType.PRICE, 
            DataType.FUNDAMENTALS,
            DataType.HISTORICAL,
            DataType.OPTIONS
        ]
        caps.supported_countries = []  # Global coverage
        caps.rate_limit = None  # No hard limit but be reasonable
        caps.requires_auth = True  # Requires TWS/Gateway
        caps.supports_batch = False
        caps.reliability_score = 0.95  # Very reliable when connected
        return caps
    
    async def _ensure_connected(self):
        """Ensure connection to IB Gateway/TWS"""
        if not self._connected or not self.ib.isConnected():
            try:
                await self.ib.connectAsync(self.host, self.port, clientId=self.client_id)
                self._connected = True
                logger.info(f"Connected to IBKR at {self.host}:{self.port}")
            except Exception as e:
                self._connected = False
                raise ProviderError("ibkr", f"Failed to connect: {e}")
    
    def _create_contract(self, symbol: str) -> Contract:
        """Create appropriate contract based on symbol format"""
        # Handle different market conventions
        if symbol.endswith('.T'):
            # Japanese stock
            clean_symbol = symbol[:-2]
            return Stock(clean_symbol, 'TSEJ', 'JPY')
        elif symbol.endswith('.HK'):
            # Hong Kong stock
            clean_symbol = symbol[:-3]
            return Stock(clean_symbol, 'SEHK', 'HKD')
        elif symbol.endswith('.L'):
            # London stock
            clean_symbol = symbol[:-2]
            return Stock(clean_symbol, 'LSE', 'GBP')
        elif '=' in symbol:
            # Forex pair
            if symbol == 'USDJPY=X':
                return Forex('USDJPY')
            elif symbol == 'EURUSD=X':
                return Forex('EURUSD')
        else:
            # Default to US stock
            return Stock(symbol, 'SMART', 'USD')
    
    async def get_price(self, symbol: str) -> Optional[StockPrice]:
        """Get current stock price from IBKR"""
        await self._ensure_connected()
        
        try:
            contract = self._create_contract(symbol)
            
            # Qualify contract
            contracts = await self.ib.qualifyContractsAsync(contract)
            if not contracts:
                raise DataNotAvailableError("ibkr", f"Cannot find contract for {symbol}")
            
            qualified = contracts[0]
            
            # Request market data
            self.ib.reqMarketDataType(1)  # Live data
            ticker = self.ib.reqMktData(qualified, '', False, False)
            
            # Wait for data
            await asyncio.sleep(2)
            
            # If no live data, try delayed
            if not ticker.last or np.isnan(ticker.last):
                self.ib.cancelMktData(ticker.contract)
                self.ib.reqMarketDataType(3)  # Delayed data
                self.ib.reqMktData(qualified, '', False, False)
                await asyncio.sleep(2)
                ticker = self.ib.ticker(qualified)
            
            # Cancel subscription
            self.ib.cancelMktData(ticker.contract)
            
            # Build price object
            price = ticker.last if ticker.last and not np.isnan(ticker.last) else None
            if not price and ticker.close and not np.isnan(ticker.close):
                price = ticker.close
            
            if price:
                return StockPrice(
                    symbol=symbol,
                    price=float(price),
                    currency=qualified.currency,
                    change=None,  # Calculate if we have previous close
                    change_percent=None,
                    volume=int(ticker.volume) if ticker.volume and not np.isnan(ticker.volume) else None,
                    high=float(ticker.high) if ticker.high and not np.isnan(ticker.high) else None,
                    low=float(ticker.low) if ticker.low and not np.isnan(ticker.low) else None,
                    open=float(ticker.open) if ticker.open and not np.isnan(ticker.open) else None,
                    close=float(ticker.close) if ticker.close and not np.isnan(ticker.close) else None,
                    bid=float(ticker.bid) if ticker.bid and not np.isnan(ticker.bid) else None,
                    ask=float(ticker.ask) if ticker.ask and not np.isnan(ticker.ask) else None,
                    timestamp=datetime.now(),
                    provider="ibkr"
                )
                
        except Exception as e:
            logger.error(f"IBKR price error for {symbol}: {e}")
        
        return None
    
    async def get_fundamentals(self, symbol: str) -> Optional[Fundamentals]:
        """Get fundamental data from IBKR (requires subscription)"""
        await self._ensure_connected()
        
        try:
            contract = self._create_contract(symbol)
            
            # Qualify contract
            contracts = await self.ib.qualifyContractsAsync(contract)
            if not contracts:
                raise DataNotAvailableError("ibkr", f"Cannot find contract for {symbol}")
            
            qualified = contracts[0]
            
            # Request fundamental data
            fundamental_data = await self.ib.reqFundamentalDataAsync(
                qualified, 
                'ReportsFinSummary'
            )
            
            if not fundamental_data:
                return None
            
            # Parse XML data (simplified - would need proper XML parsing)
            import re
            
            fundamentals = Fundamentals(symbol=symbol, provider="ibkr")
            
            # Market cap
            market_cap_match = re.search(r'<MarketCap[^>]*?>([^<]+)</MarketCap>', fundamental_data)
            if market_cap_match:
                try:
                    value = market_cap_match.group(1).replace(',', '')
                    if value.endswith('M'):
                        fundamentals.market_cap = float(value[:-1]) * 1e6
                    elif value.endswith('B'):
                        fundamentals.market_cap = float(value[:-1]) * 1e9
                    else:
                        fundamentals.market_cap = float(value)
                except:
                    pass
            
            # P/E ratio
            pe_match = re.search(r'<PERatio[^>]*?>([^<]+)</PERatio>', fundamental_data)
            if pe_match:
                try:
                    fundamentals.pe_ratio = float(pe_match.group(1))
                except:
                    pass
            
            fundamentals.timestamp = datetime.now()
            return fundamentals
            
        except Exception as e:
            logger.error(f"IBKR fundamentals error for {symbol}: {e}")
        
        return None
    
    async def get_company_info(self, symbol: str) -> Optional[CompanyInfo]:
        """Get company information from IBKR"""
        await self._ensure_connected()
        
        try:
            contract = self._create_contract(symbol)
            
            # Qualify contract
            contracts = await self.ib.qualifyContractsAsync(contract)
            if not contracts:
                return None
            
            qualified = contracts[0]
            
            # Get contract details
            details = await self.ib.reqContractDetailsAsync(qualified)
            if details:
                detail = details[0]
                return CompanyInfo(
                    symbol=symbol,
                    name=detail.longName or detail.contract.symbol,
                    sector=detail.industry,
                    industry=detail.category,
                    currency=qualified.currency,
                    exchange=qualified.exchange,
                    timestamp=datetime.now(),
                    provider="ibkr"
                )
                
        except Exception as e:
            logger.error(f"IBKR company info error for {symbol}: {e}")
        
        return None
    
    async def get_historical(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        interval: str = "1d"
    ) -> Optional[List[HistoricalData]]:
        """Get historical price data from IBKR"""
        await self._ensure_connected()
        
        try:
            contract = self._create_contract(symbol)
            
            # Qualify contract
            contracts = await self.ib.qualifyContractsAsync(contract)
            if not contracts:
                return None
            
            qualified = contracts[0]
            
            # Calculate duration
            duration_days = (end_date - start_date).days
            if duration_days <= 1:
                duration_str = "1 D"
            elif duration_days <= 7:
                duration_str = f"{duration_days} D"
            elif duration_days <= 30:
                duration_str = "1 M"
            elif duration_days <= 365:
                duration_str = f"{duration_days // 30} M"
            else:
                duration_str = f"{duration_days // 365} Y"
            
            # Convert interval
            bar_size_map = {
                "1m": "1 min",
                "5m": "5 mins",
                "15m": "15 mins",
                "30m": "30 mins",
                "1h": "1 hour",
                "1d": "1 day",
                "1w": "1 week",
                "1M": "1 month"
            }
            bar_size = bar_size_map.get(interval, "1 day")
            
            # Get historical data
            bars = await self.ib.reqHistoricalDataAsync(
                qualified,
                endDateTime='',
                durationStr=duration_str,
                barSizeSetting=bar_size,
                whatToShow='TRADES',
                useRTH=True,
                formatDate=1
            )
            
            if not bars:
                return None
            
            historical_data = []
            for bar in bars:
                historical_data.append(HistoricalData(
                    date=bar.date,
                    open=float(bar.open),
                    high=float(bar.high),
                    low=float(bar.low),
                    close=float(bar.close),
                    volume=int(bar.volume) if bar.volume else 0
                ))
            
            return historical_data
            
        except Exception as e:
            logger.error(f"IBKR historical error for {symbol}: {e}")
        
        return None
    
    async def health_check(self) -> Dict[str, Any]:
        """Check IBKR connectivity"""
        try:
            connected = self.ib.isConnected()
            if not connected:
                await self._ensure_connected()
                connected = self.ib.isConnected()
            
            return {
                "provider": "ibkr",
                "healthy": connected,
                "connected": connected,
                "host": self.host,
                "port": self.port,
                "client_id": self.client_id,
                "capabilities": {
                    "data_types": [dt.value for dt in self.capabilities.supported_data_types],
                    "countries": "global",
                    "batch_support": self.capabilities.supports_batch,
                    "requires_tws": True
                }
            }
        except Exception as e:
            return {
                "provider": "ibkr",
                "healthy": False,
                "connected": False,
                "error": str(e),
                "capabilities": {
                    "data_types": [dt.value for dt in self.capabilities.supported_data_types],
                    "countries": "global",
                    "batch_support": self.capabilities.supports_batch,
                    "requires_tws": True
                }
            }
    
    async def __aenter__(self):
        await self._ensure_connected()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._connected:
            self.ib.disconnect()
            self._connected = False