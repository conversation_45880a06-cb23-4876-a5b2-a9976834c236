#!/usr/bin/env python3
"""
ABOUTME: JQuants API provider for Japanese market data
ABOUTME: Implements comprehensive Japanese stock data access via JQuants API
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import httpx
import pandas as pd

from .base import (
    BaseProvider, ProviderCapabilities, DataType, StockPrice, 
    Fundamentals, CompanyInfo, HistoricalData, ProviderError,
    RateLimitError, AuthenticationError, DataNotAvailableError
)

logger = logging.getLogger(__name__)

JQUANTS_BASE_URL = "https://api.jquants.com/v1"


class JQuantsProvider(BaseProvider):
    """JQuants data provider for Japanese stocks"""
    
    def __init__(self):
        super().__init__()
        self.api_key = os.getenv('JQUANTS_API_KEY')
        self.email = os.getenv('JQUANTS_MAILADDRESS')
        self.password = os.getenv('JQUANTS_PASSWORD')
        self._access_token = None
        self._token_expires_at = None
        self._client = httpx.AsyncClient(timeout=30.0)
    
    def _get_capabilities(self) -> ProviderCapabilities:
        caps = ProviderCapabilities("jquants")
        caps.supported_data_types = [
            DataType.PRICE, 
            DataType.FUNDAMENTALS,
            DataType.HISTORICAL,
            DataType.COMPANY_INFO,
            DataType.FINANCIALS,
            DataType.DIVIDENDS
        ]
        caps.supported_countries = ["JP"]
        caps.rate_limit = 60  # 60 requests per minute
        caps.requires_auth = True
        caps.supports_batch = True
        caps.reliability_score = 0.95  # Very reliable for JP data
        return caps
    
    async def _ensure_authenticated(self):
        """Ensure we have a valid access token"""
        if self._access_token and self._token_expires_at and datetime.now() < self._token_expires_at:
            return
        
        # Get new token using email/password authentication flow
        if self.email and self.password:
            try:
                # Step 1: Get refresh token
                auth_data = {
                    "mailaddress": self.email,
                    "password": self.password
                }
                response = await self._client.post(
                    f"{JQUANTS_BASE_URL}/token/auth_user",
                    json=auth_data
                )
                if response.status_code != 200:
                    raise AuthenticationError("jquants", f"Auth failed: {response.status_code}")
                
                refresh_token = response.json().get("refreshToken")
                if not refresh_token:
                    raise AuthenticationError("jquants", "No refresh token received")
                
                # Step 2: Get access token from refresh token
                token_response = await self._client.post(
                    f"{JQUANTS_BASE_URL}/token/auth_refresh",
                    params={"refreshtoken": refresh_token}
                )
                if token_response.status_code != 200:
                    raise AuthenticationError("jquants", f"Token refresh failed: {token_response.status_code}")
                
                data = token_response.json()
                self._access_token = data.get("idToken")
                if not self._access_token:
                    raise AuthenticationError("jquants", "No access token received")
                
                # JQuants tokens expire in 24 hours, refresh after 23 hours
                self._token_expires_at = datetime.now() + timedelta(hours=23)
                logger.info("JQuants authentication successful")
                
            except httpx.RequestError as e:
                raise ProviderError("jquants", f"Network error during authentication: {e}")
        else:
            raise AuthenticationError("jquants", "No JQuants credentials configured")
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for JQuants (remove .T suffix)"""
        return symbol.replace('.T', '') if symbol.endswith('.T') else symbol
    
    def _add_suffix(self, symbol: str) -> str:
        """Add .T suffix for consistency"""
        return f"{symbol}.T" if not symbol.endswith('.T') else symbol
    
    async def get_price(self, symbol: str) -> Optional[StockPrice]:
        """Get current stock price from JQuants"""
        await self._ensure_authenticated()
        clean_symbol = self._normalize_symbol(symbol)
        
        try:
            headers = {"Authorization": f"Bearer {self._access_token}"}
            
            # Get latest price data
            response = await self._client.get(
                f"{JQUANTS_BASE_URL}/prices/daily_quotes",
                params={"code": clean_symbol},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                quotes = data.get("daily_quotes", [])
                
                if quotes:
                    # Get most recent quote
                    latest = quotes[-1]
                    return StockPrice(
                        symbol=self._add_suffix(clean_symbol),
                        price=float(latest.get("Close", 0)),
                        currency="JPY",
                        change=float(latest.get("Close", 0)) - float(latest.get("PreviousClose", 0)),
                        change_percent=float(latest.get("ChangePercent", 0)),
                        volume=int(latest.get("Volume", 0)),
                        high=float(latest.get("High", 0)),
                        low=float(latest.get("Low", 0)),
                        open=float(latest.get("Open", 0)),
                        close=float(latest.get("Close", 0)),
                        timestamp=datetime.fromisoformat(latest.get("Date")),
                        provider="jquants"
                    )
            elif response.status_code == 429:
                raise RateLimitError("jquants", "Rate limit exceeded")
            
        except httpx.RequestError as e:
            logger.error(f"JQuants price error for {symbol}: {e}")
        
        return None
    
    async def get_fundamentals(self, symbol: str) -> Optional[Fundamentals]:
        """Get fundamental data from JQuants"""
        await self._ensure_authenticated()
        clean_symbol = self._normalize_symbol(symbol)
        
        try:
            headers = {"Authorization": f"Bearer {self._access_token}"}
            
            # Get financial statements
            response = await self._client.get(
                f"{JQUANTS_BASE_URL}/fins/statements",
                params={"code": clean_symbol},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                statements = data.get("statements", [])
                
                if statements:
                    # Get most recent statement
                    latest = statements[-1]
                    
                    # Calculate metrics
                    market_cap = None
                    pe_ratio = None
                    
                    # Get price for calculations
                    price_data = await self.get_price(symbol)
                    if price_data:
                        shares = latest.get("NumberOfIssuedAndOutstandingSharesAtTheEndOfFiscalYearIncludingTreasuryStock")
                        if shares:
                            market_cap = price_data.price * shares
                        
                        eps = latest.get("EarningsPerShare")
                        if eps and eps > 0:
                            pe_ratio = price_data.price / eps
                    
                    return Fundamentals(
                        symbol=self._add_suffix(clean_symbol),
                        market_cap=market_cap,
                        pe_ratio=pe_ratio,
                        pb_ratio=latest.get("PriceBookValueRatio"),
                        roe=latest.get("ReturnOnEquity"),
                        roa=latest.get("ReturnOnAssets"),
                        debt_to_equity=latest.get("EquityToAssetRatio"),
                        revenue=latest.get("NetSales"),
                        net_income=latest.get("NetIncome"),
                        timestamp=datetime.now(),
                        provider="jquants"
                    )
            
        except httpx.RequestError as e:
            logger.error(f"JQuants fundamentals error for {symbol}: {e}")
        
        return None
    
    async def get_company_info(self, symbol: str) -> Optional[CompanyInfo]:
        """Get company information from JQuants"""
        await self._ensure_authenticated()
        clean_symbol = self._normalize_symbol(symbol)
        
        try:
            headers = {"Authorization": f"Bearer {self._access_token}"}
            
            # Get listed info
            response = await self._client.get(
                f"{JQUANTS_BASE_URL}/listed/info",
                params={"code": clean_symbol},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                info_list = data.get("info", [])
                
                if info_list:
                    info = info_list[0]
                    return CompanyInfo(
                        symbol=self._add_suffix(clean_symbol),
                        name=info.get("CompanyName", ""),
                        name_english=info.get("CompanyNameEnglish"),
                        sector=info.get("Sector17CodeName"),
                        industry=info.get("Sector33CodeName"),
                        country="JP",
                        currency="JPY",
                        exchange="TSE",
                        market_code=info.get("MarketCode"),
                        timestamp=datetime.now(),
                        provider="jquants"
                    )
            
        except httpx.RequestError as e:
            logger.error(f"JQuants company info error for {symbol}: {e}")
        
        return None
    
    async def get_historical(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        interval: str = "1d"
    ) -> Optional[List[HistoricalData]]:
        """Get historical price data from JQuants"""
        await self._ensure_authenticated()
        clean_symbol = self._normalize_symbol(symbol)
        
        try:
            headers = {"Authorization": f"Bearer {self._access_token}"}
            
            # Format dates
            from_date = start_date.strftime("%Y-%m-%d")
            to_date = end_date.strftime("%Y-%m-%d")
            
            response = await self._client.get(
                f"{JQUANTS_BASE_URL}/prices/daily_quotes",
                params={
                    "code": clean_symbol,
                    "from": from_date,
                    "to": to_date
                },
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                quotes = data.get("daily_quotes", [])
                
                historical_data = []
                for quote in quotes:
                    historical_data.append(HistoricalData(
                        date=datetime.fromisoformat(quote["Date"]),
                        open=float(quote["Open"]),
                        high=float(quote["High"]),
                        low=float(quote["Low"]),
                        close=float(quote["Close"]),
                        volume=int(quote["Volume"]),
                        adjusted_close=float(quote.get("AdjustmentClose", quote["Close"]))
                    ))
                
                return historical_data
            
        except httpx.RequestError as e:
            logger.error(f"JQuants historical error for {symbol}: {e}")
        
        return None
    
    async def _get_batch_prices_native(self, symbols: List[str]) -> Dict[str, Optional[StockPrice]]:
        """Get prices for multiple symbols efficiently"""
        await self._ensure_authenticated()
        results = {}
        
        # JQuants doesn't have true batch endpoint, but we can request all stocks
        # and filter to our symbols for efficiency
        clean_symbols = [self._normalize_symbol(s) for s in symbols]
        
        try:
            headers = {"Authorization": f"Bearer {self._access_token}"}
            
            # Get all daily quotes (limited to today)
            response = await self._client.get(
                f"{JQUANTS_BASE_URL}/prices/daily_quotes",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                all_quotes = data.get("daily_quotes", [])
                
                # Group by code
                quotes_by_code = {}
                for quote in all_quotes:
                    code = quote.get("Code")
                    if code in clean_symbols:
                        quotes_by_code[code] = quote
                
                # Process each requested symbol
                for symbol, clean_symbol in zip(symbols, clean_symbols):
                    if clean_symbol in quotes_by_code:
                        quote = quotes_by_code[clean_symbol]
                        results[symbol] = StockPrice(
                            symbol=self._add_suffix(clean_symbol),
                            price=float(quote.get("Close", 0)),
                            currency="JPY",
                            volume=int(quote.get("Volume", 0)),
                            high=float(quote.get("High", 0)),
                            low=float(quote.get("Low", 0)),
                            open=float(quote.get("Open", 0)),
                            close=float(quote.get("Close", 0)),
                            timestamp=datetime.fromisoformat(quote.get("Date")),
                            provider="jquants"
                        )
                    else:
                        results[symbol] = None
            
        except httpx.RequestError as e:
            logger.error(f"JQuants batch price error: {e}")
            # Return None for all symbols on error
            for symbol in symbols:
                results[symbol] = None
        
        return results
    
    async def health_check(self) -> Dict[str, Any]:
        """Check JQuants connectivity and authentication"""
        try:
            await self._ensure_authenticated()
            
            # Try a simple API call
            headers = {"Authorization": f"Bearer {self._access_token}"}
            response = await self._client.get(
                f"{JQUANTS_BASE_URL}/listed/info",
                params={"code": "7203"},  # Toyota
                headers=headers
            )
            
            healthy = response.status_code == 200
            
            return {
                "provider": "jquants",
                "healthy": healthy,
                "authenticated": self._access_token is not None,
                "token_expires": self._token_expires_at.isoformat() if self._token_expires_at else None,
                "capabilities": {
                    "data_types": [dt.value for dt in self.capabilities.supported_data_types],
                    "countries": self.capabilities.supported_countries,
                    "batch_support": self.capabilities.supports_batch,
                    "rate_limit": self.capabilities.rate_limit
                }
            }
        except Exception as e:
            return {
                "provider": "jquants",
                "healthy": False,
                "error": str(e),
                "capabilities": {
                    "data_types": [dt.value for dt in self.capabilities.supported_data_types],
                    "countries": self.capabilities.supported_countries,
                    "batch_support": self.capabilities.supports_batch,
                    "rate_limit": self.capabilities.rate_limit
                }
            }
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._client.aclose()