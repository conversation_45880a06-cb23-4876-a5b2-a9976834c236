#!/usr/bin/env python3
"""
ABOUTME: Base provider interface for all financial data providers
ABOUTME: Defines common interface and data models for provider implementations
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import asyncio


class DataType(Enum):
    """Types of financial data available"""
    PRICE = "price"
    FUNDAMENTALS = "fundamentals"
    HISTORICAL = "historical"
    OPTIONS = "options"
    DIVIDENDS = "dividends"
    COMPANY_INFO = "company_info"
    FINANCIALS = "financials"
    RECOMMENDATIONS = "recommendations"
    ECONOMIC_INDICATORS = "economic_indicators"


class ProviderName(Enum):
    """Available data providers"""
    JQUANTS = "jquants"
    YAHOO = "yahoo"
    IBKR = "ibkr"
    BOJ = "boj"  # Bank of Japan
    AUTO = "auto"  # Automatic provider selection


@dataclass
class StockPrice:
    """Unified stock price data"""
    symbol: str
    price: float
    currency: str
    change: Optional[float] = None
    change_percent: Optional[float] = None
    volume: Optional[int] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None
    close: Optional[float] = None
    bid: Optional[float] = None
    ask: Optional[float] = None
    timestamp: Optional[datetime] = None
    provider: Optional[str] = None


@dataclass
class Fundamentals:
    """Unified fundamental data"""
    symbol: str
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None
    pb_ratio: Optional[float] = None
    ps_ratio: Optional[float] = None
    ev_ebitda: Optional[float] = None
    dividend_yield: Optional[float] = None
    roe: Optional[float] = None
    roa: Optional[float] = None
    debt_to_equity: Optional[float] = None
    current_ratio: Optional[float] = None
    free_cash_flow: Optional[float] = None
    revenue: Optional[float] = None
    net_income: Optional[float] = None
    timestamp: Optional[datetime] = None
    provider: Optional[str] = None


@dataclass
class CompanyInfo:
    """Unified company information"""
    symbol: str
    name: str
    name_english: Optional[str] = None
    sector: Optional[str] = None
    industry: Optional[str] = None
    country: Optional[str] = None
    currency: Optional[str] = None
    exchange: Optional[str] = None
    market_code: Optional[str] = None
    employees: Optional[int] = None
    website: Optional[str] = None
    description: Optional[str] = None
    timestamp: Optional[datetime] = None
    provider: Optional[str] = None


@dataclass
class HistoricalData:
    """Historical price data point"""
    date: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    adjusted_close: Optional[float] = None
    dividends: Optional[float] = None
    splits: Optional[float] = None


@dataclass
class EconomicIndicators:
    """Economic indicators data"""
    country: str
    gdp_growth: Optional[float] = None
    inflation_rate: Optional[float] = None
    unemployment_rate: Optional[float] = None
    policy_rate: Optional[float] = None
    bond_yield_10y: Optional[float] = None
    currency_rate: Optional[float] = None  # vs USD
    stock_index: Optional[float] = None
    stock_index_name: Optional[str] = None
    industrial_production: Optional[float] = None
    trade_balance: Optional[float] = None
    consumer_confidence: Optional[float] = None
    business_confidence: Optional[float] = None
    timestamp: Optional[datetime] = None
    provider: Optional[str] = None
    source_urls: Optional[List[str]] = None


class ProviderCapabilities:
    """Defines what data types a provider supports"""
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
        self.supported_data_types: List[DataType] = []
        self.supported_countries: List[str] = []
        self.rate_limit: Optional[int] = None  # requests per minute
        self.requires_auth: bool = False
        self.supports_batch: bool = False
        self.reliability_score: float = 1.0  # 0-1, used for fallback ordering


class BaseProvider(ABC):
    """Abstract base class for all financial data providers"""
    
    def __init__(self):
        self.capabilities = self._get_capabilities()
        self._rate_limiter = None
    
    @abstractmethod
    def _get_capabilities(self) -> ProviderCapabilities:
        """Define provider capabilities"""
        pass
    
    @abstractmethod
    async def get_price(self, symbol: str) -> Optional[StockPrice]:
        """Get current stock price"""
        pass
    
    @abstractmethod
    async def get_fundamentals(self, symbol: str) -> Optional[Fundamentals]:
        """Get fundamental data"""
        pass
    
    @abstractmethod
    async def get_company_info(self, symbol: str) -> Optional[CompanyInfo]:
        """Get company information"""
        pass
    
    @abstractmethod
    async def get_historical(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        interval: str = "1d"
    ) -> Optional[List[HistoricalData]]:
        """Get historical price data"""
        pass
    
    async def get_economic_indicators(self, country: str) -> Optional[EconomicIndicators]:
        """Get economic indicators for a country (optional for providers)"""
        return None
    
    async def get_batch_prices(self, symbols: List[str]) -> Dict[str, Optional[StockPrice]]:
        """Get prices for multiple symbols (default: sequential calls)"""
        if self.capabilities.supports_batch:
            return await self._get_batch_prices_native(symbols)
        
        # Fallback to sequential calls with rate limiting
        results = {}
        for symbol in symbols:
            results[symbol] = await self.get_price(symbol)
            if self.capabilities.rate_limit:
                await asyncio.sleep(60 / self.capabilities.rate_limit)
        return results
    
    async def _get_batch_prices_native(self, symbols: List[str]) -> Dict[str, Optional[StockPrice]]:
        """Native batch implementation (override in subclass if supported)"""
        raise NotImplementedError("Provider does not support native batch requests")
    
    def supports_data_type(self, data_type: DataType) -> bool:
        """Check if provider supports a data type"""
        return data_type in self.capabilities.supported_data_types
    
    def supports_country(self, country: str) -> bool:
        """Check if provider supports a country"""
        return country in self.capabilities.supported_countries or not self.capabilities.supported_countries
    
    async def health_check(self) -> Dict[str, Any]:
        """Check provider health and connectivity"""
        return {
            "provider": self.capabilities.provider_name,
            "healthy": True,
            "capabilities": {
                "data_types": [dt.value for dt in self.capabilities.supported_data_types],
                "countries": self.capabilities.supported_countries,
                "batch_support": self.capabilities.supports_batch,
                "rate_limit": self.capabilities.rate_limit
            }
        }


class ProviderError(Exception):
    """Base exception for provider errors"""
    def __init__(self, provider: str, message: str, original_error: Optional[Exception] = None):
        self.provider = provider
        self.original_error = original_error
        super().__init__(f"{provider}: {message}")


class RateLimitError(ProviderError):
    """Rate limit exceeded"""
    pass


class AuthenticationError(ProviderError):
    """Authentication failed"""
    pass


class DataNotAvailableError(ProviderError):
    """Requested data not available"""
    pass