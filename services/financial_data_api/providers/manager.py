#!/usr/bin/env python3
"""
ABOUTME: Provider manager for intelligent provider selection and fallback
ABOUTME: Handles automatic failover, caching, and optimal provider routing
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime
import json

from .base import (
    BaseProvider, ProviderName, DataType, StockPrice, 
    Fundamentals, CompanyInfo, HistoricalData, EconomicIndicators,
    ProviderError, RateLimitError, AuthenticationError
)
from .jquants import JQuantsProvider
from .yahoo import YahooProvider
from .ibkr import IBKRProvider
from .boj import BOJProvider

logger = logging.getLogger(__name__)


class ProviderManager:
    """Manages multiple data providers with intelligent routing and fallback"""
    
    def __init__(self, redis_client=None):
        self.providers: Dict[ProviderName, BaseProvider] = {}
        self.redis_client = redis_client
        self._initialized = False
        
        # Provider preference by country
        self.country_preferences = {
            "JP": [ProviderName.JQUANTS, ProviderName.YAHOO, ProviderName.IBKR],
            "BR": [ProviderName.YAHOO, ProviderName.IBKR],
            "US": [ProviderName.IBKR, ProviderName.YAHOO],
            "DEFAULT": [ProviderName.YAHOO, ProviderName.IBKR]
        }
        
        # Provider preference by data type
        self.data_type_preferences = {
            DataType.COMPANY_INFO: {
                "JP": [ProviderName.JQUANTS, ProviderName.YAHOO],
                "DEFAULT": [ProviderName.YAHOO, ProviderName.IBKR]
            },
            DataType.OPTIONS: [ProviderName.IBKR, ProviderName.YAHOO],
            DataType.FUNDAMENTALS: {
                "JP": [ProviderName.JQUANTS, ProviderName.YAHOO, ProviderName.IBKR],
                "DEFAULT": [ProviderName.YAHOO, ProviderName.IBKR]
            }
        }
    
    async def initialize(self):
        """Initialize all configured providers"""
        if self._initialized:
            return
        
        # Initialize providers based on configuration
        try:
            self.providers[ProviderName.JQUANTS] = JQuantsProvider()
            logger.info("Initialized JQuants provider")
        except Exception as e:
            logger.warning(f"Failed to initialize JQuants provider: {e}")
        
        try:
            self.providers[ProviderName.YAHOO] = YahooProvider()
            logger.info("Initialized Yahoo provider")
        except Exception as e:
            logger.warning(f"Failed to initialize Yahoo provider: {e}")
        
        try:
            self.providers[ProviderName.IBKR] = IBKRProvider()
            logger.info("Initialized IBKR provider")
        except Exception as e:
            logger.warning(f"Failed to initialize IBKR provider: {e}")
            
        try:
            self.providers[ProviderName.BOJ] = BOJProvider()
            logger.info("Initialized BOJ provider")
        except Exception as e:
            logger.warning(f"Failed to initialize BOJ provider: {e}")
        
        self._initialized = True
    
    def _get_country_from_symbol(self, symbol: str) -> str:
        """Determine country from symbol format"""
        if symbol.endswith('.T'):
            return "JP"
        elif symbol.endswith('.SA'):
            return "BR"
        elif any(symbol.endswith(suffix) for suffix in ['.L', '.HK', '.TO']):
            return "OTHER"
        else:
            # Default to US for symbols without suffix
            return "US"
    
    def _get_provider_order(
        self, 
        symbol: str, 
        data_type: DataType,
        preferred_providers: Optional[List[ProviderName]] = None
    ) -> List[ProviderName]:
        """Determine optimal provider order for a request"""
        if preferred_providers:
            # User specified preference
            return preferred_providers
        
        country = self._get_country_from_symbol(symbol)
        
        # Check data type specific preferences
        if data_type in self.data_type_preferences:
            prefs = self.data_type_preferences[data_type]
            if isinstance(prefs, dict):
                return prefs.get(country, prefs.get("DEFAULT", []))
            else:
                return prefs
        
        # Fall back to country preferences
        return self.country_preferences.get(
            country, 
            self.country_preferences["DEFAULT"]
        )
    
    async def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """Get data from Redis cache"""
        if not self.redis_client:
            return None
        
        try:
            cached = await self.redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
        except Exception as e:
            logger.error(f"Cache get error: {e}")
        
        return None
    
    async def _set_cache(self, cache_key: str, data: Any, ttl: int = 300):
        """Set data in Redis cache"""
        if not self.redis_client or not data:
            return
        
        try:
            await self.redis_client.setex(
                cache_key, 
                ttl, 
                json.dumps(data, default=str)
            )
        except Exception as e:
            logger.error(f"Cache set error: {e}")
    
    async def get_price(
        self, 
        symbol: str,
        preferred_providers: Optional[List[ProviderName]] = None,
        use_cache: bool = True
    ) -> Optional[StockPrice]:
        """Get stock price with automatic provider selection and fallback"""
        await self.initialize()
        
        # Check cache first
        cache_key = f"price:{symbol}"
        if use_cache:
            cached = await self._get_from_cache(cache_key)
            if cached:
                # Reconstruct StockPrice object
                cached['timestamp'] = datetime.fromisoformat(cached['timestamp'])
                return StockPrice(**cached)
        
        # Try providers in order
        provider_order = self._get_provider_order(symbol, DataType.PRICE, preferred_providers)
        last_error = None
        
        for provider_name in provider_order:
            if provider_name not in self.providers:
                continue
            
            provider = self.providers[provider_name]
            if not provider.supports_data_type(DataType.PRICE):
                continue
            
            try:
                logger.info(f"Trying {provider_name.value} for price of {symbol}")
                result = await provider.get_price(symbol)
                
                if result:
                    # Cache successful result
                    if use_cache:
                        cache_data = result.__dict__.copy()
                        cache_data['timestamp'] = cache_data['timestamp'].isoformat()
                        await self._set_cache(cache_key, cache_data)
                    
                    return result
                    
            except RateLimitError as e:
                logger.warning(f"Rate limit hit for {provider_name.value}: {e}")
                last_error = e
                # Try next provider
                continue
            except Exception as e:
                logger.error(f"Error from {provider_name.value} for {symbol}: {e}")
                last_error = e
                continue
        
        # All providers failed
        if last_error:
            raise ProviderError("all", f"All providers failed for {symbol}", last_error)
        
        return None
    
    async def get_fundamentals(
        self, 
        symbol: str,
        preferred_providers: Optional[List[ProviderName]] = None,
        use_cache: bool = True
    ) -> Optional[Fundamentals]:
        """Get fundamental data with automatic provider selection"""
        await self.initialize()
        
        # Check cache first
        cache_key = f"fundamentals:{symbol}"
        if use_cache:
            cached = await self._get_from_cache(cache_key)
            if cached:
                cached['timestamp'] = datetime.fromisoformat(cached['timestamp'])
                return Fundamentals(**cached)
        
        # Try providers in order
        provider_order = self._get_provider_order(symbol, DataType.FUNDAMENTALS, preferred_providers)
        
        for provider_name in provider_order:
            if provider_name not in self.providers:
                continue
            
            provider = self.providers[provider_name]
            if not provider.supports_data_type(DataType.FUNDAMENTALS):
                continue
            
            try:
                logger.info(f"Trying {provider_name.value} for fundamentals of {symbol}")
                result = await provider.get_fundamentals(symbol)
                
                if result:
                    # Cache successful result
                    if use_cache:
                        cache_data = result.__dict__.copy()
                        cache_data['timestamp'] = cache_data['timestamp'].isoformat()
                        await self._set_cache(cache_key, cache_data, ttl=3600)  # 1 hour
                    
                    return result
                    
            except Exception as e:
                logger.error(f"Error from {provider_name.value} for {symbol} fundamentals: {e}")
                continue
        
        return None
    
    async def get_batch_prices(
        self,
        symbols: List[str],
        preferred_providers: Optional[List[ProviderName]] = None,
        use_cache: bool = True
    ) -> Dict[str, Optional[StockPrice]]:
        """Get prices for multiple symbols efficiently"""
        await self.initialize()
        
        results = {}
        uncached_symbols = []
        
        # Check cache for each symbol
        if use_cache:
            for symbol in symbols:
                cache_key = f"price:{symbol}"
                cached = await self._get_from_cache(cache_key)
                if cached:
                    cached['timestamp'] = datetime.fromisoformat(cached['timestamp'])
                    results[symbol] = StockPrice(**cached)
                else:
                    uncached_symbols.append(symbol)
        else:
            uncached_symbols = symbols
        
        if not uncached_symbols:
            return results
        
        # Group symbols by country for optimal provider selection
        symbols_by_country = {}
        for symbol in uncached_symbols:
            country = self._get_country_from_symbol(symbol)
            if country not in symbols_by_country:
                symbols_by_country[country] = []
            symbols_by_country[country].append(symbol)
        
        # Process each country group
        for country, country_symbols in symbols_by_country.items():
            # Get provider order for this country
            provider_order = self.country_preferences.get(
                country, 
                self.country_preferences["DEFAULT"]
            )
            
            if preferred_providers:
                provider_order = preferred_providers
            
            # Try providers that support batch
            batch_processed = False
            for provider_name in provider_order:
                if provider_name not in self.providers:
                    continue
                
                provider = self.providers[provider_name]
                if not provider.capabilities.supports_batch:
                    continue
                
                try:
                    logger.info(f"Batch fetching {len(country_symbols)} {country} symbols from {provider_name.value}")
                    batch_results = await provider.get_batch_prices(country_symbols)
                    
                    # Update results and cache
                    for symbol, price in batch_results.items():
                        if price:
                            results[symbol] = price
                            if use_cache:
                                cache_data = price.__dict__.copy()
                                cache_data['timestamp'] = cache_data['timestamp'].isoformat()
                                await self._set_cache(f"price:{symbol}", cache_data)
                    
                    batch_processed = True
                    break
                    
                except Exception as e:
                    logger.error(f"Batch error from {provider_name.value}: {e}")
                    continue
            
            # Fall back to individual requests if batch failed
            if not batch_processed:
                tasks = []
                for symbol in country_symbols:
                    if symbol not in results:
                        tasks.append(self.get_price(symbol, preferred_providers, use_cache))
                
                if tasks:
                    individual_results = await asyncio.gather(*tasks, return_exceptions=True)
                    for symbol, result in zip(country_symbols, individual_results):
                        if isinstance(result, Exception):
                            logger.error(f"Failed to get price for {symbol}: {result}")
                            results[symbol] = None
                        else:
                            results[symbol] = result
        
        return results
    
    async def get_historical(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d",
        preferred_providers: Optional[List[ProviderName]] = None,
        use_cache: bool = True
    ) -> Optional[List[HistoricalData]]:
        """Get historical data with provider fallback"""
        await self.initialize()
        
        # Build cache key
        cache_key = f"historical:{symbol}:{start_date.date()}:{end_date.date()}:{interval}"
        if use_cache:
            cached = await self._get_from_cache(cache_key)
            if cached:
                # Reconstruct HistoricalData objects
                historical_data = []
                for item in cached:
                    item['date'] = datetime.fromisoformat(item['date'])
                    historical_data.append(HistoricalData(**item))
                return historical_data
        
        # Try providers in order
        provider_order = self._get_provider_order(symbol, DataType.HISTORICAL, preferred_providers)
        
        for provider_name in provider_order:
            if provider_name not in self.providers:
                continue
            
            provider = self.providers[provider_name]
            if not provider.supports_data_type(DataType.HISTORICAL):
                continue
            
            try:
                logger.info(f"Trying {provider_name.value} for historical data of {symbol}")
                result = await provider.get_historical(symbol, start_date, end_date, interval)
                
                if result:
                    # Cache successful result
                    if use_cache:
                        cache_data = []
                        for item in result:
                            item_dict = item.__dict__.copy()
                            item_dict['date'] = item_dict['date'].isoformat()
                            cache_data.append(item_dict)
                        await self._set_cache(cache_key, cache_data, ttl=3600)  # 1 hour
                    
                    return result
                    
            except Exception as e:
                logger.error(f"Error from {provider_name.value} for {symbol} historical: {e}")
                continue
        
        return None
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all providers"""
        await self.initialize()
        
        health_results = {}
        
        # Check each provider in parallel
        tasks = []
        provider_names = []
        
        for name, provider in self.providers.items():
            tasks.append(provider.health_check())
            provider_names.append(name.value)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for name, result in zip(provider_names, results):
                if isinstance(result, Exception):
                    health_results[name] = {
                        "healthy": False,
                        "error": str(result)
                    }
                else:
                    health_results[name] = result
        
        # Overall health
        any_healthy = any(
            r.get("healthy", False) 
            for r in health_results.values()
        )
        
        return {
            "overall_healthy": any_healthy,
            "providers": health_results,
            "initialized": self._initialized,
            "available_providers": list(self.providers.keys())
        }
    
    async def get_economic_indicators(
        self,
        country: str,
        preferred_providers: Optional[List[ProviderName]] = None
    ) -> Optional[EconomicIndicators]:
        """Get economic indicators for a country"""
        await self.initialize()
        
        country = country.upper()
        
        # For economic indicators, prioritize specialized providers
        if country == "JP":
            provider_order = [ProviderName.BOJ, ProviderName.YAHOO]
        else:
            provider_order = [ProviderName.YAHOO]
            
        if preferred_providers:
            provider_order = preferred_providers
        
        # Try providers in order
        for provider_name in provider_order:
            if provider_name not in self.providers:
                continue
                
            provider = self.providers[provider_name]
            
            # Check if provider supports economic indicators
            if not provider.supports_data_type(DataType.ECONOMIC_INDICATORS):
                continue
                
            try:
                logger.info(f"Fetching economic indicators for {country} from {provider_name.value}")
                indicators = await provider.get_economic_indicators(country)
                
                if indicators:
                    logger.info(f"✅ Retrieved economic indicators for {country} from {provider_name.value}")
                    return indicators
                    
            except Exception as e:
                logger.warning(f"Provider {provider_name.value} failed for economic indicators: {e}")
                continue
        
        logger.warning(f"No provider could fetch economic indicators for {country}")
        return None
    
    async def close(self):
        """Close all provider connections"""
        for provider in self.providers.values():
            if hasattr(provider, '__aexit__'):
                await provider.__aexit__(None, None, None)