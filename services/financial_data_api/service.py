#!/usr/bin/env python3
"""
ABOUTME: Unified Financial Data API - Single service for all financial data
ABOUTME: Consolidates JQuants, Yahoo Finance, and IBKR data sources
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Query, Body, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import redis.asyncio as redis
import uvicorn

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from providers.manager import ProviderManager
from providers.base import ProviderName, DataType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6381))
PORT = int(os.getenv('PORT', 8090))

# Global instances
provider_manager: Optional[ProviderManager] = None
redis_client: Optional[redis.Redis] = None


# Request/Response Models
class PriceRequest(BaseModel):
    symbols: List[str] = Field(..., description="List of stock symbols")
    providers: Optional[List[str]] = Field(None, description="Preferred providers")
    use_cache: bool = Field(True, description="Use cached data if available")


class HistoricalRequest(BaseModel):
    symbol: str = Field(..., description="Stock symbol")
    start_date: str = Field(..., description="Start date (YYYY-MM-DD)")
    end_date: str = Field(..., description="End date (YYYY-MM-DD)")
    interval: str = Field("1d", description="Data interval (1m, 5m, 1h, 1d, 1w, 1M)")
    providers: Optional[List[str]] = Field(None, description="Preferred providers")
    use_cache: bool = Field(True, description="Use cached data if available")


class BatchDataRequest(BaseModel):
    symbols: List[str] = Field(..., description="List of stock symbols")
    data_types: List[str] = Field(..., description="Types of data to fetch")
    providers: Optional[List[str]] = Field(None, description="Preferred providers")
    use_cache: bool = Field(True, description="Use cached data if available")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle"""
    global provider_manager, redis_client
    
    # Initialize Redis
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        decode_responses=True
    )
    
    # Initialize provider manager
    provider_manager = ProviderManager(redis_client)
    await provider_manager.initialize()
    
    logger.info("Financial Data API initialized")
    
    yield
    
    # Cleanup
    if provider_manager:
        await provider_manager.close()
    if redis_client:
        await redis_client.close()


app = FastAPI(
    title="Unified Financial Data API",
    description="Single API for all financial data from JQuants, Yahoo Finance, and IBKR",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def parse_providers(providers: Optional[List[str]]) -> Optional[List[ProviderName]]:
    """Parse provider names from strings"""
    if not providers:
        return None
    
    parsed = []
    for p in providers:
        try:
            parsed.append(ProviderName(p.lower()))
        except ValueError:
            logger.warning(f"Unknown provider: {p}")
    
    return parsed if parsed else None


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    if not provider_manager:
        return {"status": "unhealthy", "error": "Service not initialized"}
    
    health = await provider_manager.health_check()
    
    return {
        "status": "healthy" if health["overall_healthy"] else "degraded",
        "providers": health["providers"],
        "redis_connected": await redis_client.ping() if redis_client else False
    }


@app.get("/api/financial-data/stock/{symbol}")
async def get_stock_data(
    symbol: str,
    providers: Optional[str] = Query(None, description="Comma-separated provider names"),
    use_cache: bool = Query(True, description="Use cached data")
):
    """Get current stock data with automatic provider selection"""
    if not provider_manager:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        # Parse providers
        provider_list = None
        if providers:
            provider_names = providers.split(",")
            provider_list = parse_providers(provider_names)
        
        # Get price data
        price = await provider_manager.get_price(symbol, provider_list, use_cache)
        
        if not price:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
        
        # Also try to get fundamentals
        fundamentals = await provider_manager.get_fundamentals(symbol, provider_list, use_cache)
        
        return {
            "symbol": symbol,
            "price": price.__dict__,
            "fundamentals": fundamentals.__dict__ if fundamentals else None,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting stock data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/financial-data/batch")
async def get_batch_data(request: BatchDataRequest):
    """Get multiple types of data for multiple symbols"""
    if not provider_manager:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        provider_list = parse_providers(request.providers)
        results = {}
        
        # Process each data type
        for data_type in request.data_types:
            if data_type == "price":
                prices = await provider_manager.get_batch_prices(
                    request.symbols,
                    provider_list,
                    request.use_cache
                )
                results["prices"] = {
                    symbol: price.__dict__ if price else None
                    for symbol, price in prices.items()
                }
            
            elif data_type == "fundamentals":
                # Get fundamentals for each symbol (could be optimized)
                fundamentals = {}
                tasks = []
                for symbol in request.symbols:
                    tasks.append(provider_manager.get_fundamentals(
                        symbol, provider_list, request.use_cache
                    ))
                
                fund_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for symbol, result in zip(request.symbols, fund_results):
                    if isinstance(result, Exception):
                        fundamentals[symbol] = None
                    else:
                        fundamentals[symbol] = result.__dict__ if result else None
                
                results["fundamentals"] = fundamentals
        
        return {
            "data": results,
            "symbols": request.symbols,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in batch request: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/financial-data/historical")
async def get_historical_data(request: HistoricalRequest):
    """Get historical price data"""
    if not provider_manager:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        # Parse dates
        start_date = datetime.strptime(request.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(request.end_date, "%Y-%m-%d")
        
        # Parse providers
        provider_list = parse_providers(request.providers)
        
        # Get historical data
        historical = await provider_manager.get_historical(
            request.symbol,
            start_date,
            end_date,
            request.interval,
            provider_list,
            request.use_cache
        )
        
        if not historical:
            raise HTTPException(
                status_code=404, 
                detail=f"No historical data found for {request.symbol}"
            )
        
        return {
            "symbol": request.symbol,
            "start_date": request.start_date,
            "end_date": request.end_date,
            "interval": request.interval,
            "data": [h.__dict__ for h in historical],
            "count": len(historical),
            "timestamp": datetime.now().isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {e}")
    except Exception as e:
        logger.error(f"Error getting historical data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/financial-data/providers")
async def get_providers():
    """Get information about available providers and their capabilities"""
    if not provider_manager:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    health = await provider_manager.health_check()
    
    providers_info = {}
    for provider_name, health_info in health["providers"].items():
        providers_info[provider_name] = {
            "healthy": health_info.get("healthy", False),
            "capabilities": health_info.get("capabilities", {}),
            "error": health_info.get("error")
        }
    
    return {
        "providers": providers_info,
        "data_types": [dt.value for dt in DataType],
        "supported_intervals": ["1m", "5m", "15m", "30m", "1h", "1d", "1w", "1M"]
    }


@app.get("/api/financial-data/economic-indicators/{country}")
async def get_economic_indicators(
    country: str,
    providers: Optional[str] = Query(None, description="Comma-separated provider names")
):
    """Get economic indicators for a country (e.g., 'JP' for Japan)"""
    if not provider_manager:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    # Parse provider preferences
    provider_prefs = None
    if providers:
        provider_prefs = parse_providers(providers.split(','))
    
    try:
        indicators = await provider_manager.get_economic_indicators(
            country=country.upper(),
            preferred_providers=provider_prefs
        )
        
        if not indicators:
            raise HTTPException(
                status_code=404, 
                detail=f"Economic indicators not available for country: {country}"
            )
        
        # Convert to dict for JSON response
        result = {
            "country": indicators.country,
            "gdp_growth": indicators.gdp_growth,
            "inflation_rate": indicators.inflation_rate,
            "unemployment_rate": indicators.unemployment_rate,
            "policy_rate": indicators.policy_rate,
            "bond_yield_10y": indicators.bond_yield_10y,
            "currency_rate": indicators.currency_rate,
            "stock_index": indicators.stock_index,
            "stock_index_name": indicators.stock_index_name,
            "industrial_production": indicators.industrial_production,
            "trade_balance": indicators.trade_balance,
            "consumer_confidence": indicators.consumer_confidence,
            "business_confidence": indicators.business_confidence,
            "timestamp": indicators.timestamp.isoformat() if indicators.timestamp else None,
            "provider": indicators.provider,
            "source_urls": indicators.source_urls or []
        }
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting economic indicators for {country}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/financial-data/search")
async def search_symbols(
    query: str,
    country: Optional[str] = Query(None, description="Country code (JP, US, BR, etc)"),
    limit: int = Query(10, description="Maximum results")
):
    """Search for symbols (simplified for now)"""
    # This would normally search a database or use provider search APIs
    # For now, return a simple response
    return {
        "query": query,
        "results": [],
        "message": "Symbol search not yet implemented in unified API"
    }


if __name__ == "__main__":
    uvicorn.run(
        "service:app",
        host="0.0.0.0",
        port=PORT,
        reload=True
    )