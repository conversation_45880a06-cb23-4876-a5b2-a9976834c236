# ABOUTME: SmolLM3-3B categorization model using smart tools server with MCP integration
# ABOUTME: Categorizes documents using vLLM-served SmolLM3-3B with real-time financial data

import json
import logging
import os
from typing import Dict

import httpx

from shared.models.documents import QueuedDocument

from .base import (
    BaseCategorizationModel,
    CategorizationError,
    CategorizationResult,
)

logger = logging.getLogger(__name__)


class SmolLM3CategorizationModel(BaseCategorizationModel):
    """
    SmolLM3-3B categorization model using smart tools server
    Integrates with Yahoo Finance MCP and IBKR MCP for real-time data
    """

    def __init__(self):
        super().__init__("HuggingFaceTB/SmolLM3-3B")
        # Use Docker service name when running in container
        default_url = (
            "http://smoll-vllm:8093/v1"
            if os.path.exists("/.dockerenv")
            else "http://localhost:8093/v1"
        )
        self.smart_tools_url = os.getenv("SMOLL_TOOLS_URL", default_url)
        self.temperature = float(os.getenv("SMOLL_TEMPERATURE", "0.6"))
        self.max_tokens = int(os.getenv("SMOLL_MAX_TOKENS", "1000"))  # Reduced for 8K context
        self.api_key = "fake-key"  # Not needed for local server

        # Initialize cached_prompt attribute (matches base class prompt_template)
        self.cached_prompt = None

        # HTTP client with longer timeout for tool-enhanced responses
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(60.0, connect=5.0),
            limits=httpx.Limits(max_keepalive_connections=5),
        )

        logger.info(f"✅ SmolLM3-3B model initialized with smart tools at {self.smart_tools_url}")

    def set_prompt(self, prompt_template):
        """Set the prompt template from database"""
        self.cached_prompt = prompt_template
        logger.info(
            f"✅ Database prompt loaded for SmolLM3 - version: {getattr(prompt_template, 'version', 'unknown')}"
        )

    async def health_check(self) -> bool:
        """Check if SmolLM3-3B smart tools server is available"""
        try:
            response = await self.client.get(f"{self.smart_tools_url.replace('/v1', '')}/health")
            if response.status_code == 200:
                data = response.json()
                return data.get("status") == "healthy" and data.get("tools_enabled", False)
            return False
        except Exception as e:
            logger.warning(f"SmolLM3-3B health check failed: {e}")
            return False

    async def categorize(self, document: QueuedDocument) -> dict:
        """
        Categorize document using SmolLM3-3B with smart tools
        Tools will automatically enhance prompts with real-time financial data
        """
        try:
            logger.info(f"🔄 Starting categorization for document {document.id}")

            # Debug: Log document attributes and values
            logger.info(f"Document ID: {document.id}")
            logger.info(f"Document type: {type(document)}")
            logger.info(
                f"Document attributes: title={hasattr(document, 'title')}, content={hasattr(document, 'content')}, source={hasattr(document, 'source')}"
            )

            if hasattr(document, "title"):
                logger.info(f"Document title: {document.title}")
            if hasattr(document, "content"):
                logger.info(
                    f"Document content length: {len(document.content) if document.content else 0}"
                )
            if hasattr(document, "source"):
                logger.info(f"Document source: {document.source}")
                logger.info(f"Source type: {type(document.source)}")
                if hasattr(document.source, "source_type"):
                    logger.info(f"Source source_type: {document.source.source_type}")

            # Prepare the categorization prompt - NO FALLBACKS
            try:
                if not self.cached_prompt:
                    logger.error("❌ No database prompt available - failing fast")
                    raise CategorizationError(
                        "No categorization prompt loaded from database - system not properly initialized"
                    )

                logger.info("Using production categorization prompt from database")
                logger.info(f"Prompt version: {getattr(self.cached_prompt, 'version', 'unknown')}")

                # Get prompt text from database
                prompt_text = getattr(self.cached_prompt, "prompt_text", None) or getattr(
                    self.cached_prompt, "content", None
                )

                if not prompt_text:
                    logger.error("❌ Database prompt has no content")
                    raise CategorizationError("Database prompt is empty - prompt system failure")

                # Format prompt with document data
                prompt_content = prompt_text.format(
                    title=document.title,
                    content=document.content[:1500],  # Further reduced to fit 8K token limit
                    source=document.source.source_type,
                )
                logger.info("✅ Production prompt prepared successfully")
            except Exception as prompt_error:
                logger.error(f"❌ Error preparing prompt: {prompt_error}")
                raise CategorizationError(f"Prompt preparation failed: {str(prompt_error)}")

            # Make API call to smart tools server
            try:
                logger.info(f"🌐 Making API call to {self.smart_tools_url}/chat/completions")
                response = await self.client.post(
                    f"{self.smart_tools_url}/chat/completions",
                    json={
                        "model": "SMOLL_WITH_TOOLS",
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are a financial document categorizer. Always respond with valid JSON only.",
                            },
                            {"role": "user", "content": prompt_content},
                        ],
                        "temperature": self.temperature,
                        "max_tokens": self.max_tokens,
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {self.api_key}",
                    },
                )
                logger.info(f"✅ API call completed with status: {response.status_code}")
            except Exception as api_error:
                logger.error(f"❌ API call error: {api_error}")
                raise CategorizationError(f"API call failed: {str(api_error)}")

            if response.status_code != 200:
                raise CategorizationError(
                    f"SmolLM3-3B API error: {response.status_code} - {response.text}"
                )

            # Extract response
            try:
                logger.info("📝 Parsing API response")
                result_data = response.json()
                logger.info(f"Response keys: {list(result_data.keys())}")

                content = result_data["choices"][0]["message"]["content"]
                logger.info(f"✅ Content extracted, length: {len(content)}")
            except Exception as extract_error:
                logger.error(f"❌ Response extraction error: {extract_error}")
                raise CategorizationError(f"Response extraction failed: {str(extract_error)}")

            # Parse JSON response - NO TRANSFORMATION, just parsing
            try:
                logger.info("🔍 Parsing JSON response")
                logger.info(f"Raw response content: {content[:1000]}...")  # Log first 1000 chars
                
                # Try direct JSON parsing first
                try:
                    result = json.loads(content.strip())
                except json.JSONDecodeError:
                    # Handle markdown-wrapped JSON
                    if "```json" in content:
                        json_start = content.find("```json") + 7
                        json_end = content.rfind("```")
                        if json_start > 6 and json_end > json_start:
                            json_str = content[json_start:json_end].strip()
                            result = json.loads(json_str)
                    else:
                        # Try to find JSON object
                        json_start = content.find("{")
                        json_end = content.rfind("}") + 1
                        if json_start >= 0 and json_end > json_start:
                            json_str = content[json_start:json_end]
                            result = json.loads(json_str)
                        else:
                            raise CategorizationError(f"Failed to parse JSON from response: {content[:200]}...")
                
                logger.info(f"✅ JSON parsed successfully, keys: {list(result.keys())}")
                
                # Add model metadata to the result
                result["model_used"] = "smoll"
                result["prompt_version"] = getattr(self.cached_prompt, 'version', '1.0.4')
                
                # Normalize the categorization response (handles nested categorization objects)
                normalized_result = self.normalize_categorization_response(result)
                
                # Return the normalized result wrapped in a categorization field
                return {"categorization": normalized_result}
                
            except Exception as parse_error:
                logger.error(f"❌ JSON parsing error: {parse_error}")
                logger.error(f"Full response content: {content}")
                raise CategorizationError(f"JSON parsing failed: {str(parse_error)}")

        except httpx.TimeoutException:
            logger.error("⏰ SmolLM3-3B request timed out")
            raise CategorizationError("SmolLM3-3B request timed out")
        except CategorizationError:
            # Re-raise categorization errors as-is
            raise
        except Exception as e:
            logger.error(f"❌ SmolLM3-3B categorization error: {e}")
            logger.error(f"Error type: {type(e)}")
            logger.error(f"Error args: {e.args}")
            raise CategorizationError(f"SmolLM3-3B categorization failed: {str(e)}")


    def deep_unnest_categorization(self, data: Dict) -> Dict:
        """
        Recursively unnest deeply nested categorization objects.
        
        SmolLM3 sometimes produces responses like:
        {
          "categorization": {
            "categorization": {
              "primary": "...",
              "confidence_score": 0.9
            }
          }
        }
        
        This function traverses nested structures to find the actual data.
        """
        # If this is not a categorization object, return as-is
        if not isinstance(data, dict) or "categorization" not in data:
            return data
            
        # Keep going deeper until we find actual categorization data
        current = data["categorization"]
        depth = 1
        
        while isinstance(current, dict) and "categorization" in current and depth < 10:
            current = current["categorization"]
            depth += 1
            
        if depth > 1:
            logger.warning(f"Found {depth} levels of nested categorization objects - unnesting")
            
        # If we found nested categorization data, extract it
        if isinstance(current, dict):
            # Extract the categorization data and preserve top-level fields
            result = {}
            
            # Add non-categorization fields from the top level
            for key, value in data.items():
                if key != "categorization":
                    result[key] = value
                    
            # Add the unnested categorization data
            result.update(current)
            
            logger.info(f"Successfully unnested categorization data from depth {depth}")
            return result
            
        logger.warning("Could not find valid categorization data in nested structure")
        return data

    def normalize_categorization_response(self, result: Dict) -> Dict:
        """
        Normalize the categorization response to ensure proper structure and required fields.
        """
        # First, deep unnest any nested categorization objects
        unnested_result = self.deep_unnest_categorization(result)
        
        # Now normalize the unnested data to expected flat format
        normalized_result = {}
        
        # Extract categorization fields with various possible names
        primary_category = (
            unnested_result.get("primary") or 
            unnested_result.get("primary_category") or 
            "other"
        )
        secondary_category = (
            unnested_result.get("secondary") or 
            unnested_result.get("secondary_category") or 
            "general"
        )
        confidence = (
            unnested_result.get("confidence_score") or 
            unnested_result.get("confidence") or 
            0.5
        )
        
        # Remove $ prefixes if present (SmolLM3 sometimes adds these)
        if isinstance(primary_category, str) and primary_category.startswith("$"):
            primary_category = primary_category[1:]
        if isinstance(secondary_category, str) and secondary_category.startswith("$"):
            secondary_category = secondary_category[1:]
            
        normalized_result = {
            "primary_category": primary_category,
            "secondary_category": secondary_category,
            "confidence": confidence,
        }
        
        # Preserve other non-categorization fields from original result
        for key, value in result.items():
            if key != "categorization" and key not in normalized_result:
                normalized_result[key] = value
                
        # Also preserve any additional fields from the unnested data
        for key, value in unnested_result.items():
            if key not in ["primary", "secondary", "confidence_score", "confidence"] and key not in normalized_result:
                normalized_result[key] = value
        
        # Ensure confidence meets minimum validation threshold
        final_confidence = normalized_result["confidence"]
        if isinstance(final_confidence, (int, float)):
            # Convert from percentage to decimal if needed
            if final_confidence > 1.0:
                normalized_result["confidence"] = final_confidence / 100.0
            # Ensure minimum threshold
            if normalized_result["confidence"] < 0.3:
                logger.warning(f"Confidence {normalized_result['confidence']} below minimum threshold 0.3, setting to 0.3")
                normalized_result["confidence"] = 0.3
                
        # Add required validation fields
        normalized_result["model_used"] = normalized_result.get("model_used", "smoll")
        normalized_result["language"] = normalized_result.get("language", "ja")
        
        logger.info(f"Normalized categorization result: {normalized_result}")
        return normalized_result

    async def cleanup(self):
        """Cleanup HTTP client"""
        await self.client.aclose()

    def __del__(self):
        """Ensure client is closed"""
        try:
            import asyncio

            if hasattr(self, "client"):
                asyncio.create_task(self.client.aclose())
        except:
            pass
