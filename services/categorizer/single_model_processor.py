# ABOUTME: SmolLM3-3B only document categorization processor
# ABOUTME: Simplified processor with no fallbacks, mock data, or external APIs

import asyncio
import logging
import os
from datetime import datetime
from typing import List, Optional

from shared.models.documents import Preprocessing<PERSON>esult, QueuedDocument, CompanyInfo
from shared.utils.ai_metrics_tracker import create_ai_tracker
from shared.utils.monitoring import get_logfire
from shared.utils.prompt_client import PromptClient

from .config import FlashPreprocessorConfig
from .models import SmolLM3CategorizationModel

logger = logging.getLogger(__name__)


class SmolLM3Processor:
    """
    SmolLM3-3B only document categorization processor
    No fallbacks, no mock data, no external APIs - fail fast if SmolLM3 unavailable
    """

    def __init__(self, config: Optional[FlashPreprocessorConfig] = None):
        self.config = config or FlashPreprocessorConfig.from_env()
        self.logfire = get_logfire()

        # Single model only
        self.model: Optional[SmolLM3CategorizationModel] = None

        # Initialize SmolLM3 model - fail if not available
        self._initialize_model()

        # Model statistics (simplified)
        self.model_stats = {"attempts": 0, "successes": 0, "failures": 0}

        # Initialize prompt client
        self.prompt_client = None
        self.cached_prompt = None

        # Initialize AI metrics tracker
        self.ai_tracker = create_ai_tracker(
            service_name="categorizer",
            postgres_host=os.getenv("TIMESCALE_HOST", "timescaledb"),
            postgres_port=int(os.getenv("TIMESCALE_PORT", "5432")),
            postgres_db=os.getenv("TIMESCALE_DB", "omotesamba"),
        )

    def _initialize_model(self):
        """Initialize SmolLM3-3B model - fail fast if unavailable"""
        try:
            self.model = SmolLM3CategorizationModel()
            logger.info("✅ SmolLM3-3B model initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize SmolLM3-3B model: {e}")
            raise RuntimeError(f"SmolLM3-3B model initialization failed: {e}")

    async def initialize(self):
        """Initialize prompt client and load categorization prompt"""
        self.prompt_client = PromptClient()
        await self.prompt_client.initialize()
        await self.reload_prompt()

        # Set prompt for model
        if self.model and hasattr(self.model, "set_prompt"):
            self.model.set_prompt(self.cached_prompt)

    async def reload_prompt(self):
        """Reload categorization prompt from database"""
        if not self.prompt_client:
            self.prompt_client = PromptClient()
            await self.prompt_client.initialize()

        prompt_template = await self.prompt_client.get_prompt("categorization")
        if prompt_template:
            self.cached_prompt = prompt_template
            logger.info(f"Loaded categorization prompt version {prompt_template.version}")

            # Update model with new prompt
            if self.model and hasattr(self.model, "set_prompt"):
                self.model.set_prompt(self.cached_prompt)
            return True
        else:
            logger.error("Failed to load categorization prompt from database")
            raise RuntimeError("No categorization prompt available")

    async def preprocess(self, document: QueuedDocument) -> PreprocessingResult:
        """
        Categorize document using SmolLM3-3B only
        No fallbacks - fail fast if model unavailable
        """
        logger.info(f"🚀 PREPROCESS STARTED for document {getattr(document, 'id', 'UNKNOWN')}")
        logger.info(f"🔍 Document type: {type(document)}")
        logger.info(
            f"🔍 Document attributes: {dir(document) if hasattr(document, '__dict__') else 'no __dict__'}"
        )
        if not self.model:
            logger.error("❌ SmolLM3-3B model not available")
            raise RuntimeError("SmolLM3-3B model not available")

        logger.info(f"🔍 Model available, checking health for document {document.id}")

        # Health check model before processing
        try:
            health_ok = await self.model.health_check()
            if not health_ok:
                logger.error(f"❌ SmolLM3-3B model failed health check for document {document.id}")
                raise RuntimeError("SmolLM3-3B model failed health check")
            logger.info(f"✅ SmolLM3-3B model health check passed for document {document.id}")
        except Exception as health_error:
            logger.error(f"❌ Health check exception for document {document.id}: {health_error}")
            raise RuntimeError(f"SmolLM3-3B model health check failed: {health_error}")

        try:
            # Track attempt
            self.model_stats["attempts"] += 1

            # Categorize document
            logger.info(f"🔄 Starting categorization for document {document.id}")
            start_time = asyncio.get_event_loop().time()

            try:
                response = await self.model.categorize(document)
                logger.info(f"✅ Got categorization response for document {document.id}")
            except Exception as cat_error:
                logger.error(f"❌ Categorization error for document {document.id}: {cat_error}")
                logger.error(f"Error type: {type(cat_error)}")
                raise

            # Response is now a dict with categorization field
            # Extract the necessary fields to create PreprocessingResult
            try:
                cat_data = response.get("categorization", {})
                # cat_data IS the categorization section - don't look for nested categorization
                cat_section = cat_data  # Direct access to the categorization fields
                doc_details = cat_data.get("document_details", {})
                entities = cat_data.get("entities", {})
                
                # Create PreprocessingResult from the raw response
                logger.info(f"🔍 Setting categorization_result to: {response}")
                
                # Debug field mapping
                primary_cat = cat_section.get("primary_category") or cat_section.get("primary")
                secondary_cat = cat_section.get("secondary_category") or cat_section.get("secondary")
                confidence_val = cat_section.get("confidence", 0.0) or cat_section.get("confidence_score", 0.0)
                
                logger.info(f"🔍 Extracted values: primary='{primary_cat}', secondary='{secondary_cat}', confidence={confidence_val}")
                logger.info(f"🔍 cat_section keys: {list(cat_section.keys())}")
                
                result = PreprocessingResult(
                    document_id=document.id,
                    title_english=doc_details.get("title_english", document.title),
                    category=primary_cat,
                    subcategory=secondary_cat,
                    confidence_score=confidence_val,
                    language="ja",  # Default for Japanese documents
                    company_verified=True,
                    company_verification=document.company or CompanyInfo(code="0000", name="Unknown"),
                    has_required_data=True,
                    quality_assessment=cat_section.get("confidence", 0.0) or cat_section.get("confidence_score", 0.0),
                    summary=doc_details.get("justification", ""),
                    key_entities=[entities.get("primary_company", {}).get("english_name", "Unknown")],
                    categorization_result=response  # Store the full raw response with "categorization" field
                )
                logger.info(f"🔍 Result has categorization_result: {hasattr(result, 'categorization_result')}")
                logger.info(f"🔍 Result.categorization_result value: {getattr(result, 'categorization_result', None)}")
                logger.info(f"✅ Created preprocessing result for document {document.id}")
            except Exception as convert_error:
                logger.error(f"❌ Error creating preprocessing result for document {document.id}: {convert_error}")
                logger.error(f"Response was: {response}")
                raise

            # Calculate latency
            latency_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)

            # Extract token usage from response dict
            input_tokens = 0
            output_tokens = 0
            total_tokens = 0

            token_usage = response.get("categorization", {}).get("token_usage", {})
            if token_usage:
                input_tokens = token_usage.get("input_tokens", 0)
                output_tokens = token_usage.get("output_tokens", 0)
                total_tokens = token_usage.get("total_tokens", 0)

            # Fallback token estimation if not provided
            if total_tokens == 0:
                input_tokens = len(document.content) // 4 + 200
                output_tokens = 50
                total_tokens = input_tokens + output_tokens

            # Record to AI metrics tracker
            if self.ai_tracker:
                try:
                    self.ai_tracker.log_call_simple(
                        model_name="smolllm3-3b",
                        operation_type="categorization",
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        latency_ms=latency_ms,
                        success=True,
                        document_id=document.id,
                        queue_name=self.config.input_queue,
                        response_quality_score=result.quality_assessment,
                        metadata={
                            "category": result.category,
                            "subcategory": result.subcategory,
                            "confidence": result.confidence_score,
                            "language": result.language,
                            "has_token_usage": bool(token_usage),
                        },
                    )
                except Exception as e:
                    logger.warning(f"Failed to log AI metrics: {e}")

            # Track success
            self.model_stats["successes"] += 1

            return result

        except Exception as e:
            # Track failure
            self.model_stats["failures"] += 1

            # Record failure to AI metrics
            if self.ai_tracker:
                try:
                    self.ai_tracker.log_call_simple(
                        model_name="smolllm3-3b",
                        operation_type="categorization",
                        input_tokens=0,
                        output_tokens=0,
                        latency_ms=0,
                        success=False,
                        document_id=document.id,
                        queue_name=self.config.input_queue,
                        response_quality_score=0.0,
                        metadata={"error": str(e)},
                    )
                except Exception as metrics_error:
                    logger.warning(f"Failed to log AI metrics for failure: {metrics_error}")

            logger.error(f"SmolLM3-3B categorization failed for document {document.id}: {e}")
            raise RuntimeError(f"SmolLM3-3B categorization failed: {e}")

    async def batch_preprocess(self, documents: List[QueuedDocument]) -> List[PreprocessingResult]:
        """
        Process batch of documents using SmolLM3-3B
        Process one at a time (batch_size=1)
        """
        if not documents:
            return []

        results = []
        for document in documents:
            try:
                result = await self.preprocess(document)
                results.append(result)
            except Exception as e:
                # Create failure result for DLQ routing
                failure_result = self._create_failure_result(document, str(e))
                results.append(failure_result)

        return results

    def _create_failure_result(
        self, document: QueuedDocument, error_message: str
    ) -> PreprocessingResult:
        """Create a failure result for DLQ routing"""
        from shared.models.documents import CompanyInfo

        logger.error(f"CATEGORIZATION FAILED for document {document.id}: {error_message}")

        return PreprocessingResult(
            document_id=document.id,
            title_english=f"[FAILED] {document.source.title}",
            category=None,  # None indicates failure
            company_verification=document.company
            or CompanyInfo(code="0000", name="Unknown Company"),
            company_verified=False,
            confidence_score=0.0,
            language=getattr(document, "language", None) or "unknown",
            has_required_data=False,
            quality_assessment=0.0,
            batch_recommendation="dlq",
            error=error_message,
            failed_at=datetime.now().isoformat(),
        )

    def get_model_statistics(self) -> dict:
        """Get SmolLM3-3B model performance statistics"""
        if self.model_stats["attempts"] > 0:
            success_rate = self.model_stats["successes"] / self.model_stats["attempts"]
        else:
            success_rate = 0.0

        return {
            "model": "smolllm3-3b",
            "attempts": self.model_stats["attempts"],
            "successes": self.model_stats["successes"],
            "failures": self.model_stats["failures"],
            "success_rate": success_rate,
            "status": "healthy" if self.model else "unavailable",
        }

    async def health_check(self) -> bool:
        """Check if SmolLM3-3B model is healthy"""
        if not self.model:
            return False
        return await self.model.health_check()
