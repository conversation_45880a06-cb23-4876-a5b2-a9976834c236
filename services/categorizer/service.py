"""
Categorizer FastAPI Service
Document categorization and routing service
"""

import asyncio
import json
import logging
import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Optional

from fastapi import BackgroundTasks, FastAPI, HTTPException, Response
from prometheus_client import CONTENT_TYPE_LATEST, Counter, Gauge, Histogram, generate_latest
from pydantic import BaseModel, Field

from shared.database.connection import get_redis_client
from shared.models.documents import PreprocessingR<PERSON>ult, QueuedDocument
from shared.utils.logging import setup_logging
from shared.utils.monitoring import get_logfire
from shared.utils.rate_limit_config import RateLimitConfig
from shared.utils.simple_reactive_controller import create_reactive_controller

from .config import FlashPreprocessorConfig
from .config_manager import ConfigManager
from .single_model_processor import SmolLM3Processor

# Setup
setup_logging()
logfire = get_logfire()
logger = logging.getLogger(__name__)

# Prometheus metrics - use try/except to avoid duplicate registration
try:
    documents_processed = Counter(
        "categorizer_documents_processed_total",
        "Total number of documents processed",
        ["category", "status"],
    )
except ValueError:
    # Metric already registered, get it from registry
    from prometheus_client import REGISTRY

    documents_processed = REGISTRY._names_to_collectors["categorizer_documents_processed_total"]

try:
    processing_time = Histogram(
        "categorizer_processing_duration_seconds",
        "Document processing time in seconds",
        ["operation"],
    )
except ValueError:
    from prometheus_client import REGISTRY

    processing_time = REGISTRY._names_to_collectors["categorizer_processing_duration_seconds"]

try:
    queue_depth = Gauge("categorizer_queue_depth", "Current queue depth", ["queue_name"])
except ValueError:
    from prometheus_client import REGISTRY

    queue_depth = REGISTRY._names_to_collectors["categorizer_queue_depth"]

try:
    model_requests = Counter(
        "categorizer_model_requests_total",
        "Total AI model requests",
        ["provider", "model", "status"],
    )
except ValueError:
    from prometheus_client import REGISTRY

    model_requests = REGISTRY._names_to_collectors["categorizer_model_requests_total"]

try:
    rate_limit_remaining = Gauge(
        "categorizer_rate_limit_remaining",
        "Remaining rate limit for AI models",
        ["provider", "model", "limit_type"],
    )
except ValueError:
    from prometheus_client import REGISTRY

    rate_limit_remaining = REGISTRY._names_to_collectors["categorizer_rate_limit_remaining"]

try:
    service_info = Gauge(
        "categorizer_service_info",
        "Service information",
        ["version", "model", "queue_enabled"],
    )
except ValueError:
    from prometheus_client import REGISTRY

    service_info = REGISTRY._names_to_collectors["categorizer_service_info"]


class PreprocessRequest(BaseModel):
    """Request to preprocess a document"""

    document_id: str
    priority: int = 5


class PreprocessResponse(BaseModel):
    """Preprocessing result"""

    document_id: str
    category: str
    quality_score: float
    processing_priority: int
    immediate_processing: bool
    queue_name: str


class RateLimitUpdate(BaseModel):
    """Rate limit update request"""

    rpm: int  # Requests per minute
    rpd: int  # Requests per day
    tpm: int  # Tokens per minute
    enabled: bool = True


class BatchConfigUpdate(BaseModel):
    """Batch processing configuration update"""

    batch_size: Optional[int] = Field(
        None, ge=1, le=50, description="Number of documents per batch"
    )
    batch_timeout: Optional[float] = Field(None, ge=0.1, le=60.0, description="Timeout in seconds")


class BatchConfigResponse(BaseModel):
    """Batch processing configuration response"""

    batch_size: int
    max_batch_size: int
    min_batch_size: int
    batch_timeout: float
    enabled: bool


# Global instances
processor: SmolLM3Processor | None = None
config: FlashPreprocessorConfig | None = None
config_manager: ConfigManager | None = None
rate_limit_config: RateLimitConfig | None = None
service_controller = None

# Configuration logging task
config_logging_task: asyncio.Task | None = None


async def log_configuration_periodically():
    """Log the current configuration periodically for monitoring"""
    try:
        while True:
            try:
                # Get current configuration
                current_config = await config_manager.get_config() if config_manager else None
                emergency_status = (
                    await config_manager.get_emergency_status() if config_manager else {}
                )

                if current_config:
                    # Build simplified configuration
                    config_info = {
                        "service": "categorizer",
                        "model": current_config.primary_model,
                        "provider": current_config.primary_provider,
                        "queue_consumer_enabled": service_controller.is_processing_enabled()
                        if service_controller
                        else False,
                        "batch_processing": {
                            "enabled": current_config.enable_batch_processing,
                            "size": current_config.batch_size,
                        },
                        "emergency_stop": emergency_status.get("emergency_stop", False),
                        "queues": {
                            "input": config.input_queue,
                            "output": config.output_queue,  # Single queue architecture
                            "architecture": "single_queue_with_category_metadata",
                        },
                    }

                    # Log the simplified configuration
                    logger.info(f"📊 Flash Preprocessor Config: {json.dumps(config_info, indent=2)}")

            except Exception as e:
                logger.error(f"Error logging configuration: {e}")

            # Wait 5 minutes before next log
            await asyncio.sleep(300)
    except asyncio.CancelledError:
        logger.info("Configuration logging task cancelled")
    except Exception as e:
        logger.error(f"Configuration logging task error: {e}")


async def validate_startup_configuration(config: FlashPreprocessorConfig) -> None:
    """Validate configuration on startup and fail fast if issues found"""
    validation_errors = []

    # Validate Redis URL
    try:
        import redis.asyncio as redis

        test_client = await redis.from_url(config.redis_url, decode_responses=True)
        await test_client.ping()
        await test_client.close()
        logger.info("✅ Redis connection validated")
    except Exception as e:
        validation_errors.append(f"Redis connection failed: {e}")

    # Validate queue names
    required_queues = [config.input_queue, config.dlq]
    for queue_name in required_queues:
        if not queue_name or not isinstance(queue_name, str) or len(queue_name.strip()) == 0:
            validation_errors.append(f"Invalid queue name: '{queue_name}'")

    # Validate output queue (simplified single-queue architecture)
    if not config.output_queue or not isinstance(config.output_queue, str):
        validation_errors.append("Output queue is missing or invalid")

    # Validate batch configuration
    if config.batch_size < config.min_batch_size or config.batch_size > config.max_batch_size:
        validation_errors.append(
            f"batch_size ({config.batch_size}) must be between {config.min_batch_size} and {config.max_batch_size}"
        )

    if config.batch_timeout <= 0:
        validation_errors.append(f"batch_timeout ({config.batch_timeout}) must be positive")

    # Check environment variables
    required_env_vars = ["GEMINI_API_KEY", "DEEPSEEK_API_KEY", "BAIDU_API_KEY"]
    missing_env_vars = []
    for env_var in required_env_vars:
        if not os.getenv(env_var):
            missing_env_vars.append(env_var)

    if missing_env_vars:
        validation_errors.append(f"Missing required environment variables: {missing_env_vars}")

    # If any validation errors, fail fast
    if validation_errors:
        error_msg = "Configuration validation failed:\n" + "\n".join(
            f"- {error}" for error in validation_errors
        )
        logger.error(error_msg)
        raise RuntimeError(error_msg)

    logger.info("✅ Configuration validation passed")


@asynccontextmanager
async def lifespan(app: FastAPI):  # noqa: ARG001
    """Initialize and cleanup resources"""
    global processor, config, rate_limit_config, service_controller, config_manager, config_logging_task
    config = FlashPreprocessorConfig.from_env()

    # Validate configuration before proceeding
    await validate_startup_configuration(config)

    # Initialize Redis for rate limiting
    import redis.asyncio as redis

    redis_client = await redis.from_url(config.redis_url, decode_responses=True)

    processor = SmolLM3Processor(config)
    await processor.initialize()  # Initialize prompt client and load prompts

    rate_limit_config = RateLimitConfig(redis_client, "categorizer")

    # Initialize dynamic configuration manager
    global config_manager
    config_manager = ConfigManager()
    await config_manager.initialize()

    # Initialize reactive service controller for dynamic queue management
    service_controller = create_reactive_controller("categorizer")

    # Define queue runner function
    async def queue_runner():
        """Queue processing function that can be started/stopped dynamically"""
        logger.info("🟢 CATEGORIZER: Queue runner function started")
        print("🟢 CATEGORIZER: Queue runner function started")
        try:
            from .queue_based_categorizer import QueueBasedCategorizer

            categorizer = QueueBasedCategorizer()
            await categorizer.initialize()
            await categorizer.run()
        except asyncio.CancelledError:
            logger.info("🔴 CATEGORIZER: Queue runner function stopped")
            print("🔴 CATEGORIZER: Queue runner function stopped")
            raise
        except Exception as e:
            logger.error(f"❌ CATEGORIZER: Queue runner function error: {e}")
            print(f"❌ CATEGORIZER: Queue runner function error: {e}")
            raise

    # Set up reactive callbacks for queue processing control
    queue_task = None
    main_loop = asyncio.get_running_loop()

    def on_settings_change(enabled, auto_queue_processing):
        nonlocal queue_task
        if enabled and auto_queue_processing:
            if queue_task is None or queue_task.done():
                # Schedule task creation in the main event loop
                asyncio.run_coroutine_threadsafe(create_queue_task(), main_loop)
                logger.info("🟢 Started queue processing (settings enabled)")
        else:
            if queue_task and not queue_task.done():
                queue_task.cancel()
                logger.info("🔴 Stopped queue processing (settings disabled)")

    async def create_queue_task():
        nonlocal queue_task
        queue_task = asyncio.create_task(queue_runner())

    def on_emergency_stop(emergency_stop):
        nonlocal queue_task
        if emergency_stop:
            if queue_task and not queue_task.done():
                queue_task.cancel()
                logger.warning("🚨 Stopped queue processing (emergency stop)")

    service_controller.on_settings_change = on_settings_change
    service_controller.on_emergency_stop = on_emergency_stop

    # Start processing if currently enabled
    if service_controller.is_processing_enabled():
        queue_task = asyncio.create_task(queue_runner())
        logger.info("🟢 Started queue processing (initially enabled)")

    logger.info("Reactive service controller initialized with queue processing capability")

    # Start configuration logging task
    config_logging_task = asyncio.create_task(log_configuration_periodically())
    logger.info("Started configuration logging task (logs every 2 minutes)")

    yield

    # Cleanup
    if config_logging_task:
        config_logging_task.cancel()
        try:
            await config_logging_task
        except asyncio.CancelledError:
            pass
    if service_controller:
        service_controller.stop()
    if config_manager:
        await config_manager.cleanup()
    await redis_client.close()


app = FastAPI(
    title="Flash Preprocessor Service",
    description="High-speed document categorization and routing",
    version="1.0.0",
    lifespan=lifespan,
)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    service_status = {}
    if service_controller:
        service_status = service_controller.get_status()

    return {
        "status": "healthy",
        "service": "categorizer",
        "queue_consumption": service_status,
    }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    try:
        # Update service info metric
        if config and config_manager:
            current_config = await config_manager.get_config()
            service_info.labels(
                version=current_config.version,
                model=current_config.primary_model,
                queue_enabled=str(
                    service_controller.is_processing_enabled() if service_controller else False
                ),
            ).set(1)

        # Update queue depth metrics if Redis is available
        if config:
            try:
                redis = await get_redis_client()
                # Input queue
                input_depth = await redis.llen(config.input_queue)
                queue_depth.labels(queue_name=config.input_queue).set(input_depth)

                # Output queue (single queue for simplified architecture)
                output_depth = await redis.llen(config.output_queue)
                queue_depth.labels(queue_name=config.output_queue).set(output_depth)
            except Exception as e:
                logger.warning(f"Could not update queue metrics: {e}")

        # Update rate limit metrics if available
        if processor:
            try:
                rate_status = await processor.get_rate_limit_status()
                for model_name, status in rate_status.items():
                    if isinstance(status, dict) and "remaining" in status:
                        # Extract provider from model name
                        provider = "gemini" if "gemini" in model_name.lower() else "unknown"
                        if "deepseek" in model_name.lower():
                            provider = "deepseek"
                        elif "ernie" in model_name.lower():
                            provider = "ernie"

                        rate_limit_remaining.labels(
                            provider=provider, model=model_name, limit_type="rpm"
                        ).set(status["remaining"].get("rpm", 0))

                        rate_limit_remaining.labels(
                            provider=provider, model=model_name, limit_type="rpd"
                        ).set(status["remaining"].get("rpd", 0))

                        rate_limit_remaining.labels(
                            provider=provider, model=model_name, limit_type="tpm"
                        ).set(status["remaining"].get("tpm", 0))
            except Exception as e:
                logger.warning(f"Could not update rate limit metrics: {e}")

        # Generate and return metrics
        return Response(content=generate_latest(), media_type=CONTENT_TYPE_LATEST)
    except Exception as e:
        logger.error(f"Error generating metrics: {e}")
        # Return empty metrics rather than error
        return Response(content="", media_type=CONTENT_TYPE_LATEST)


# ============================================================================
# CONFIGURATION MANAGEMENT ENDPOINTS
# ============================================================================


@app.get("/config")
async def get_config():
    """Get current dynamic configuration including prompts and models"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        current_config = await config_manager.get_config()
        config_dict = current_config.model_dump()

        # Add prompt information
        config_dict["prompts"] = ["categorization"]  # Flash preprocessor uses categorization prompt
        config_dict["service"] = "categorizer"
        config_dict["version"] = "1.5.0"

        # Add current prompt version if available
        if hasattr(processor, "categorizer") and hasattr(processor.categorizer, "cached_prompt"):
            config_dict["prompt_version"] = (
                processor.categorizer.cached_prompt.version
                if processor.categorizer.cached_prompt
                else "unknown"
            )

        # Add model information
        config_dict["model"] = config_dict.get("primary_model", "gemini-2.5-flash")
        config_dict["available_models"] = ["gemini-2.5-flash", "deepseek-v3", "ernie-4.5"]

        # Add feature flags
        config_dict["features"] = {
            "queue_consumer_enabled": service_controller.is_processing_enabled()
            if service_controller
            else False,
            "testing_mode": config_dict.get("testing_mode", False),
            "batch_processing": config_dict.get("enable_batch_processing", True),
            "emergency_stop": config_dict.get("emergency_stop", False),
            "database_prompts": True,
        }

        # Add queue information - simplified single-queue architecture
        config_dict["queues"] = {
            "input": config_dict.get("input_queue", "extraction_completed_queue"),
            "output": "analysis_queue",  # Fixed output queue
            "architecture": "single_queue_with_category_metadata",
        }

        # Remove duplicate fields from root level
        fields_to_remove = [
            "enable_queue_consumer",
            "enable_model_fallback",
            "enable_ai_tracking",
            "enable_batch_processing",
            "fallback_provider",
            "fallback_model",
            "max_retries",
            "retry_delay_base_seconds",
            "retry_delay_max_seconds",
            "retry_queue",
            "input_queue",
            "output_queue",
            "dlq",
            "emergency_stop",
            "testing_mode",
        ]
        for field in fields_to_remove:
            config_dict.pop(field, None)

        return config_dict
    except Exception as e:
        logger.error(f"Error getting config: {e}")
        raise HTTPException(500, f"Failed to get configuration: {str(e)}")


@app.post("/config")
async def update_config(updates: dict, updated_by: str = "api_user"):
    """Update configuration with new values"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        # If 'primary_model' is provided, determine the provider automatically
        if "primary_model" in updates:
            model = updates["primary_model"]
            if "gemini" in model.lower():
                updates["primary_provider"] = "gemini"
            elif "deepseek" in model.lower():
                updates["primary_provider"] = "deepseek"
            elif "ernie" in model.lower():
                updates["primary_provider"] = "baidu"

        # Remove all fallback-related settings - fallbacks are forbidden
        updates.pop("fallback_provider", None)
        updates.pop("fallback_model", None)
        updates.pop("enable_model_fallback", None)  # Remove entirely

        new_config = await config_manager.update_config(updates, updated_by)
        logger.info(f"Configuration updated by {updated_by}: {updates}")

        # Reinitialize the preprocessor with the new model
        if hasattr(app.state, "preprocessor") and "primary_model" in updates:
            app.state.preprocessor = SmolLM3Processor(new_config)
            await app.state.preprocessor.initialize()
            logger.info(f"Reinitialized preprocessor with model: {updates['primary_model']}")

        return {
            "success": True,
            "message": "Configuration updated successfully",
            "config": new_config.model_dump(),
        }
    except Exception as e:
        logger.error(f"Error updating config: {e}")
        raise HTTPException(500, f"Failed to update configuration: {str(e)}")


@app.post("/config/provider")
async def switch_provider(provider_update: dict):
    """Switch AI provider and model"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        provider = provider_update.get("provider")
        model = provider_update.get("model")
        updated_by = provider_update.get("updated_by", "api_user")

        if not provider:
            raise HTTPException(400, "Provider is required")

        # Default models for each provider
        default_models = {
            "ernie": "ERNIE-Lite-Pro-128K",
            "gemini": "gemini-2.5-flash",
            "deepseek": "deepseek-chat",
            "smoll": "smolllm3-3b",
        }

        if not model:
            model = default_models.get(provider)
            if not model:
                raise HTTPException(400, f"Unknown provider: {provider}")

        updates = {"primary_provider": provider, "primary_model": model}

        new_config = await config_manager.update_config(updates, updated_by)
        logger.info(f"AI provider switched to {provider}/{model} by {updated_by}")

        return {
            "success": True,
            "message": f"Provider switched to {provider}/{model}",
            "config": {
                "primary_provider": new_config.primary_provider,
                "primary_model": new_config.primary_model,
            },
        }
    except Exception as e:
        logger.error(f"Error switching provider: {e}")
        raise HTTPException(500, f"Failed to switch provider: {str(e)}")


@app.get("/config/status")
async def get_config_status():
    """Get current service configuration status"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        current_config = await config_manager.get_config()
        emergency_status = await config_manager.get_emergency_status()

        return {
            "service": "categorizer",
            "version": current_config.version,
            "last_updated": current_config.last_updated.isoformat(),
            "updated_by": current_config.updated_by,
            "primary_model": f"{current_config.primary_provider}/{current_config.primary_model}",
            "batch_size": current_config.batch_size,
            "emergency_stop": emergency_status["emergency_stop"],
            "maintenance_mode": emergency_status["maintenance_mode"],
            "feature_flags": {
                "queue_consumer": service_controller.is_processing_enabled()
                if service_controller
                else False,
                "batch_processing": current_config.enable_batch_processing,
            },
        }
    except Exception as e:
        logger.error(f"Error getting config status: {e}")
        raise HTTPException(500, f"Failed to get configuration status: {str(e)}")


@app.post("/config/emergency-stop")
async def set_emergency_stop(emergency_update: dict):
    """Set emergency stop flag"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        enabled = emergency_update.get("enabled", False)
        updated_by = emergency_update.get("updated_by", "api_user")

        await config_manager.set_emergency_stop(enabled, updated_by)
        status = await config_manager.get_emergency_status()

        action = "enabled" if enabled else "disabled"
        logger.warning(f"Emergency stop {action} by {updated_by}")

        return {"success": True, "message": f"Emergency stop {action}", "status": status}
    except Exception as e:
        logger.error(f"Error setting emergency stop: {e}")
        raise HTTPException(500, f"Failed to set emergency stop: {str(e)}")


@app.post("/config/batch")
async def update_batch_config(batch_update: dict):
    """Update batch processing configuration"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        updated_by = batch_update.pop("updated_by", "api_user")

        # Only allow specific batch-related updates
        allowed_fields = [
            "batch_size",
            "max_batch_size",
            "min_batch_size",
            "batch_timeout_seconds",
            "enable_batch_processing",
        ]

        updates = {k: v for k, v in batch_update.items() if k in allowed_fields}

        if not updates:
            raise HTTPException(400, "No valid batch configuration fields provided")

        new_config = await config_manager.update_config(updates, updated_by)
        logger.info(f"Batch configuration updated by {updated_by}: {updates}")

        return {
            "success": True,
            "message": "Batch configuration updated",
            "config": {
                "batch_size": new_config.batch_size,
                "max_batch_size": new_config.max_batch_size,
                "min_batch_size": new_config.min_batch_size,
                "batch_timeout_seconds": new_config.batch_timeout_seconds,
                "enable_batch_processing": new_config.enable_batch_processing,
            },
        }
    except Exception as e:
        logger.error(f"Error updating batch config: {e}")
        raise HTTPException(500, f"Failed to update batch configuration: {str(e)}")


# ============================================================================


@app.get("/service/status")
async def get_service_status():
    """Get detailed service status including queue consumption"""
    if not service_controller:
        return {"error": "Service controller not initialized"}

    status = service_controller.get_status()
    status["queue_processing_active"] = service_controller.is_processing_enabled()
    status["monitoring_active"] = True
    return status


@app.post("/service/toggle")
async def toggle_service_locally():
    """Toggle service queue consumption via reactive API"""
    if not service_controller:
        raise HTTPException(500, "Service controller not initialized")

    try:
        # Get current state and toggle
        current_enabled = service_controller.is_enabled
        new_enabled = not current_enabled

        # Call reactive settings API to toggle
        import httpx

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://reactive-settings-api:8026/api/v1/services/categorizer",
                json={"enabled": new_enabled},
            )

        if response.status_code == 200:
            return {"status": "success", "enabled": new_enabled}
        else:
            raise HTTPException(500, f"Failed to toggle via reactive API: {response.text}")

    except Exception as e:
        logger.error(f"Error toggling service: {e}")
        raise HTTPException(500, f"Failed to toggle service: {str(e)}")


@app.get("/rate-limits")
async def get_rate_limits():
    """Get current rate limit status for all models"""
    if not processor:
        raise HTTPException(500, "Processor not initialized")

    try:
        status = await processor.get_rate_limit_status()
        return status
    except Exception as e:
        logger.error(f"Error getting rate limit status: {e}")
        raise HTTPException(500, f"Failed to get rate limit status: {str(e)}")


@app.get("/rate-limits/config")
async def get_rate_limit_configs():
    """Get rate limit configurations for all models"""
    if not rate_limit_config:
        raise HTTPException(500, "Rate limit config not initialized")

    try:
        configs = await rate_limit_config.get_all_configs()
        return {"configs": configs}
    except Exception as e:
        logger.error(f"Error getting rate limit configs: {e}")
        raise HTTPException(500, f"Failed to get configs: {str(e)}")


@app.put("/rate-limits/config/{model}")
async def update_rate_limit_config(model: str, update: RateLimitUpdate):
    """Update rate limit configuration for a specific model"""
    if not rate_limit_config:
        raise HTTPException(500, "Rate limit config not initialized")

    try:
        config_dict = {
            "rpm": update.rpm,
            "rpd": update.rpd,
            "tpm": update.tpm,
            "enabled": update.enabled,
        }

        success = await rate_limit_config.set_config(model, config_dict)
        if success:
            return {"status": "success", "model": model, "config": config_dict}
        else:
            raise HTTPException(400, "Failed to update configuration")

    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        logger.error(f"Error updating rate limit config: {e}")
        raise HTTPException(500, f"Failed to update config: {str(e)}")


@app.post("/rate-limits/config/{model}/toggle")
async def toggle_rate_limiting(model: str, enabled: bool):
    """Enable or disable rate limiting for a model"""
    if not rate_limit_config:
        raise HTTPException(500, "Rate limit config not initialized")

    try:
        success = await rate_limit_config.enable_rate_limiting(model, enabled)
        if success:
            return {"status": "success", "model": model, "enabled": enabled}
        else:
            raise HTTPException(400, "Failed to toggle rate limiting")

    except Exception as e:
        logger.error(f"Error toggling rate limiting: {e}")
        raise HTTPException(500, f"Failed to toggle: {str(e)}")


@app.post("/rate-limits/config/reset")
async def reset_rate_limits(model: Optional[str] = None):
    """Reset rate limits to defaults for all models or a specific model"""
    if not rate_limit_config:
        raise HTTPException(500, "Rate limit config not initialized")

    try:
        success = await rate_limit_config.reset_to_defaults(model)
        if success:
            return {
                "status": "success",
                "message": f"Reset {'all models' if not model else model} to defaults",
            }
        else:
            raise HTTPException(400, "Failed to reset rate limits")

    except Exception as e:
        logger.error(f"Error resetting rate limits: {e}")
        raise HTTPException(500, f"Failed to reset: {str(e)}")


@app.get("/batch-config", response_model=BatchConfigResponse)
async def get_batch_config():
    """Get current batch processing configuration"""
    if not config:
        raise HTTPException(500, "Configuration not initialized")

    return BatchConfigResponse(
        batch_size=config.batch_size,
        max_batch_size=config.max_batch_size,
        min_batch_size=config.min_batch_size,
        batch_timeout=config.batch_timeout,
        enabled=service_controller.is_processing_enabled() if service_controller else False,
    )


@app.put("/batch-config", response_model=BatchConfigResponse)
async def update_batch_config(update: BatchConfigUpdate):
    """Update batch processing configuration"""
    if not config:
        raise HTTPException(500, "Configuration not initialized")

    try:
        # Update configuration
        if update.batch_size is not None:
            if (
                update.batch_size < config.min_batch_size
                or update.batch_size > config.max_batch_size
            ):
                raise HTTPException(
                    400,
                    f"Batch size must be between {config.min_batch_size} and {config.max_batch_size}",
                )
            config.batch_size = update.batch_size
            logger.info(f"Updated batch size to {update.batch_size}")

        if update.batch_timeout is not None:
            config.batch_timeout = update.batch_timeout
            logger.info(f"Updated batch timeout to {update.batch_timeout}s")

        # Return updated configuration
        return BatchConfigResponse(
            batch_size=config.batch_size,
            max_batch_size=config.max_batch_size,
            min_batch_size=config.min_batch_size,
            batch_timeout=config.batch_timeout,
            enabled=service_controller.is_processing_enabled() if service_controller else False,
        )

    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        logger.error(f"Error updating batch config: {e}")
        raise HTTPException(500, f"Failed to update config: {str(e)}")


@app.get("/models/config")
async def get_model_config():
    """Get current model configuration"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        current_config = await config_manager.get_config()
        return {
            "primary_model": current_config.primary_model,
            "categorization_model": current_config.primary_model,  # Use primary model for categorization
        }
    except Exception as e:
        logger.error(f"Error getting model config: {e}")
        raise HTTPException(500, f"Failed to get model configuration: {str(e)}")


@app.put("/models/config")
async def update_model_config(model: str):
    """Update model configuration"""
    if not config_manager:
        raise HTTPException(500, "Configuration manager not initialized")

    try:
        # Determine provider from model name
        if "gemini" in model.lower():
            provider = "gemini"
        elif "deepseek" in model.lower():
            provider = "deepseek"
        elif "ernie" in model.lower():
            provider = "baidu"
        elif "smoll" in model.lower() or "smolllm" in model.lower():
            provider = "smoll"
        else:
            raise HTTPException(400, f"Unknown model: {model}")

        # Update configuration
        updates = {"primary_provider": provider, "primary_model": model}

        await config_manager.update_config(updates, "api_user")

        return {
            "status": "success",
            "config": {
                "primary_model": model,
                "primary_provider": provider,
            },
        }

    except Exception as e:
        logger.error(f"Error updating model config: {e}")
        raise HTTPException(500, f"Failed to update model config: {str(e)}")


@app.get("/models/available")
async def get_available_models():
    """Get list of available models"""
    return {
        "gemini": [
            "gemini-2.5-flash-lite-preview-06-17",
            "gemini-2.5-flash",
            "gemini-2.5-pro",
            "gemini-1.5-pro",
            "gemini-1.5-flash",
        ],
        "deepseek": ["deepseek-chat", "deepseek-v3"],
        "ernie": [
            "ERNIE-Lite-Pro-128K",
            "ERNIE-Speed-Pro-128K",
            "ERNIE-4.5-21B-A3B",
            "ERNIE-4.0-Turbo-128K",
        ],
        "smoll": ["smolllm3-3b", "HuggingFaceTB/SmolLM3-3B"],
    }


@app.post("/config/reload")
async def reload_configuration():
    """Reload configuration including prompts from database"""
    try:
        # Reload dynamic configuration
        if config_manager:
            await config_manager.reload_config()
            logger.info("Reloaded dynamic configuration")

        # Reload prompts from database
        if processor:
            await processor.reload_prompt()
            logger.info("Reloaded categorization prompt from database in multi-model processor")

        # Also reload for queue-based categorizer if it exists
        if processor and hasattr(processor, "categorizer"):
            await processor.categorizer.reload_prompt()
            logger.info("Reloaded categorization prompt from database")

        # Get updated configuration
        current_config = await config_manager.get_config() if config_manager else None

        return {
            "status": "success",
            "message": "Configuration and prompts reloaded successfully",
            "config": {
                "model": current_config.primary_model if current_config else "unknown",
                "prompt_version": processor.categorizer.cached_prompt.version
                if (
                    processor
                    and hasattr(processor, "categorizer")
                    and processor.categorizer.cached_prompt
                )
                else "unknown",
                "emergency_stop": current_config.emergency_stop if current_config else False,
            },
        }
    except Exception as e:
        logger.error(f"Failed to reload configuration: {e}")
        raise HTTPException(500, f"Failed to reload configuration: {str(e)}")


@app.post("/reload-prompts")
async def reload_prompts():
    """Reload prompts from database (alias for /config/reload)"""
    return await reload_configuration()


@app.post("/preprocess", response_model=PreprocessResponse)
async def preprocess_document(
    request: PreprocessRequest, background_tasks: BackgroundTasks
) -> PreprocessResponse:
    """
    Preprocess a single document
    Returns categorization and routing information
    """
    if not processor:
        raise HTTPException(500, "Processor not initialized")

    # Fetch document from Redis queue
    redis = await get_redis_client()
    doc_data = await redis.hget("queued_documents", request.document_id)

    if not doc_data:
        raise HTTPException(404, f"Document {request.document_id} not found in queue")

    # Parse document
    document = QueuedDocument.model_validate_json(doc_data)

    # Preprocess with timing
    with processing_time.labels(operation="single_document").time():
        result = await processor.preprocess(document)

    # Increment processed counter
    documents_processed.labels(category=result.category, status="success").inc()

    # Determine routing
    priority = processor.get_processing_priority(result)
    immediate = await processor.should_process_immediately(result)

    # Simplified single-queue architecture: all categorized documents go to analysis_queue
    # Pro analyzer will handle category-based prompt selection internally
    queue_name = "analysis_queue"

    # Add to background task: update document metadata
    background_tasks.add_task(
        update_document_metadata, request.document_id, result, queue_name, priority
    )

    response = PreprocessResponse(
        document_id=request.document_id,
        category=result.category,
        quality_score=result.quality_assessment,
        processing_priority=priority,
        immediate_processing=immediate,
        queue_name=queue_name,
    )

    # Log model statistics if available
    if hasattr(processor, "get_model_statistics"):
        stats = processor.get_model_statistics()
        if stats and logfire:
            logfire.info("model_statistics", stats=stats)

    return response


@app.post("/preprocess/batch")
async def preprocess_batch(document_ids: list[str], background_tasks: BackgroundTasks):
    """
    Preprocess multiple documents in parallel
    Optimized for high throughput
    """
    if not processor:
        raise HTTPException(500, "Processor not initialized")

    # Fetch all documents
    redis = await get_redis_client()
    pipeline = redis.pipeline()
    for doc_id in document_ids:
        pipeline.hget("queued_documents", doc_id)

    doc_data_list = await pipeline.execute()

    # Parse documents
    documents = []
    for _doc_id, doc_data in zip(document_ids, doc_data_list, strict=False):
        if doc_data:
            documents.append(QueuedDocument.model_validate_json(doc_data))

    if not documents:
        raise HTTPException(404, "No valid documents found")

    # Batch preprocess with timing
    with processing_time.labels(operation="batch").time():
        results = await processor.batch_preprocess(documents)

    # Process results
    response = []
    for doc, result in zip(documents, results, strict=False):
        # Increment processed counter
        documents_processed.labels(category=result.category, status="success").inc()
        priority = processor.get_processing_priority(result)
        immediate = await processor.should_process_immediately(result)
        queue_name = get_queue_name(result.category)

        response.append(
            {
                "document_id": doc.id,
                "category": result.category,
                "quality_score": result.quality_assessment,
                "processing_priority": priority,
                "immediate_processing": immediate,
                "queue_name": queue_name,
            }
        )

        # Schedule metadata update
        background_tasks.add_task(update_document_metadata, doc.id, result, queue_name, priority)

    return {"processed": len(response), "results": response}


async def update_document_metadata(
    document_id: str, result: PreprocessingResult, queue_name: str, priority: int
):
    """Background task to update document metadata after preprocessing"""
    redis = await get_redis_client()

    # Update preprocessing result
    await redis.hset(
        f"preprocessing_results:{document_id}",
        mapping={
            "category": result.category,
            "quality_score": str(result.quality_assessment),
            "priority": str(priority),
            "queue": queue_name,
            "title_english": result.title_english,
        },
    )

    # Add to appropriate queue
    await redis.zadd(queue_name, {document_id: priority})

    # Log
    if logfire:
        logfire.info(
            "document_routed",
            document_id=document_id,
            category=result.category,
            queue=queue_name,
            priority=priority,
        )


def get_queue_name(category: str) -> str:
    """Return single analysis queue for simplified architecture"""
    # Simplified architecture: all categorized documents go to single analysis_queue
    # Category information is preserved in document metadata for prompt selection
    return "analysis_queue"


class TestModelRequest(BaseModel):
    """Request model for test endpoint"""

    content: str = Field(..., description="Document content to categorize")
    provider: str = Field(..., description="AI provider: gemini, deepseek, or ernie")
    model_name: str = Field(..., description="Model name like gemini-2.5-flash, ERNIE-4.5-21B-A3B")
    title: str = Field(default="Test Document", description="Document title")


@app.post("/test/model")
async def test_specific_model(request: TestModelRequest):
    """
    Simple test endpoint that uses any model directly
    Just pass provider and model name - that's it!
    """
    import time

    from .simple_categorizer_with_db import categorize_document

    try:
        # Validate provider
        valid_providers = ["gemini", "deepseek", "ernie"]
        if request.provider not in valid_providers:
            raise HTTPException(
                400, f"Invalid provider '{request.provider}'. Must be one of: {valid_providers}"
            )

        # Validate model name based on provider
        if request.provider == "gemini" and not request.model_name.startswith("gemini"):
            raise HTTPException(400, f"Invalid model '{request.model_name}' for provider 'gemini'")
        elif request.provider == "deepseek" and "deepseek" not in request.model_name.lower():
            raise HTTPException(
                400, f"Invalid model '{request.model_name}' for provider 'deepseek'"
            )
        elif request.provider == "ernie" and "ernie" not in request.model_name.lower():
            raise HTTPException(400, f"Invalid model '{request.model_name}' for provider 'ernie'")

        # Validate content
        if not request.content.strip():
            raise HTTPException(400, "Content cannot be empty")

        # Time the categorization
        start_time = time.time()
        result = await categorize_document(
            request.content, request.title, request.provider, request.model_name
        )
        processing_time = time.time() - start_time

        # Add processing time and request metadata
        result["processing_time"] = processing_time
        result["request_metadata"] = {
            "provider": request.provider,
            "model_name": request.model_name,
            "title": request.title,
            "content_length": len(request.content),
        }

        return result

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        raise HTTPException(500, f"Categorization failed: {str(e)}")


@app.post("/trigger-batch")
async def trigger_batch_processing(batch_size: int = 1, background_tasks: BackgroundTasks = None):
    """
    Trigger one batch processing run from the queue
    This simulates what the queue consumer would do
    CRITICAL: Always processes exactly 1 document at a time
    """
    if not processor:
        raise HTTPException(500, "Processor not initialized")

    try:
        redis = await get_redis_client()

        # Check queue size
        queue_size = await redis.llen(config.input_queue)
        if queue_size == 0:
            return {
                "status": "no_documents",
                "message": f"No documents in {config.input_queue}",
                "queue_size": 0,
            }

        # CRITICAL: Force batch_size to 1 - ALWAYS process exactly 1 document at a time
        actual_batch_size = 1  # Override any request parameter
        documents = []
        document_ids = []
        original_documents = {}  # Store original documents by ID

        for _ in range(actual_batch_size):
            doc_json = await redis.rpop(config.input_queue)
            if doc_json:
                doc = json.loads(doc_json)
                # Store original document structure
                original_documents[doc.get("id")] = doc
                # Convert to QueuedDocument format
                from shared.models.documents import DocumentSource

                # Create DocumentSource object
                source_type = doc.get("source", "tdnet")
                # Ensure source_type is valid
                valid_sources = [
                    "tdnet",
                    "boj",
                    "fed",
                    "ecb",
                    "edinet",
                    "bcb",
                    "b3",
                    "edgar",
                    "cvm",
                    "local_files",
                ]
                if source_type not in valid_sources:
                    source_type = "tdnet"  # default fallback

                # Handle datetime parsing
                try:
                    pub_date_str = doc.get("discovery", {}).get("source", {}).get("published_date") or doc.get(
                    "published_at", doc.get("created_at", datetime.now().isoformat())
                )
                    if pub_date_str.endswith("Z"):
                        pub_date_str = pub_date_str.replace("Z", "+00:00")
                    published_date = datetime.fromisoformat(pub_date_str)
                except:
                    published_date = datetime.now()

                source_obj = DocumentSource(
                    url=doc.get("url", "http://example.com"),
                    title=doc.get("title", ""),
                    source_type=source_type,
                    published_date=published_date,
                    scraped_date=datetime.now(),
                )

                # Extract content from nested extraction structure or fallback to flat fields
                extraction_data = doc.get("extraction", {})
                content = (
                    extraction_data.get("content", "")
                    or extraction_data.get("text", "")  # New unified content field
                    or doc.get("extracted_text", "")  # Fallback to old text field
                    or doc.get("content", "")  # Legacy format  # Another legacy format
                )

                # For multimodal fields, use content for both text and markdown
                extracted_content = (
                    extraction_data.get("content")
                    or extraction_data.get("text")
                    or doc.get("extracted_text")
                )

                queued_doc = QueuedDocument(
                    id=doc.get("id", f"batch_{datetime.now().timestamp()}"),
                    title=doc.get("title", ""),
                    content=content,
                    url=doc.get("url", ""),
                    source=source_obj,
                    published_at=published_date,
                    metadata=doc.get("metadata", {}),
                    # Include multimodal data from nested extraction or flat fields
                    extracted_text=extracted_content,  # Use unified content
                    extracted_markdown=extracted_content,  # Same content (already in markdown format)
                    extraction_assets=extraction_data.get("assets") or doc.get("extraction_assets"),
                    extraction_images=extraction_data.get("images") or doc.get("extraction_images"),
                    extraction_tables=extraction_data.get("tables") or doc.get("extraction_tables"),
                    extraction_metadata=extraction_data.get("metadata")
                    or doc.get("extraction_metadata"),
                )
                documents.append(queued_doc)
                document_ids.append(queued_doc.id)

        if not documents:
            return {"status": "error", "message": "No valid documents found"}

        # Process batch with timing and handle failures
        with processing_time.labels(operation="queue_batch").time():
            try:
                results = await processor.batch_preprocess(documents)
            except Exception as e:
                # Handle catastrophic batch processing failure
                logger.error(f"Batch processing failed catastrophically: {e}")
                # Route all documents to DLQ - preserve original structure
                redis_client = await get_redis_client()
                dlq_name = "categorization_failed_dlq"
                failed_docs = []
                for doc in documents:
                    doc_data = original_documents[doc.id].copy()  # Use original document
                    doc_data["categorization_failed_at"] = datetime.now().isoformat()
                    doc_data["final_error"] = str(e)
                    doc_data["failure_reason"] = "Batch processing catastrophic failure"
                    await redis_client.lpush(dlq_name, json.dumps(doc_data))
                    failed_docs.append(
                        {"document_id": doc.id, "error": str(e), "output_queue": dlq_name}
                    )

                return {
                    "status": "error",
                    "message": f"Batch processing failed: {str(e)}",
                    "failed_documents": failed_docs,
                    "dlq_queue": dlq_name,
                }

        # Route documents to output queues
        processed_docs = []
        for doc, result in zip(documents, results):
            # Check if this is a failed document that should go to DLQ
            if result.category is None or getattr(result, "error", None):
                # Route to DLQ - preserve original document structure
                redis_client = await get_redis_client()
                dlq_name = "categorization_failed_dlq"
                doc_data = original_documents[doc.id].copy()  # Use original document
                doc_data["categorization_failed_at"] = getattr(
                    result, "failed_at", datetime.now().isoformat()
                )
                doc_data["final_error"] = getattr(result, "error", "Unknown categorization error")
                doc_data["failure_reason"] = "Categorization processing failed"
                await redis_client.lpush(dlq_name, json.dumps(doc_data))

                # Track failed document
                documents_processed.labels(category="categorization_failed", status="failed").inc()
                processed_docs.append(
                    {
                        "document_id": doc.id,
                        "error": getattr(result, "error", "Unknown categorization error"),
                        "output_queue": dlq_name,
                    }
                )
                continue

            # Increment processed counter for successful documents
            documents_processed.labels(category=result.category, status="success").inc()
            # CRITICAL: Preserve original document structure and add categorization data
            # Use the original document with all pipeline data (discovery, extraction, etc.)
            doc_data = original_documents[doc.id].copy()  # Start with original full document

            # Get the full categorization result - MUST be available
            if hasattr(result, "primary_category") and result.primary_category:
                # Use the complete categorization result from the flattened structure
                doc_data["categorization"] = {
                    "primary_category": result.primary_category,
                    "secondary_category": result.secondary_category,
                    "confidence": result.confidence,
                    "model_used": result.model_used,
                    "language": result.language,
                }
            else:
                # No fallback data construction allowed - fail loudly
                raise RuntimeError(
                    f"Document {doc.id} lacks proper categorization_result - processing cannot continue"
                )

            # Update pipeline metadata to reflect categorization stage
            if "pipeline" not in doc_data:
                doc_data["pipeline"] = {}

            doc_data["pipeline"]["current_stage"] = "categorization"
            doc_data["pipeline"]["current_queue"] = config.output_queue

            # Add categorization to validation gates passed
            if "validation_gates_passed" not in doc_data["pipeline"]:
                doc_data["pipeline"]["validation_gates_passed"] = []
            if "categorization" not in doc_data["pipeline"]["validation_gates_passed"]:
                doc_data["pipeline"]["validation_gates_passed"].append("categorization")

            # Add categorization timestamp
            if "processing_timestamps" not in doc_data["pipeline"]:
                doc_data["pipeline"]["processing_timestamps"] = {}
            doc_data["pipeline"]["processing_timestamps"][
                "categorization"
            ] = datetime.now().isoformat()

            # Ensure we don't have legacy fields in the new format
            if "category" in doc_data["categorization"]:
                logger.warning(
                    f"Found legacy 'category' field in categorization for {doc.id}, this should not happen"
                )
            if "subcategory" in doc_data["categorization"]:
                logger.warning(
                    f"Found legacy 'subcategory' field in categorization for {doc.id}, this should not happen"
                )

            # Route to output queue - check subcategory first, then category
            # Subcategory to primary category mapping
            subcategory_to_primary = {
                "earnings": "corporate_filing",
                "dividend": "corporate_filing",
                "buyback": "corporate_filing",
                "m&a": "corporate_filing",
                "guidance": "corporate_filing",
                "shares": "corporate_filing",
                "restructuring": "corporate_filing",
                "ipo": "corporate_filing",
                # Add more mappings as needed
            }

            # Route to output queue (should be analysis_queue per config)
            queue_name = config.output_queue
            try:
                await redis.lpush(queue_name, json.dumps(doc_data, default=str))
                logger.info(f"✅ Routed document {doc.id} to {queue_name}")
            except Exception as e:
                logger.error(f"❌ Failed to route document {doc.id} to {queue_name}: {e}")

            processed_docs.append(
                {
                    "document_id": doc.id,
                    "category": result.category,
                    "quality_score": result.quality_assessment,
                    "output_queue": queue_name,
                }
            )

        return {
            "status": "success",
            "processed": len(processed_docs),
            "queue_size_before": queue_size,
            "queue_size_after": await redis.llen(config.input_queue),
            "documents": processed_docs,
        }

    except Exception as e:
        logger.error(f"Batch trigger error: {e}")
        raise HTTPException(500, f"Batch processing failed: {str(e)}")


# Standardized batch configuration endpoints
@app.get("/batch-config")
async def get_batch_config():
    """Get current batch processing configuration"""
    try:
        if config_manager:
            current_config = await config_manager.get_config()
            return {
                "batch_size": current_config.batch_size,
                "max_batch_size": current_config.max_batch_size,
                "min_batch_size": current_config.min_batch_size,
                "batch_timeout_seconds": current_config.batch_timeout_seconds,
                "enable_batch_processing": current_config.enable_batch_processing,
                "service": "categorizer",
                "timestamp": datetime.now().isoformat(),
            }
        else:
            return HTTPException(503, "Configuration manager not initialized")
    except Exception as e:
        logger.error(f"Error getting batch config: {e}")
        raise HTTPException(500, f"Failed to get batch config: {str(e)}")


@app.put("/batch-config")
async def update_batch_config(config_update: dict):
    """Update batch processing configuration"""
    try:
        if not config_manager:
            raise HTTPException(503, "Configuration manager not initialized")

        # Only allow specific batch-related updates
        allowed_fields = [
            "batch_size",
            "max_batch_size",
            "min_batch_size",
            "batch_timeout_seconds",
            "enable_batch_processing",
        ]

        updates = {k: v for k, v in config_update.items() if k in allowed_fields}

        if not updates:
            raise HTTPException(400, "No valid batch configuration fields provided")

        updated_by = config_update.get("updated_by", "api_user")
        updated_config = await config_manager.update_config(updates, updated_by)

        logger.info(f"Batch configuration updated by {updated_by}: {updates}")

        return {
            "status": "success",
            "message": "Batch configuration updated successfully",
            "updated_fields": updates,
            "current_config": {
                "batch_size": updated_config.batch_size,
                "max_batch_size": updated_config.max_batch_size,
                "min_batch_size": updated_config.min_batch_size,
                "batch_timeout_seconds": updated_config.batch_timeout_seconds,
                "enable_batch_processing": updated_config.enable_batch_processing,
            },
            "timestamp": datetime.now().isoformat(),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating batch config: {e}")
        raise HTTPException(500, f"Failed to update batch config: {str(e)}")


@app.post("/reload-prompt")
async def reload_prompt():
    """Reload categorization prompt from database"""
    try:
        if not processor:
            raise HTTPException(500, "Processor not initialized")

        # Force reload the prompt
        success = await processor.reload_prompt()

        if success:
            return {
                "status": "success",
                "message": "Categorization prompt reloaded successfully",
                "timestamp": datetime.now().isoformat(),
            }
        else:
            raise HTTPException(500, "Failed to reload prompt from database")

    except Exception as e:
        logger.error(f"Error reloading prompt: {e}")
        raise HTTPException(500, f"Failed to reload prompt: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(app, host="0.0.0.0", port=port)
