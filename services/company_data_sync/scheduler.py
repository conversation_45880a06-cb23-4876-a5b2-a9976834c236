#!/usr/bin/env python3
"""
ABOUTME: Cron scheduler script for company data sync service
ABOUTME: Triggers the company data sync service via HTTP API calls
"""

import asyncio
import aiohttp
import os
import sys
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def trigger_full_sync():
    """Trigger full company data sync"""
    service_url = os.getenv('COMPANY_DATA_SYNC_URL', 'http://company-data-sync:8050')
    timeout = int(os.getenv('SCHEDULER_TIMEOUT', 7200))  # 2 hours default
    
    try:
        logger.info(f"🚀 Starting scheduled company data sync at {datetime.now()}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            # Trigger full sync
            async with session.post(f"{service_url}/api/trigger/full") as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ Successfully triggered full sync: {result}")
                elif response.status == 409:
                    logger.warning("⚠️ Service already running, skipping this scheduled run")
                else:
                    logger.error(f"❌ Failed to trigger sync: HTTP {response.status}")
                    sys.exit(1)
                    
        logger.info(f"📊 Scheduled sync completed at {datetime.now()}")
        
    except asyncio.TimeoutError:
        logger.error(f"❌ Sync timed out after {timeout} seconds")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Error during scheduled sync: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(trigger_full_sync())