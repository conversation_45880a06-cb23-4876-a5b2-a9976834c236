# ABOUTME: Cron container for scheduled company data sync
# ABOUTME: Runs daily at 2 AM to trigger company data enrichment

FROM python:3.11-slim

# Install cron, curl, and psmisc (for pgrep) for healthchecks
RUN apt-get update && apt-get install -y \
    cron \
    curl \
    psmisc \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY services/company_data_sync/requirements.txt .
RUN pip install --no-cache-dir aiohttp python-dotenv

# Copy scheduler script
COPY services/company_data_sync/scheduler.py .

# Create cron log directory
RUN mkdir -p /var/log/cron

# Create cron script
RUN echo '#!/bin/bash' > /app/run_scheduler.sh && \
    echo 'cd /app' >> /app/run_scheduler.sh && \
    echo 'python scheduler.py >> /var/log/cron/company-data-sync.log 2>&1' >> /app/run_scheduler.sh && \
    chmod +x /app/run_scheduler.sh

# Create startup script
RUN echo '#!/bin/bash' > /app/start_cron.sh && \
    echo 'echo "Starting Company Data Sync Cron Scheduler..."' >> /app/start_cron.sh && \
    echo 'echo "Cron schedule: ${CRON_SCHEDULE:-0 2 * * *}"' >> /app/start_cron.sh && \
    echo 'echo "Service URL: ${COMPANY_DATA_SYNC_URL:-http://company-data-sync:8050}"' >> /app/start_cron.sh && \
    echo '' >> /app/start_cron.sh && \
    echo '# Add cron job' >> /app/start_cron.sh && \
    echo 'echo "${CRON_SCHEDULE:-0 2 * * *} /app/run_scheduler.sh" | crontab -' >> /app/start_cron.sh && \
    echo '' >> /app/start_cron.sh && \
    echo '# Start cron in foreground' >> /app/start_cron.sh && \
    echo 'echo "Cron scheduler started. Logs available at /var/log/cron/company-data-sync.log"' >> /app/start_cron.sh && \
    echo 'cron -f' >> /app/start_cron.sh && \
    chmod +x /app/start_cron.sh

# Environment variables
ENV COMPANY_DATA_SYNC_URL=http://company-data-sync:8050
ENV CRON_SCHEDULE="0 2 * * *"
ENV SCHEDULER_TIMEOUT=7200

# Create logs volume mount point
VOLUME ["/var/log/cron"]

# Health check - simple process check
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD test -d /proc/1 || exit 1

# Run cron in foreground
CMD ["/app/start_cron.sh"]