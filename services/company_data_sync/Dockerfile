# ABOUTME: Docker container for company data sync service with IBKR integration
# ABOUTME: Scheduled daily enrichment of Japanese and US company fundamental data

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY services/company_data_sync/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy service code
COPY services/company_data_sync/service.py .

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Environment variables with defaults
ENV PORT=8050
ENV ENABLE_SCHEDULING=true
ENV SCHEDULE_TIME=02:00
ENV JP_BATCH_SIZE=5
ENV US_BATCH_SIZE=10
ENV REQUEST_DELAY=3.0
ENV BATCH_DELAY=15.0
ENV MAX_COMPANIES_PER_RUN=500

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/api/status || exit 1

# Expose port
EXPOSE ${PORT}

# Run the service
CMD ["python", "service.py"]