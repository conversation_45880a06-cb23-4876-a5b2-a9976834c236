#!/usr/bin/env python3
"""
ABOUTME: Company market data enrichment service with IBKR integration and daily scheduling
ABOUTME: Unified service for Japanese and US company fundamental data enrichment via IBKR API
"""

import asyncio
import json
import logging
import os
import signal
import sys
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import time
import threading
try:
    import schedule
except ImportError:
    schedule = None

import asyncpg
import redis.asyncio as redis
from aiohttp import web
from aiohttp_cors import ResourceOptions, setup
from prometheus_client import Counter, Gauge, Histogram, generate_latest
from ib_insync import IB, Stock, util
import xml.etree.ElementTree as ET
import re

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Prometheus metrics
enrichment_documents_processed_total = Counter(
    "company_enrichment_documents_processed_total",
    "Total companies processed by enrichment service",
    ["country", "status"],  # status: success/failed/skipped
)

enrichment_processing_duration = Histogram(
    "company_enrichment_processing_duration_seconds",
    "Company enrichment processing duration",
    ["country", "data_source"],  # JP/US, ibkr/cache/fallback
)

enrichment_queue_depths = Gauge(
    "company_enrichment_queue_depths",
    "Current enrichment processing status",
    ["metric_name"],  # pending_jp, pending_us, processed_today, etc.
)

enrichment_data_quality = Gauge(
    "company_enrichment_data_quality",
    "Average data quality score",
    ["country"],
)


class CompanyMarketDataService:
    """Unified company market data enrichment service"""
    
    def __init__(self):
        # Database configuration
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5435)),
            'user': os.getenv('DB_USER', 'omotesamba'),
            'password': os.getenv('DB_PASSWORD', 'omotesamba'),
            'database': os.getenv('DB_NAME', 'omotesamba')
        }
        
        # IBKR configuration
        self.ib_host = os.getenv('IB_HOST', 'tws')
        self.ib_port = int(os.getenv('IB_PORT', 7499))  # TWS API port
        self.ib_client_id = int(os.getenv('IB_CLIENT_ID', 25))
        
        # Rate limiting configuration
        self.jp_batch_size = int(os.getenv('JP_BATCH_SIZE', 5))
        self.us_batch_size = int(os.getenv('US_BATCH_SIZE', 10))
        self.request_delay = float(os.getenv('REQUEST_DELAY', 3.0))
        self.batch_delay = float(os.getenv('BATCH_DELAY', 15.0))
        
        # Service configuration
        self.enable_scheduling = os.getenv('ENABLE_SCHEDULING', 'true').lower() == 'true'
        self.schedule_time = os.getenv('SCHEDULE_TIME', '02:00')  # Daily at 2 AM
        self.max_companies_per_run = int(os.getenv('MAX_COMPANIES_PER_RUN', 500))
        
        # Redis configuration
        self.redis_url = os.getenv('REDIS_URL', 'redis://redis:6379')
        
        # Runtime state
        self.db_pool = None
        self.redis_client = None
        self.ib = None
        self.is_running = False
        self.current_job = None
        self.scheduler_thread = None
        
        # Processing state for real-time visibility
        self.current_processing = {
            'status': 'idle',  # idle, processing_jp, processing_us
            'current_company': None,
            'current_batch': 0,
            'total_batches': 0,
            'batch_progress': 0,
            'batch_size': 0,
            'companies_processed_today': 0,
            'companies_skipped_today': 0,
            'current_country': None,
            'processing_start_time': None,
            'estimated_completion': None
        }
        
        # Statistics
        self.stats = {
            'last_run': None,
            'jp_processed': 0,
            'us_processed': 0,
            'jp_errors': 0,
            'us_errors': 0,
            'total_runtime': 0
        }
        
    async def startup(self):
        """Initialize all service components"""
        try:
            logger.info("🚀 Starting Company Market Data Service...")
            
            # Initialize database
            self.db_pool = await asyncpg.create_pool(**self.db_config)
            logger.info("✅ Connected to TimescaleDB")
            
            # Initialize Redis
            try:
                self.redis_client = redis.from_url(self.redis_url)
                await self.redis_client.ping()
                logger.info("✅ Connected to Redis")
            except Exception as e:
                logger.warning(f"⚠️ Redis connection failed: {e}, continuing without cache")
                self.redis_client = None
            
            # Initialize IBKR connection
            await self._initialize_ibkr()
            
            # Start scheduler if enabled
            if self.enable_scheduling:
                self._start_scheduler()
                logger.info(f"✅ Scheduler started - daily run at {self.schedule_time}")
            
            logger.info("🎉 Company Market Data Service startup complete!")
            
        except Exception as e:
            logger.error(f"❌ Service startup failed: {e}")
            raise
            
    async def shutdown(self):
        """Cleanup all service components"""
        try:
            logger.info("🔄 Shutting down Company Market Data Service...")
            
            # Stop scheduler
            if self.scheduler_thread and schedule is not None:
                schedule.clear()
                self.scheduler_thread = None
                
            # Cleanup IBKR
            if self.ib and self.ib.isConnected():
                self.ib.disconnect()
                
            # Cleanup Redis
            if self.redis_client:
                await self.redis_client.close()
                
            # Cleanup database
            if self.db_pool:
                await self.db_pool.close()
                
            logger.info("✅ Service shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")
            
    async def _initialize_ibkr(self):
        """Initialize IBKR connection"""
        try:
            self.ib = IB()
            await self.ib.connectAsync(self.ib_host, self.ib_port, clientId=self.ib_client_id)
            logger.info(f"✅ Connected to IBKR at {self.ib_host}:{self.ib_port}")
        except Exception as e:
            logger.warning(f"⚠️ IBKR connection failed: {e}, will retry during processing")
            self.ib = None
            
    def _start_scheduler(self):
        """Start the scheduling thread"""
        if schedule is None:
            logger.warning("⚠️ Schedule module not available, scheduling disabled")
            return
            
        schedule.every().day.at(self.schedule_time).do(self._schedule_enrichment)
        
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
    def _schedule_enrichment(self):
        """Scheduled enrichment job"""
        logger.info("📅 Starting scheduled company enrichment...")
        asyncio.create_task(self.run_full_enrichment())
        
    async def get_companies_to_process(self, country: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get companies that need enrichment for given country"""
        
        # Different strategies for JP vs US
        if country == 'JP':
            query = """
            SELECT c.code, c.symbol, c.company_name, c.company_name_english,
                   c.sector_17_name, c.scale_category, c.market_name,
                   CASE c.scale_category
                       WHEN 'TOPIX Core30' THEN 1
                       WHEN 'TOPIX Large70' THEN 2
                       WHEN 'TOPIX Mid400' THEN 3
                       ELSE 4
                   END as scale_priority
            FROM companies c
            LEFT JOIN company_market_data cf ON c.code = cf.code 
                AND c.country = cf.country 
                AND cf.snapshot_date = CURRENT_DATE
            WHERE c.country = 'JP' 
            AND c.is_active = true
            AND c.data_source = 'jquants'
            AND cf.id IS NULL  -- Only companies without recent data
            AND c.code IN (
                SELECT code FROM companies WHERE country = 'JP' 
                AND scale_category IN ('TOPIX Core30', 'TOPIX Large70', 'TOPIX Mid400', 'TOPIX Small')
                UNION
                SELECT code FROM companies WHERE country = 'JP' 
                AND code IN ('7203', '6758', '9984', '8306', '4502', '6861', '9432', '9433', '7267', '8058')
            )
            ORDER BY scale_priority, c.code
            """
        else:  # US
            query = """
            SELECT c.code, c.symbol, c.company_name, c.company_name_english,
                   c.sector_17_name, c.scale_category, c.market_name
            FROM companies c
            LEFT JOIN company_market_data cf ON c.code = cf.code 
                AND c.country = cf.country 
                AND cf.snapshot_date = CURRENT_DATE
            WHERE c.country = 'US' 
            AND c.is_active = true
            AND c.data_source = 'ibkr'
            AND cf.id IS NULL  -- Only companies without recent data
            ORDER BY c.market_name, c.code
            """
        
        if limit:
            query += f" LIMIT {limit}"
            
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch(query)
            
        companies = []
        for row in rows:
            companies.append({
                'code': row['code'],
                'symbol': row['symbol'],
                'company_name': row['company_name'],
                'company_name_english': row['company_name_english'],
                'sector': row['sector_17_name'],
                'scale_category': row['scale_category'],
                'market': row['market_name']
            })
            
        logger.info(f"Found {len(companies)} {country} companies to process")
        return companies
        
    def create_stock_contract(self, country: str, code: str) -> Stock:
        """Create IBKR stock contract for given country and code"""
        if country == 'JP':
            return Stock(code, "TSEJ", "JPY")
        else:  # US
            return Stock(code, "SMART", "USD")
            
    async def fetch_company_data(self, company: Dict[str, Any], country: str) -> Optional[Dict[str, Any]]:
        """Fetch comprehensive company data from IBKR"""
        code = company['code']
        symbol = company['symbol']
        
        try:
            # Ensure IBKR connection
            if not self.ib or not self.ib.isConnected():
                await self._initialize_ibkr()
                
            if not self.ib or not self.ib.isConnected():
                logger.warning(f"IBKR not available for {code}, skipping")
                return None
            
            logger.info(f"Processing {code} ({company['company_name']})")
            
            # Create and qualify contract
            contract = self.create_stock_contract(country, code)
            contracts = await self.ib.qualifyContractsAsync(contract)
            
            if not contracts:
                logger.warning(f"Could not qualify contract for {code}")
                return None
                
            qualified = contracts[0]
            logger.debug(f"Qualified contract: {qualified}")
            
            # Initialize data structure
            market_data = {
                'country': country,
                'code': code,
                'symbol': symbol,
                'snapshot_date': date.today(),
                'data_source': 'ibkr',
                'currency': 'JPY' if country == 'JP' else 'USD'
            }
            
            # Get current market data (synchronous - ib_insync handles this)
            try:
                ticker = self.ib.reqMktData(qualified, '', False, False)
                await asyncio.sleep(2)  # Wait for data
                
                if ticker and hasattr(ticker, 'last') and ticker.last and ticker.last > 0:
                    market_data.update({
                        'current_price': float(ticker.last),
                        'week_52_high': float(ticker.high52) if hasattr(ticker, 'high52') and ticker.high52 and ticker.high52 > 0 else None,
                        'week_52_low': float(ticker.low52) if hasattr(ticker, 'low52') and ticker.low52 and ticker.low52 > 0 else None,
                        'avg_volume_30d': int(ticker.avgVolume) if hasattr(ticker, 'avgVolume') and ticker.avgVolume and ticker.avgVolume > 0 else None
                    })
                    
                    # For US stocks, try to get market cap
                    if country == 'US' and hasattr(ticker, 'sharesOutstanding') and ticker.sharesOutstanding:
                        market_data['market_cap'] = int(ticker.last * ticker.sharesOutstanding)
                        market_data['shares_outstanding'] = int(ticker.sharesOutstanding)
                    
                    logger.info(f"Got market data for {code}: {market_data['currency']}{ticker.last}")
                
                # Cancel market data subscription
                self.ib.cancelMktData(qualified)
                
            except Exception as e:
                logger.warning(f"Error getting market data for {code}: {e}")
            
            # Try to get fundamental data (often not available)
            try:
                fundamental_xml = await self.ib.reqFundamentalDataAsync(qualified, "ReportsFinSummary")
                if fundamental_xml:
                    fundamentals = self._parse_fundamental_xml(fundamental_xml)
                    market_data.update(fundamentals)
                    logger.info(f"Got fundamental data for {code}: {len(fundamentals)} fields")
            except Exception as e:
                logger.debug(f"No fundamental data for {code}: {e}")
            
            # Calculate data quality score
            market_data['data_quality_score'] = self._calculate_data_quality(market_data)
            
            logger.info(f"✅ Successfully enriched {code} with quality score {market_data['data_quality_score']}")
            return market_data
            
        except Exception as e:
            logger.error(f"Error processing {code}: {e}")
            return None
            
    def _parse_fundamental_xml(self, xml_data: str) -> Dict[str, Any]:
        """Parse IBKR fundamental XML data"""
        data = {}
        try:
            # Remove namespace prefixes for easier parsing
            clean_xml = re.sub(r'xmlns[^=]*="[^"]*"', '', xml_data)
            clean_xml = re.sub(r'[a-zA-Z]+:', '', clean_xml)
            
            root = ET.fromstring(clean_xml)
            
            # Parse financial highlights
            for highlight in root.findall('.//FinancialHighlight'):
                # Revenue
                revenue = highlight.find('.//Revenue[@periodType="TTM"]')
                if revenue is not None and revenue.text:
                    try:
                        data['revenue_ttm'] = int(float(revenue.text) * 1000000)
                    except:
                        pass
                
                # Net Income  
                net_income = highlight.find('.//NetIncome[@periodType="TTM"]')
                if net_income is not None and net_income.text:
                    try:
                        data['net_income_ttm'] = int(float(net_income.text) * 1000000)
                    except:
                        pass
            
            # Parse ratios
            for ratio_group in root.findall('.//Ratios'):
                # P/E Ratio
                pe_ratio = ratio_group.find('.//PriceEarningsRatio')
                if pe_ratio is not None and pe_ratio.text:
                    try:
                        data['pe_ratio'] = float(pe_ratio.text)
                    except:
                        pass
                
                # P/B Ratio
                pb_ratio = ratio_group.find('.//PriceBookRatio')
                if pb_ratio is not None and pb_ratio.text:
                    try:
                        data['pb_ratio'] = float(pb_ratio.text)
                    except:
                        pass
                
                # ROE
                roe = ratio_group.find('.//ReturnOnEquity')
                if roe is not None and roe.text:
                    try:
                        data['roe'] = float(roe.text) / 100
                    except:
                        pass
                
                # Debt to Equity
                debt_equity = ratio_group.find('.//DebtEquityRatio')
                if debt_equity is not None and debt_equity.text:
                    try:
                        data['debt_to_equity'] = float(debt_equity.text)
                    except:
                        pass
            
        except Exception as e:
            logger.warning(f"Error parsing fundamental XML: {e}")
            
        return data
        
    def _calculate_data_quality(self, data: Dict[str, Any]) -> float:
        """Calculate data quality score"""
        required_fields = ['current_price']
        important_fields = ['pe_ratio', 'revenue_ttm', 'net_income_ttm']
        optional_fields = ['pb_ratio', 'roe', 'debt_to_equity', 'week_52_high', 'week_52_low']
        
        required_score = sum(1 for field in required_fields if data.get(field) is not None) / len(required_fields)
        important_score = sum(1 for field in important_fields if data.get(field) is not None) / len(important_fields)
        optional_score = sum(1 for field in optional_fields if data.get(field) is not None) / len(optional_fields)
        
        # Weighted score: required 60%, important 30%, optional 10%
        total_score = (required_score * 0.6) + (important_score * 0.3) + (optional_score * 0.1)
        return round(total_score, 2)
        
    async def save_company_data(self, market_data: Dict[str, Any]) -> bool:
        """Save company market data to database"""
        try:
            insert_query = """
            INSERT INTO company_market_data (
                country, code, symbol, snapshot_date, data_source, currency,
                current_price, market_cap, shares_outstanding, pe_ratio, pb_ratio,
                roe, debt_to_equity, revenue_ttm, net_income_ttm,
                week_52_high, week_52_low, avg_volume_30d, data_quality_score
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
            )
            ON CONFLICT (country, code, snapshot_date, data_source)
            DO UPDATE SET
                current_price = EXCLUDED.current_price,
                market_cap = EXCLUDED.market_cap,
                shares_outstanding = EXCLUDED.shares_outstanding,
                pe_ratio = EXCLUDED.pe_ratio,
                pb_ratio = EXCLUDED.pb_ratio,
                roe = EXCLUDED.roe,
                debt_to_equity = EXCLUDED.debt_to_equity,
                revenue_ttm = EXCLUDED.revenue_ttm,
                net_income_ttm = EXCLUDED.net_income_ttm,
                week_52_high = EXCLUDED.week_52_high,
                week_52_low = EXCLUDED.week_52_low,
                avg_volume_30d = EXCLUDED.avg_volume_30d,
                data_quality_score = EXCLUDED.data_quality_score,
                last_updated = CURRENT_TIMESTAMP
            """
            
            async with self.db_pool.acquire() as conn:
                await conn.execute(
                    insert_query,
                    market_data.get('country'), market_data.get('code'),
                    market_data.get('symbol'), market_data.get('snapshot_date'),
                    market_data.get('data_source'), market_data.get('currency'),
                    market_data.get('current_price'), market_data.get('market_cap'),
                    market_data.get('shares_outstanding'), market_data.get('pe_ratio'),
                    market_data.get('pb_ratio'), market_data.get('roe'),
                    market_data.get('debt_to_equity'), market_data.get('revenue_ttm'),
                    market_data.get('net_income_ttm'), market_data.get('week_52_high'),
                    market_data.get('week_52_low'), market_data.get('avg_volume_30d'),
                    market_data.get('data_quality_score')
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving data for {market_data.get('code')}: {e}")
            return False
            
    async def process_companies(self, country: str, limit: Optional[int] = None) -> Dict[str, int]:
        """Process companies for given country with rate limiting"""
        start_time = datetime.now()
        processed = 0
        errors = 0
        skipped = 0
        
        # Update processing state
        self.current_processing.update({
            'status': f'processing_{country.lower()}',
            'current_country': country,
            'processing_start_time': start_time,
            'companies_processed_today': 0,
            'companies_skipped_today': 0
        })
        
        try:
            logger.info(f"🚀 Starting {country} companies enrichment process...")
            
            # Get companies to process
            companies = await self.get_companies_to_process(country, limit)
            
            if not companies:
                logger.info(f"✅ No {country} companies need processing - all up to date!")
                self.current_processing['status'] = 'idle'
                return {'processed': 0, 'errors': 0, 'skipped': 0}
            
            batch_size = self.jp_batch_size if country == 'JP' else self.us_batch_size
            total_batches = (len(companies) + batch_size - 1) // batch_size
            
            # Update processing state
            self.current_processing.update({
                'total_batches': total_batches,
                'batch_size': batch_size
            })
            
            logger.info(f"📋 Found {len(companies)} {country} companies to process in {total_batches} batches")
            
            # Process in batches with rate limiting
            for i in range(0, len(companies), batch_size):
                batch = companies[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                # Update batch progress
                self.current_processing.update({
                    'current_batch': batch_num,
                    'batch_progress': 0
                })
                
                logger.info(f"🔄 Processing {country} batch {batch_num}/{total_batches}: {len(batch)} companies")
                
                for idx, company in enumerate(batch):
                    # Update current company being processed
                    self.current_processing.update({
                        'current_company': {
                            'code': company['code'],
                            'name': company['company_name'],
                            'symbol': company['symbol']
                        },
                        'batch_progress': idx + 1
                    })
                    
                    company_code = company['code']
                    company_name = company.get('company_name', 'N/A')
                    
                    try:
                        logger.info(f"🔍 Processing {country} company: {company_code} ({company_name})")
                        
                        # Check if already processed today (double-check)
                        if await self._is_processed_today(country, company_code):
                            logger.info(f"⏭️ Skipping {company_code} - already processed today")
                            skipped += 1
                            self.current_processing['companies_skipped_today'] += 1
                            enrichment_documents_processed_total.labels(country=country, status='skipped').inc()
                            continue
                        
                        # Fetch data from IBKR
                        logger.info(f"📡 Fetching IBKR data for {company_code}...")
                        market_data = await self.fetch_company_data(company, country)
                        
                        if market_data:
                            # Save to database
                            logger.info(f"💾 Saving market data for {company_code}...")
                            success = await self.save_company_data(market_data)
                            if success:
                                processed += 1
                                self.current_processing['companies_processed_today'] += 1
                                enrichment_documents_processed_total.labels(country=country, status='success').inc()
                                logger.info(f"✅ Successfully enriched {company_code} with quality score {market_data.get('data_quality_score', 'N/A')}")
                            else:
                                errors += 1
                                enrichment_documents_processed_total.labels(country=country, status='failed').inc()
                                logger.error(f"❌ Failed to save data for {company_code}")
                        else:
                            errors += 1
                            enrichment_documents_processed_total.labels(country=country, status='skipped').inc()
                            logger.warning(f"⚠️ No market data available for {company_code}")
                        
                        # Rate limiting between requests
                        if idx < len(batch) - 1:  # Don't wait after last company in batch
                            logger.debug(f"⏸️ Rate limiting: waiting {self.request_delay}s before next company...")
                            await asyncio.sleep(self.request_delay)
                        
                    except Exception as e:
                        logger.error(f"❌ Error processing {company_code}: {e}")
                        errors += 1
                        enrichment_documents_processed_total.labels(country=country, status='failed').inc()
                
                # Wait between batches
                if i + batch_size < len(companies):
                    logger.info(f"⏸️ Batch complete. Waiting {self.batch_delay}s before next batch...")
                    await asyncio.sleep(self.batch_delay)
            
            # Update metrics
            duration = (datetime.now() - start_time).total_seconds()
            enrichment_processing_duration.labels(country=country, data_source='ibkr').observe(duration)
            
            logger.info(f"🎉 {country} processing complete!")
            logger.info(f"📊 Results: {processed} processed, {skipped} skipped (already current), {errors} errors")
            logger.info(f"⏱️ Total time: {duration:.1f}s")
            
            # Reset processing state
            self.current_processing['status'] = 'idle'
            self.current_processing['current_company'] = None
            
            return {'processed': processed, 'errors': errors, 'skipped': skipped}
            
        except Exception as e:
            logger.error(f"💥 Critical error processing {country} companies: {e}")
            self.current_processing['status'] = 'error'
            return {'processed': processed, 'errors': errors, 'skipped': skipped}
            
    async def run_full_enrichment(self):
        """Run full enrichment for both JP and US companies"""
        if self.is_running:
            logger.warning("Enrichment already running, skipping...")
            return
            
        self.is_running = True
        self.current_job = datetime.now()
        
        try:
            logger.info("🚀 Starting full company enrichment...")
            
            # Process Japanese companies
            jp_results = await self.process_companies('JP', self.max_companies_per_run // 2)
            self.stats['jp_processed'] = jp_results['processed']
            self.stats['jp_errors'] = jp_results['errors']
            
            # Small break between countries
            await asyncio.sleep(30)
            
            # Process US companies
            us_results = await self.process_companies('US', self.max_companies_per_run // 2)
            self.stats['us_processed'] = us_results['processed']
            self.stats['us_errors'] = us_results['errors']
            
            # Update statistics
            self.stats['last_run'] = datetime.now()
            
            # Update metrics
            await self._update_metrics()
            
            total_processed = jp_results['processed'] + us_results['processed']
            total_errors = jp_results['errors'] + us_results['errors']
            
            logger.info("🎉 Full enrichment complete!")
            logger.info(f"📊 Total processed: {total_processed}, Total errors: {total_errors}")
            
        except Exception as e:
            logger.error(f"Error during full enrichment: {e}")
        finally:
            self.is_running = False
            self.current_job = None
            
    async def _update_metrics(self):
        """Update Prometheus metrics"""
        try:
            async with self.db_pool.acquire() as conn:
                # Pending companies counts
                jp_pending = await conn.fetchval("""
                    SELECT COUNT(*) FROM companies c
                    LEFT JOIN company_market_data cf ON c.code = cf.code 
                        AND c.country = cf.country 
                        AND cf.snapshot_date >= CURRENT_DATE - INTERVAL '1 day'
                    WHERE c.country = 'JP' AND c.is_active = true AND cf.id IS NULL
                """)
                
                us_pending = await conn.fetchval("""
                    SELECT COUNT(*) FROM companies c
                    LEFT JOIN company_market_data cf ON c.code = cf.code 
                        AND c.country = cf.country 
                        AND cf.snapshot_date >= CURRENT_DATE - INTERVAL '1 day'
                    WHERE c.country = 'US' AND c.is_active = true AND cf.id IS NULL
                """)
                
                # Today's processed counts
                jp_today = await conn.fetchval("""
                    SELECT COUNT(*) FROM company_market_data 
                    WHERE country = 'JP' AND snapshot_date = CURRENT_DATE
                """)
                
                us_today = await conn.fetchval("""
                    SELECT COUNT(*) FROM company_market_data 
                    WHERE country = 'US' AND snapshot_date = CURRENT_DATE
                """)
                
                # Average quality scores
                jp_quality = await conn.fetchval("""
                    SELECT AVG(data_quality_score) FROM company_market_data 
                    WHERE country = 'JP' AND snapshot_date = CURRENT_DATE
                """) or 0.0
                
                us_quality = await conn.fetchval("""
                    SELECT AVG(data_quality_score) FROM company_market_data 
                    WHERE country = 'US' AND snapshot_date = CURRENT_DATE
                """) or 0.0
                
                # Update metrics
                enrichment_queue_depths.labels(metric_name='pending_jp').set(jp_pending)
                enrichment_queue_depths.labels(metric_name='pending_us').set(us_pending)
                enrichment_queue_depths.labels(metric_name='processed_jp_today').set(jp_today)
                enrichment_queue_depths.labels(metric_name='processed_us_today').set(us_today)
                
                enrichment_data_quality.labels(country='JP').set(jp_quality)
                enrichment_data_quality.labels(country='US').set(us_quality)
                
        except Exception as e:
            logger.warning(f"Error updating metrics: {e}")
            
    async def _is_processed_today(self, country: str, code: str) -> bool:
        """Check if a company has already been processed today"""
        try:
            async with self.db_pool.acquire() as conn:
                result = await conn.fetchval("""
                    SELECT COUNT(*) FROM company_market_data 
                    WHERE country = $1 AND code = $2 AND snapshot_date = CURRENT_DATE
                """, country, code)
                return result > 0
        except Exception as e:
            logger.warning(f"Error checking if {code} processed today: {e}")
            return False  # If we can't check, proceed with processing
            
    async def get_status(self) -> Dict[str, Any]:
        """Get service status with detailed processing information"""
        try:
            await self._update_metrics()
            
            # Calculate estimated completion time if processing
            estimated_completion = None
            if self.current_processing['status'] != 'idle' and self.current_processing['processing_start_time']:
                elapsed = (datetime.now() - self.current_processing['processing_start_time']).total_seconds()
                if self.current_processing['current_batch'] > 0:
                    avg_time_per_batch = elapsed / self.current_processing['current_batch']
                    remaining_batches = self.current_processing['total_batches'] - self.current_processing['current_batch']
                    estimated_seconds = remaining_batches * avg_time_per_batch
                    estimated_completion = (datetime.now() + timedelta(seconds=estimated_seconds)).isoformat()
            
            # Prepare current processing info
            processing_info = self.current_processing.copy()
            if processing_info['processing_start_time']:
                processing_info['processing_start_time'] = processing_info['processing_start_time'].isoformat()
            processing_info['estimated_completion'] = estimated_completion
            
            status = {
                'service': 'company-market-data',
                'status': 'running' if self.is_running else 'idle',
                'current_job': self.current_job.isoformat() if self.current_job else None,
                'last_run': self.stats['last_run'].isoformat() if self.stats['last_run'] else None,
                'stats': self.stats.copy(),
                'current_processing': processing_info,
                'configuration': {
                    'enable_scheduling': self.enable_scheduling,
                    'schedule_time': self.schedule_time,
                    'jp_batch_size': self.jp_batch_size,
                    'us_batch_size': self.us_batch_size,
                    'request_delay': self.request_delay,
                    'batch_delay': self.batch_delay,
                    'max_companies_per_run': self.max_companies_per_run
                },
                'connections': {
                    'database': bool(self.db_pool),
                    'redis': bool(self.redis_client),
                    'ibkr': bool(self.ib and self.ib.isConnected())
                }
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting status: {e}")
            return {'status': 'error', 'message': str(e)}


# Service instance
service = CompanyMarketDataService()


# HTTP API handlers
async def handle_status(request):
    """Get service status"""
    status = await service.get_status()
    return web.json_response(status)


async def handle_trigger_jp(request):
    """Trigger Japanese companies enrichment"""
    if service.is_running:
        return web.json_response({'error': 'Service already running'}, status=409)
    
    # Get limit from query params
    limit = request.query.get('limit')
    limit = int(limit) if limit else None
    
    asyncio.create_task(service.process_companies('JP', limit))
    return web.json_response({'message': 'Japanese companies enrichment started', 'limit': limit})


async def handle_trigger_us(request):
    """Trigger US companies enrichment"""
    if service.is_running:
        return web.json_response({'error': 'Service already running'}, status=409)
    
    # Get limit from query params
    limit = request.query.get('limit')
    limit = int(limit) if limit else None
    
    asyncio.create_task(service.process_companies('US', limit))
    return web.json_response({'message': 'US companies enrichment started', 'limit': limit})


async def handle_trigger_full(request):
    """Trigger full enrichment"""
    if service.is_running:
        return web.json_response({'error': 'Service already running'}, status=409)
    
    asyncio.create_task(service.run_full_enrichment())
    return web.json_response({'message': 'Full enrichment started'})


async def handle_metrics(request):
    """Prometheus metrics endpoint"""
    return web.Response(text=generate_latest().decode('utf-8'), content_type='text/plain')


async def init_app():
    """Initialize web application"""
    app = web.Application()
    
    # Setup CORS
    cors = setup(app, defaults={
        "*": ResourceOptions(
            allow_credentials=True,
            expose_headers="*",
            allow_headers="*",
            allow_methods="*"
        )
    })
    
    # Add routes
    app.router.add_get('/api/status', handle_status)
    app.router.add_post('/api/trigger/jp', handle_trigger_jp)
    app.router.add_post('/api/trigger/us', handle_trigger_us)
    app.router.add_post('/api/trigger/full', handle_trigger_full)
    app.router.add_get('/metrics', handle_metrics)
    
    # Add CORS to all routes
    for route in list(app.router.routes()):
        cors.add(route)
    
    return app


async def main():
    """Main service entry point"""
    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(service.shutdown())
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize service
        await service.startup()
        
        # Initialize web app
        app = await init_app()
        
        # Start web server
        runner = web.AppRunner(app)
        await runner.setup()
        
        port = int(os.getenv('PORT', 8050))
        site = web.TCPSite(runner, '0.0.0.0', port)
        await site.start()
        
        logger.info(f"🌐 Company Market Data Service running on port {port}")
        logger.info(f"📊 Status: http://localhost:{port}/api/status")
        logger.info(f"📈 Metrics: http://localhost:{port}/metrics")
        
        # Keep running
        while True:
            await asyncio.sleep(3600)  # Sleep for 1 hour
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Service error: {e}")
    finally:
        await service.shutdown()


if __name__ == "__main__":
    asyncio.run(main())