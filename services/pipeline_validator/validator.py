"""
ABOUTME: Pipeline validation service that enforces quality gates between processing stages
ABOUTME: Routes failed documents to appropriate DLQ queues with detailed failure reasons
"""

import json
import logging
import os
import re
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

import redis.asyncio as redis
from pydantic import BaseModel
from shared.utils.company_normalizer import normalize_document_company_info

logger = logging.getLogger(__name__)


class ValidationStage(str, Enum):
    """Pipeline validation stages"""

    DISCOVERY = "discovery"
    EXTRACTION = "extraction"
    ENRICHMENT = "enrichment"
    CATEGORIZATION = "categorization"
    ANALYSIS = "analysis"
    MARKET_ANALYSIS = "market_analysis"
    TRADING_SIGNALS = "trading_signals"
    STORAGE = "storage"


class ValidationResult(BaseModel):
    """Result of pipeline validation"""

    passed: bool
    stage: ValidationStage
    failures: List[str]
    warnings: List[str]
    quality_score: float
    should_retry: bool = False
    dlq_queue: Optional[str] = None


class PipelineValidator:
    """
    Central pipeline validation service that enforces quality gates
    between each processing stage and routes failures to appropriate DLQs
    """

    def __init__(self):
        self.redis_client = None

        # Quality thresholds - standardized confidence scoring (0.0 - 1.0)
        self.min_content_length = 150
        self.min_analysis_length = 150
        self.min_market_analysis_length = 200
        self.min_quality_score = 0.6  # General minimum for pipeline progression
        self.min_categorization_confidence = 0.3  # Temporarily lowered for SmolLM3 confidence calibration
        self.min_analysis_confidence = 0.6  # Standard threshold for analysis
        self.min_trading_confidence = 0.6  # Standard threshold for trading signals

        # DLQ queue names
        self.dlq_queues = {
            ValidationStage.DISCOVERY: "discovery_quality_dlq",
            ValidationStage.EXTRACTION: "extraction_quality_dlq",
            ValidationStage.ENRICHMENT: "enrichment_quality_dlq",
            ValidationStage.CATEGORIZATION: "categorization_quality_dlq",
            ValidationStage.ANALYSIS: "analysis_quality_dlq",
            ValidationStage.MARKET_ANALYSIS: "market_analysis_quality_dlq",
            ValidationStage.TRADING_SIGNALS: "trading_signals_quality_dlq",
            ValidationStage.STORAGE: "storage_quality_dlq",
        }

        # Special DLQ for malformed JSON that can't be parsed
        self.malformed_json_dlq = "malformed_json_dlq"

    async def connect(self):
        """Connect to Redis"""
        try:
            redis_url = f"redis://{os.environ.get('REDIS_HOST', 'localhost')}:{os.environ.get('REDIS_PORT', 6379)}"
            self.redis_client = await redis.from_url(redis_url, decode_responses=True)
            await self.redis_client.ping()
            logger.info("✅ Connected to Redis for pipeline validation")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            raise

    async def validate_discovery(self, document: Dict) -> ValidationResult:
        """
        Validate document after discovery stage

        Quality gates:
        - Must have valid URL
        - Must have document title
        - Must have source information
        - Must have basic metadata
        """
        failures = []
        warnings = []
        quality_score = 1.0

        # Check URL (from discovery.source structure)
        discovery_source = document.get("discovery", {}).get("source", {})
        logger.info(f"🔍 DISCOVERY DEBUG - discovery_source type: {type(discovery_source)}")
        logger.info(f"🔍 DISCOVERY DEBUG - discovery_source: {discovery_source}")
        url = discovery_source.get("url", "").strip()
        logger.info(f"🔍 DISCOVERY DEBUG - url extracted: '{url}'")
        logger.info(f"🔍 DISCOVERY DEBUG - url empty check: {not url}")
        if not url:
            failures.append("Missing document URL")
            quality_score -= 0.5
        elif not (url.startswith("http://") or url.startswith("https://")):
            failures.append("Invalid URL format - must start with http:// or https://")
            quality_score -= 0.3

        # Check title (from discovery.source structure)
        title = discovery_source.get("title", "").strip()
        logger.info(f"🔍 DISCOVERY DEBUG - title extracted: '{title}'")
        logger.info(f"🔍 DISCOVERY DEBUG - title empty check: {not title}")
        if not title:
            failures.append("Missing document title")
            quality_score -= 0.4
        elif len(title) < 10:
            warnings.append(f"Document title is very short: {len(title)} chars")
            quality_score -= 0.1

        # Check source information (from discovery.source structure)
        source_type = discovery_source.get("source_type", "")
        logger.info(f"🔍 DISCOVERY DEBUG - source_type extracted: '{source_type}'")
        logger.info(f"🔍 DISCOVERY DEBUG - source_type empty check: {not source_type}")
        if not source_type:
            failures.append("Missing source information")
            quality_score -= 0.3

        # Check document ID
        doc_id = document.get("id", "").strip()
        if not doc_id:
            failures.append("Missing document ID")
            quality_score -= 0.3

        # Check internal_id (optional - warn if missing but don't fail)
        internal_id = document.get("internal_id")
        if internal_id is None:
            warnings.append("Missing internal_id (optional for tracking)")
            quality_score -= 0.1  # Minor warning only
        elif not isinstance(internal_id, int) or internal_id <= 0:
            warnings.append("Invalid internal_id - should be positive integer")
            quality_score -= 0.1  # Minor warning only

        # Check published date (warning if missing)
        published_at = document.get("published_at")
        if not published_at:
            warnings.append("Missing published_at timestamp")
            quality_score -= 0.1

        # Check for basic metadata structure
        metadata = document.get("metadata", {})
        if not isinstance(metadata, dict):
            warnings.append("Metadata is not in expected dict format")
            quality_score -= 0.1

        passed = len(failures) == 0 and quality_score >= self.min_quality_score
        dlq_queue = None if passed else self.dlq_queues[ValidationStage.DISCOVERY]

        return ValidationResult(
            passed=passed,
            stage=ValidationStage.DISCOVERY,
            failures=failures,
            warnings=warnings,
            quality_score=max(0.0, quality_score),
            should_retry=False,  # Retries disabled
            dlq_queue=dlq_queue,
        )

    async def validate_extraction(self, document: Dict) -> ValidationResult:
        """
        Validate document after extraction stage

        Quality gates:
        - Must have extracted content with >= 150 characters (new schema: extraction.content, old schema: extraction.text/extracted_text)
        - Must not be "[Non-PDF document: ...]" placeholder
        - Must have extraction_method != "error"
        """
        failures = []
        warnings = []
        quality_score = 1.0

        # Support both NEW and OLD schema formats
        extraction_data = document.get("extraction", {})
        
        # NEW SCHEMA: Check extraction.content (unified field) or fallback to old text/markdown
        if extraction_data:
            # Try new unified content field first, then fallback to old fields
            extracted_text = extraction_data.get("content", "") or extraction_data.get("text", "")
            extracted_markdown = extraction_data.get("content", "") or extraction_data.get("markdown", "")
            extraction_metadata = extraction_data.get("metadata", {})
            extraction_method = extraction_metadata.get("extraction_method", "")
            
            logger.info(f"🔍 NEW SCHEMA: Document {document.get('id', 'unknown')} has extraction namespace")
        else:
            # OLD SCHEMA: Check extracted_text and extracted_markdown
            extracted_text = document.get("extracted_text", "")
            extracted_markdown = document.get("extracted_markdown", "")
            extraction_metadata = document.get("extraction_metadata", {})
            extraction_method = document.get("extraction_method", "")
            
            logger.info(f"🔍 OLD SCHEMA: Document {document.get('id', 'unknown')} using legacy format")

        # Use the better quality extraction if available
        primary_content = extracted_markdown if extracted_markdown else extracted_text

        if not primary_content or len((primary_content or "").strip()) < self.min_content_length:
            failures.append(
                f"Insufficient extracted content: {len(primary_content or '')} chars (min {self.min_content_length})"
            )
            quality_score -= 0.5

        # Check for non-PDF placeholder in either format
        if (extracted_text and extracted_text.startswith("[Non-PDF document:")) or (extracted_markdown and extracted_markdown.startswith(
            "[Non-PDF document:"
        )):
            failures.append("Document marked as non-PDF but may actually be PDF content")
            quality_score -= 0.3

        # Validate extraction metadata quality
        if extraction_metadata:
            # NEW SCHEMA: Check tables_extracted vs OLD SCHEMA: has_tables
            if extraction_data:
                # New schema: check tables_extracted count
                tables_extracted = extraction_metadata.get("tables_extracted", 0)
                has_tables = tables_extracted > 0
                tables_count = len(extraction_data.get("tables", []))
                
                # Validate consistency between metadata and actual tables
                if tables_extracted != tables_count:
                    warnings.append(f"Tables metadata inconsistent: metadata says {tables_extracted} but found {tables_count} tables")
                    quality_score -= 0.1
            else:
                # Old schema: check has_tables flag
                has_tables = extraction_metadata.get("has_tables", False)
                tables_extracted = extraction_metadata.get("tables_found", 0)
            
            # Check if tables were detected for financial documents
            if (
                not has_tables
                and primary_content
                and any(
                    keyword in (primary_content or "").lower()
                    for keyword in ["revenue", "profit", "earnings", "financial"]
                )
            ):
                warnings.append(
                    "Financial document detected but no tables extracted - possible extraction quality issue"
                )
                quality_score -= 0.1

        # Check extraction method
        if extraction_method == "error":
            failures.append("Extraction failed with error status")
            quality_score -= 0.4
        elif extraction_method == "skipped":
            warnings.append("Extraction was skipped - verify if this is correct")
            quality_score -= 0.2

        # Check for extraction errors
        if document.get("extraction_error"):
            failures.append(f"Extraction error: {document['extraction_error']}")
            quality_score -= 0.3

        # NEW SCHEMA: Additional validation for structured data
        if extraction_data:
            # Validate structured tables
            tables = extraction_data.get("tables", [])
            if tables and not isinstance(tables, list):
                failures.append("Extraction tables must be a list")
                quality_score -= 0.2
            
            # Validate structured images
            images = extraction_data.get("images", [])
            if images and not isinstance(images, list):
                failures.append("Extraction images must be a list")
                quality_score -= 0.2
            
            # Validate structured formulas
            formulas = extraction_data.get("formulas", [])
            if formulas and not isinstance(formulas, list):
                failures.append("Extraction formulas must be a list")
                quality_score -= 0.2
            
            # Validate extraction quality score
            extraction_quality = extraction_metadata.get("extraction_quality_score", 0)
            if extraction_quality < 0.3:
                warnings.append(f"Low extraction quality score: {extraction_quality}")
                quality_score -= 0.1

        passed = len(failures) == 0 and quality_score >= self.min_quality_score
        dlq_queue = None if passed else self.dlq_queues[ValidationStage.EXTRACTION]

        return ValidationResult(
            passed=passed,
            stage=ValidationStage.EXTRACTION,
            failures=failures,
            warnings=warnings,
            quality_score=max(0.0, quality_score),
            should_retry=False,  # Retries disabled
            dlq_queue=dlq_queue,
        )

    async def validate_enrichment(self, document: Dict) -> ValidationResult:
        """
        Validate document after enrichment stage (Layer 3.5)
        
        Validates:
        - Document has enrichment data with fundamentals
        - Market data is present and valid
        - Company matching was successful
        - Data sources are documented
        """
        failures = []
        warnings = []
        quality_score = 1.0

        # Check if document has enrichment namespace (new schema)
        if "enrichment" in document:
            logger.info(f"🔍 NEW SCHEMA: Document {document.get('id')} has enrichment namespace")
            enrichment = document["enrichment"]
            
            # Check for fundamentals data
            if "fundamentals_at_disclosure" not in enrichment:
                failures.append("Missing fundamentals_at_disclosure data")
                quality_score -= 0.3
            else:
                fundamentals = enrichment["fundamentals_at_disclosure"]
                
                # Check required fundamental fields
                required_fields = ["price_at_disclosure", "market_cap", "shares_outstanding"]
                for field in required_fields:
                    if field not in fundamentals or fundamentals[field] is None:
                        failures.append(f"Missing required fundamental field: {field}")
                        quality_score -= 0.1
                
                # Validate price data
                if "price_at_disclosure" in fundamentals:
                    price = fundamentals["price_at_disclosure"]
                    if price <= 0:
                        failures.append("Invalid price_at_disclosure (must be positive)")
                        quality_score -= 0.2
            
            # Check for peer comparison (optional but preferred)
            if "peer_comparison" not in enrichment:
                warnings.append("No peer comparison data")
                quality_score -= 0.05
            
            # Check for historical context (optional but preferred)
            if "historical_context" not in enrichment:
                warnings.append("No historical context data")
                quality_score -= 0.05
            
            # Check data sources
            if "data_sources" not in enrichment or not enrichment["data_sources"]:
                failures.append("No data sources documented")
                quality_score -= 0.1
            
            # Check enrichment timestamp
            if "enrichment_timestamp" not in enrichment:
                warnings.append("Missing enrichment timestamp")
        else:
            # Legacy check - enrichment data might be at root level
            failures.append("Missing enrichment namespace - document not properly enriched")
            quality_score = 0.0

        # Check pipeline metadata
        pipeline = document.get("pipeline", {})
        
        # Ensure extraction was completed before enrichment
        gates_passed = pipeline.get("validation_gates_passed", [])
        if "extraction" not in gates_passed:
            failures.append("Document cannot skip extraction before enrichment")
            quality_score = 0.0
        
        # Ensure current stage is correct
        if pipeline.get("current_stage") != "enrichment":
            warnings.append(f"Current stage is {pipeline.get('current_stage')}, expected 'enrichment'")

        # Determine if validation passed
        passed = len(failures) == 0 and quality_score >= 0.6
        dlq_queue = None if passed else self.dlq_queues[ValidationStage.ENRICHMENT]

        return ValidationResult(
            passed=passed,
            stage=ValidationStage.ENRICHMENT,
            failures=failures,
            warnings=warnings,
            quality_score=max(0.0, quality_score),
            should_retry=False,
            dlq_queue=dlq_queue,
        )

    async def validate_categorization(self, document: Dict) -> ValidationResult:
        """
        Validate document after categorization stage

        Quality gates:
        - Must have data from previous stages (extraction)
        - Must have valid category assignment
        - Must have primary, secondary, and tertiary categories
        - Must have company name and code
        - Must have quality_score >= 0.6
        - Must have subcategory for non-general categories
        """
        failures = []
        warnings = []
        quality_score = 1.0

        # CRITICAL: Check for required data from previous stages
        # Stage 1: Extraction - must have content (support both new and old schema)
        extraction_data = document.get("extraction", {})
        
        # NEW SCHEMA: Check extraction.content (unified field) or fallback to old text/markdown
        if extraction_data:
            # Try new unified content field first, then fallback to old fields
            extracted_text = extraction_data.get("content", "") or extraction_data.get("text", "")
            extracted_markdown = extraction_data.get("content", "") or extraction_data.get("markdown", "")
            # Use the better quality extraction if available
            primary_content = extracted_markdown if extracted_markdown else extracted_text
            has_new_content = bool(primary_content.strip())
        else:
            has_new_content = False
        
        # OLD SCHEMA: Check extracted_text and extracted_markdown at root level
        has_old_content = bool(document.get("content", "").strip() or document.get("extracted_text", "").strip())
        
        if not has_new_content and not has_old_content:
            failures.append("Missing content/extracted_text from extraction stage - document cannot skip extraction")
            quality_score -= 0.5
        
        # Must have metadata (support both new and old schema)
        has_metadata = bool(document.get("metadata")) or bool(document.get("discovery", {}).get("metadata"))
        if not has_metadata:
            failures.append("Missing metadata - required for document tracking")
            quality_score -= 0.3
        
        # Must have source information (support both new and old schema)
        discovery_source = document.get("discovery", {}).get("source", {})
        has_source = bool(document.get("source") or document.get("url") or discovery_source.get("url"))
        if not has_source:
            failures.append("Missing source/url information")
            quality_score -= 0.2

        # Check categorization data
        categorization = document.get("categorization", {})

        if not categorization:
            failures.append("Missing categorization data")
            quality_score -= 0.5
        else:
            # Check for unknown/failed categorization fields
            language = categorization.get("language", "")
            if language == "unknown" or not language:
                failures.append("Unknown or missing language detection")
                quality_score -= 0.4

            model_used = categorization.get("model_used", "")
            if model_used == "unknown" or not model_used:
                failures.append("Unknown or missing model_used field")
                quality_score -= 0.4

            # Validate title_english contains actual English text
            title_english = categorization.get("title_english", "")
            if title_english:
                # Check if title_english contains non-Latin characters (likely not English)
                import re

                has_cjk = bool(
                    re.search(r"[\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff]", title_english)
                )
                if has_cjk:
                    failures.append("title_english contains non-English characters")
                    quality_score -= 0.3

            # Validate key_indicators are meaningful and in English
            key_indicators = categorization.get("key_indicators", [])
            if key_indicators and isinstance(key_indicators, list):
                if len(key_indicators) == 0:
                    warnings.append("Empty key_indicators list - should contain meaningful phrases")
                    quality_score -= 0.1
                else:
                    for indicator in key_indicators:
                        if isinstance(indicator, str):
                            # Check for non-English characters
                            has_cjk = bool(
                                re.search(r"[\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff]", indicator)
                            )
                            if has_cjk:
                                failures.append("key_indicators contain non-English text")
                                quality_score -= 0.3
                                break
                            # Check for generic/meaningless indicators
                            if indicator.lower() in [
                                "unknown",
                                "n/a",
                                "not applicable",
                                "none",
                                "null",
                            ]:
                                warnings.append("key_indicators contain generic/meaningless terms")
                                quality_score -= 0.2
                                break

            # Check categorization results for primary/secondary categories
            # Note: Using actual field names from categorization service output

            # Check primary category
            primary_category = categorization.get("primary_category")
            if not primary_category or primary_category == "null":
                failures.append("Missing or null primary category from categorization")
                quality_score -= 0.4

            # Check secondary category
            secondary_category = categorization.get("secondary_category")
            if not secondary_category or secondary_category == "null":
                failures.append("Missing or null secondary category from categorization")
                quality_score -= 0.3

            # Tertiary category is optional in single-queue architecture
            # No longer required since we simplified to primary/secondary only

            # Check basic category (fallback to primary_category for backward compatibility)
            category = categorization.get("category") or primary_category
            if not category or category == "null":
                failures.append("Missing or null category")
                quality_score -= 0.4
            elif category == "other":
                failures.append("Document categorized as 'other' - invalid category")
                quality_score -= 0.5

            # Check confidence score (actual field name used by categorization service)
            cat_quality_score = categorization.get("confidence", 0)
            if cat_quality_score < self.min_categorization_confidence:
                failures.append(
                    f"Low categorization confidence score: {cat_quality_score} (min {self.min_categorization_confidence})"
                )
                quality_score -= 0.5

            # Check subcategory for specific categories (use secondary_category)
            subcategory = categorization.get("secondary_category", "")
            if category in ["earnings", "dividend", "buyback", "m&a"] and not subcategory:
                warnings.append(f"Missing subcategory for {category} document")
                quality_score -= 0.1

            # Check priority assignment
            priority = categorization.get("priority", 0)
            if priority < 1:
                warnings.append("Low priority assignment - verify importance")
                quality_score -= 0.1

            # Validate reasoning field contains actual reasoning
            reasoning = categorization.get("reasoning", "")
            if reasoning:
                reasoning_lower = (reasoning or "").lower()
                if (
                    reasoning_lower in ["unknown", "n/a", "not available", "none", "null"]
                    or len(reasoning or "") < 20
                ):
                    warnings.append("Categorization reasoning is too short or generic")
                    quality_score -= 0.2
            else:
                warnings.append("Missing categorization reasoning")
                quality_score -= 0.1

        # Check company name and code based on document category
        category = categorization.get("category", "").lower()
        primary_category = categorization.get("primary_category", "").lower()

        # Categories that require company information
        corporate_categories = [
            "corporate_filing",
            "regulatory_disclosure",
            "debt_credit",
            "earnings",
        ]
        requires_company = (
            category in corporate_categories or primary_category in corporate_categories
        )

        # Categories where company info is optional (ETFs, funds, etc.)
        optional_company_categories = [
            "fund_investment",
            "etf_data",
            "economic_data",
            "central_bank",
            "geopolitical",
        ]
        company_optional = (
            category in optional_company_categories
            or primary_category in optional_company_categories
        )

        if requires_company:
            # Multi-source company information extraction approach
            # Check multiple sources for company info: document root, categorization, metadata
            company_name = None
            company_code = None

            # Source 1: Document root level
            if document.get("company_name"):
                company_name = (document.get("company_name") or "").strip()
            if document.get("company_code"):
                company_code = document.get("company_code")

            # Source 2: Enhanced categorization result (new capability)
            if categorization:
                if categorization.get("company_name") and not company_name:
                    company_name = (categorization.get("company_name") or "").strip()
                if categorization.get("company_code") and not company_code:
                    company_code = categorization.get("company_code")

            # Source 3: Document metadata (old schema)
            metadata = document.get("metadata", {})
            if metadata:
                if metadata.get("company_name") and not company_name:
                    company_name = (metadata.get("company_name") or "").strip()
                if metadata.get("raw_company_code") and not company_code:
                    company_code = metadata.get("raw_company_code")
            
            # Source 4: Discovery metadata company (new schema)
            discovery_company = document.get("discovery", {}).get("metadata", {}).get("company", {})
            if discovery_company:
                if discovery_company.get("name") and not company_name:
                    company_name = (discovery_company.get("name") or "").strip()
                if discovery_company.get("code") and not company_code:
                    company_code = discovery_company.get("code")

            # RELAXED VALIDATION: Use warnings instead of failures
            # Allow documents to proceed without company info for pro-analyzer to handle
            if not company_name:
                warnings.append("Missing company name - will attempt extraction in pro-analyzer")
                quality_score -= 0.2  # Reduced penalty from 0.4

            if not company_code:
                warnings.append("Missing company code - will attempt extraction in pro-analyzer")
                quality_score -= 0.2  # Reduced penalty from 0.5

            # Update document with consolidated company info if found
            if company_name:
                document["company_name"] = company_name
            if company_code:
                document["company_code"] = company_code

        elif company_optional:
            # For ETFs/funds, company info is optional but should be consistent if present
            company_code = document.get("company_code")
            company_name = document.get("company_name")
            if company_code and not company_name:
                warnings.append("Company code present but missing company name")
                quality_score -= 0.1
            elif company_name and not company_code:
                warnings.append("Company name present but missing company code")
                quality_score -= 0.1
        else:
            # For other document types (central bank, economic data), company fields should not be present
            if document.get("company_code") or document.get("company_name"):
                warnings.append("Company information present for non-corporate document type")
                quality_score -= 0.1

        passed = len(failures) == 0 and quality_score >= self.min_quality_score
        dlq_queue = None if passed else self.dlq_queues[ValidationStage.CATEGORIZATION]

        return ValidationResult(
            passed=passed,
            stage=ValidationStage.CATEGORIZATION,
            failures=failures,
            warnings=warnings,
            quality_score=max(0.0, quality_score),
            should_retry=False,  # Retries disabled
            dlq_queue=dlq_queue,
        )

    async def validate_analysis(self, document: Dict) -> ValidationResult:
        """
        Validate document after analysis stage

        Quality gates:
        - Must have data from previous stages (extraction, categorization)
        - Must have pro_analysis data structure
        - Must have analysis content >= 150 characters
        - Must have required analysis fields for category
        - Must have confidence >= 0.6
        """
        failures = []
        warnings = []
        quality_score = 1.0
        
        # CRITICAL: Check for required data from previous stages
        # Stage 1: Extraction - must have content
        if not document.get("content") and not document.get("extracted_text"):
            failures.append("Missing content/extracted_text from extraction stage - document cannot skip extraction")
            quality_score -= 0.5
        
        # Stage 2: Categorization - must have categorization
        if not document.get("categorization"):
            failures.append("Missing categorization from categorization stage - document cannot skip categorization")
            quality_score -= 0.5
        
        # Must have metadata
        if not document.get("metadata"):
            failures.append("Missing metadata - required for document tracking")
            quality_score -= 0.3
        
        try:
            # Check that pro_analysis exists (critical requirement)
            pro_analysis = document.get("pro_analysis", {})
            if not pro_analysis:
                failures.append(
                    "Missing pro_analysis data - document should not proceed without analysis"
                )
                quality_score -= 0.6

            # Check analysis content length - support both analysis and pro_analysis structures
            analysis_content = document.get("analysis", {})

            # Get summary from either structure
            summary = ""
            confidence = 0

            if isinstance(analysis_content, dict) and analysis_content.get("summary"):
                summary = analysis_content.get("summary") or ""
                confidence = analysis_content.get("confidence", 0)
            elif isinstance(pro_analysis, dict):
                # Check for nested content structure first (new format)
                if isinstance(pro_analysis.get("content"), dict):
                    content = pro_analysis.get("content", {})
                    # Try structured_response first, then response
                    if isinstance(content.get("structured_response"), dict):
                        structured = content.get("structured_response", {})
                        # Try various summary field names (with and without spaces/underscores)
                        summary = (structured.get("Executive Summary") or 
                                 structured.get("Executive_Summary") or 
                                 structured.get("Investment_Thesis") or 
                                 structured.get("Investment Thesis") or
                                 structured.get("executive_summary") or
                                 structured.get("summary") or "")
                        # Debug logging
                        print(f"DEBUG: Found structured_response with keys: {list(structured.keys())}")
                        print(f"DEBUG: Executive Summary content: {structured.get('Executive Summary', 'NOT_FOUND')[:100]}")
                        print(f"DEBUG: Final summary length: {len(summary)}")
                    elif isinstance(content.get("response"), str):
                        summary = content.get("response") or ""
                    confidence = pro_analysis.get("confidence", 0.8)
                # Legacy structure: either .response or .analysis.response
                elif isinstance(pro_analysis.get("response"), str):
                    summary = pro_analysis.get("response") or ""
                    confidence = pro_analysis.get("confidence", 0.8)
                elif isinstance(pro_analysis.get("analysis"), dict):
                    summary = pro_analysis.get("analysis", {}).get("response") or ""
                    confidence = pro_analysis.get("confidence", 0.8)

            if len(summary or "") < self.min_analysis_length:
                failures.append(
                    f"Analysis summary too short: {len(summary or '')} chars (min {self.min_analysis_length})"
                )
                quality_score -= 0.4

            # Check confidence
            if confidence < self.min_analysis_confidence:
                failures.append(
                    f"Low analysis confidence: {confidence} (min {self.min_analysis_confidence})"
                )
                quality_score -= 0.3

            # Validate pro_analysis structure if it exists
            if pro_analysis and isinstance(pro_analysis, dict):
                # Check for nested content structure (new format)
                content = pro_analysis.get("content", {})
                
                # Check that pro_analysis has meaningful content
                has_content = (
                    pro_analysis.get("response") or 
                    pro_analysis.get("analysis") or 
                    (content and (content.get("response") or content.get("structured_response")))
                )
                if not has_content:
                    failures.append("Pro_analysis exists but lacks response or analysis content")
                    quality_score -= 0.4

                # Check for required pro_analysis fields (check both top-level and nested in content)
                model_used = pro_analysis.get("model_used") or content.get("model_used")
                if not model_used:
                    failures.append("Pro_analysis missing model_used field")
                    quality_score -= 0.2

                provider = pro_analysis.get("provider") or content.get("provider")
                if not provider:
                    failures.append("Pro_analysis missing provider field")
                    quality_score -= 0.2

                # Check for timestamp to ensure it was recently processed
                if not pro_analysis.get("timestamp") and not pro_analysis.get("analyzed_at"):
                    warnings.append("Pro_analysis missing timestamp - may be stale data")
                    quality_score -= 0.1

                # Check for content quality - avoid generic/placeholder responses
                response_content = (
                    pro_analysis.get("response", "") or 
                    content.get("response", "") or
                    str(content.get("structured_response", {}))
                )
                if response_content:
                    response_lower = (response_content or "").lower()
                    if any(
                        phrase in response_lower
                        for phrase in [
                            "unable to analyze",
                            "insufficient information",
                            "cannot determine",
                            "not enough data",
                        ]
                    ):
                        warnings.append("Pro_analysis contains generic/placeholder content")
                        quality_score -= 0.3

            # For pro_analysis, we're more lenient on specific fields since it's a comprehensive analysis
            # Check trading signal validity if present in either structure
            trading_signal = ""
            if isinstance(analysis_content, dict):
                trading_signal = analysis_content.get("trading_signal", "")

            # Pro analysis doesn't require specific structured fields since it's comprehensive text
            valid_signals = ["STRONG_BUY", "BUY", "HOLD", "SELL", "STRONG_SELL"]
            if trading_signal and trading_signal not in valid_signals:
                warnings.append(f"Non-standard trading signal: {trading_signal}")
                quality_score -= 0.1

            passed = len(failures) == 0 and quality_score >= self.min_quality_score
            dlq_queue = None if passed else self.dlq_queues[ValidationStage.ANALYSIS]

            return ValidationResult(
                passed=passed,
                stage=ValidationStage.ANALYSIS,
                failures=failures,
                warnings=warnings,
                quality_score=max(0.0, quality_score),
                should_retry=False,  # Retries disabled
                dlq_queue=dlq_queue,
            )
        except Exception as e:
            import traceback
            logger.error(f"❌ Error in validate_analysis: {e}")
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            return ValidationResult(
                passed=False,
                stage=ValidationStage.ANALYSIS,
                failures=[f"Validation error: {str(e)}"],
                warnings=[],
                quality_score=0.0,
                should_retry=False,
                dlq_queue=self.dlq_queues[ValidationStage.ANALYSIS],
            )

    async def validate_market_analysis(self, document: Dict) -> ValidationResult:
        """
        Validate document after market analysis stage

        Quality gates:
        - Must have data from ALL previous stages (extraction, categorization, analysis)
        - Must have valid pro_analysis (prerequisite)
        - Must have market_analysis data structure
        - Must have market predictions
        - Must have impact assessment
        """
        failures = []
        warnings = []
        quality_score = 1.0

        # CRITICAL: Check for required data from ALL previous stages
        # Stage 1: Extraction - must have content
        if not document.get("content") and not document.get("extracted_text"):
            failures.append("Missing content/extracted_text from extraction stage - document cannot skip extraction")
            quality_score -= 0.5
        
        # Stage 2: Categorization - must have categorization
        if not document.get("categorization"):
            failures.append("Missing categorization from categorization stage - document cannot skip categorization")
            quality_score -= 0.5
        
        # Must have metadata
        if not document.get("metadata"):
            failures.append("Missing metadata - required for document tracking")
            quality_score -= 0.3

        # CRITICAL: Check that document has pro_analysis first (prerequisite for market analysis)
        pro_analysis = document.get("pro_analysis", {})
        if not pro_analysis:
            failures.append(
                "Missing pro_analysis - document should not reach market analysis without it"
            )
            quality_score -= 0.6
        elif isinstance(pro_analysis, dict):
            # Validate pro_analysis has required content
            # Check for nested content structure first (new format)
            content = pro_analysis.get("content", {})
            if content and isinstance(content, dict):
                # For new format, get the full response content
                response_content = content.get("response") or ""
                # If no response but we have structured_response, combine the structured content
                if not response_content and isinstance(content.get("structured_response"), dict):
                    structured = content.get("structured_response", {})
                    # Combine major fields to get full content length
                    response_content = " ".join([
                        structured.get("Executive_Summary", ""),
                        structured.get("Investment_Thesis", ""),
                        " ".join(structured.get("Key_Financial_Insights", [])),
                        " ".join(structured.get("Risk_Factors", [])),
                    ]).strip()
            else:
                # Legacy format
                response_content = pro_analysis.get("response") or str(
                    pro_analysis.get("analysis", {}).get("response") or ""
                )
            if len(response_content or "") < 500:
                failures.append(
                    f"Pro_analysis response too short: {len(response_content or '')} chars (min 500)"
                )
                quality_score -= 0.3

        # Check market analysis content (modern MarketAnalysisResponse format)
        market_analysis = document.get("market_analysis", {})

        # CRITICAL: Check if market_analysis is a string (malformed JSON)
        if isinstance(market_analysis, str):
            failures.append(
                "CRITICAL: market_analysis is a string instead of a dictionary - malformed JSON structure"
            )
            quality_score -= 0.8  # Severe penalty for malformed structure
            # Try to check if it contains markdown formatting
            if market_analysis.startswith("#") or "```" in market_analysis:
                failures.append(
                    "market_analysis contains markdown formatting instead of structured JSON"
                )
            return ValidationResult(
                passed=False,
                failures=failures,
                warnings=warnings,
                quality_score=0.0,  # Force failure
                should_retry=False,
                dlq_queue="market_analysis_dlq",
            )

        if isinstance(market_analysis, dict):
            # Check executive summary and market narrative (modern fields)
            executive_summary = market_analysis.get("executive_summary") or ""
            market_narrative = market_analysis.get("market_narrative") or ""

            # Combine both fields for total content length check
            total_analysis_content = f"{executive_summary} {market_narrative}".strip()
            if len(total_analysis_content) < self.min_market_analysis_length:
                failures.append(
                    f"Market analysis content too short: {len(total_analysis_content)} chars (min {self.min_market_analysis_length})"
                )
                quality_score -= 0.4

            # Check modern primary impact structure
            primary_impact = market_analysis.get("primary_impact", {})
            if not primary_impact:
                failures.append("Missing primary market impact prediction")
                quality_score -= 0.3
            else:
                # Check required prediction fields in modern structure
                if "direction" not in primary_impact:
                    failures.append("Missing impact direction in primary_impact")
                    quality_score -= 0.2
                if "magnitude_percent" not in primary_impact:
                    failures.append("Missing magnitude_percent in primary_impact")
                    quality_score -= 0.2
                if "confidence" not in primary_impact:
                    warnings.append("Missing confidence score in primary_impact")
                    quality_score -= 0.1

            # Check trading recommendation (should always be present)
            trading_recommendation = market_analysis.get("trading_recommendation")
            if not trading_recommendation:
                warnings.append("Missing trading recommendation")
                quality_score -= 0.1
            elif trading_recommendation not in ["buy", "sell", "hold", "avoid"]:
                warnings.append(f"Invalid trading recommendation: {trading_recommendation}")
                quality_score -= 0.1
        else:
            failures.append("Market analysis content is not in expected format")
            quality_score -= 0.5

        passed = len(failures) == 0 and quality_score >= self.min_quality_score
        dlq_queue = None if passed else self.dlq_queues[ValidationStage.MARKET_ANALYSIS]

        return ValidationResult(
            passed=passed,
            stage=ValidationStage.MARKET_ANALYSIS,
            failures=failures,
            warnings=warnings,
            quality_score=max(0.0, quality_score),
            should_retry=False,  # Retries disabled
            dlq_queue=dlq_queue,
        )

    async def validate_trading_signals(self, document: Dict) -> ValidationResult:
        """
        Validate document after trading signals generation

        Quality gates:
        - Must have data from ALL previous stages (extraction, categorization, analysis, market analysis)
        - Must have valid trading signals data structure
        - Must have signal metadata with confidence ≥ 0.6
        - Must have risk assessment
        - Must have complete signal validation
        """
        failures = []
        warnings = []
        quality_score = 1.0

        # CRITICAL: Check for required data from ALL previous stages
        # Stage 1: Extraction - must have content
        if not document.get("content") and not document.get("extracted_text"):
            failures.append("Missing content/extracted_text from extraction stage - document cannot skip extraction")
            quality_score -= 0.5
        
        # Stage 2: Categorization - must have categorization
        if not document.get("categorization"):
            failures.append("Missing categorization from categorization stage - document cannot skip categorization")
            quality_score -= 0.5
        
        # Stage 3: Analysis - must have pro_analysis
        if not document.get("pro_analysis"):
            failures.append("Missing pro_analysis from analysis stage - document cannot skip analysis")
            quality_score -= 0.5
        
        # Stage 4: Market Analysis - must have market_analysis
        if not document.get("market_analysis"):
            failures.append("Missing market_analysis from market analysis stage - document cannot skip market analysis")
            quality_score -= 0.5
        
        # Must have metadata
        if not document.get("metadata"):
            failures.append("Missing metadata - required for document tracking")
            quality_score -= 0.3

        # Check that trading signals exist OR there's a valid no_signals_reason
        trading_signals = document.get("trading_signals", {})
        no_signals_reason = document.get("no_signals_reason")
        
        if not trading_signals and not no_signals_reason:
            failures.append(
                "Missing trading_signals data - document should not proceed without signals"
            )
            quality_score -= 0.6
        elif isinstance(trading_signals, dict):
            # Check signal confidence
            confidence = trading_signals.get("confidence", 0)
            if confidence < self.min_trading_confidence:
                failures.append(
                    f"Low trading signal confidence: {confidence} (min {self.min_trading_confidence})"
                )
                quality_score -= 0.3

            # Check signal format and required fields
            signal_type = trading_signals.get("signal_type", "")
            if not signal_type:
                failures.append("Missing signal_type in trading signals")
                quality_score -= 0.2

            direction = trading_signals.get("direction", "")
            if direction not in ["BUY", "SELL", "HOLD"]:
                failures.append(f"Invalid or missing signal direction: {direction}")
                quality_score -= 0.3

            # Check risk assessment
            risk_assessment = trading_signals.get("risk_assessment", {})
            if not risk_assessment:
                failures.append("Missing risk assessment in trading signals")
                quality_score -= 0.3
            elif isinstance(risk_assessment, dict):
                risk_level = risk_assessment.get("risk_level", "")
                if not risk_level:
                    warnings.append("Missing risk_level in risk assessment")
                    quality_score -= 0.1
                elif risk_level not in ["low", "medium", "high", "extreme"]:
                    warnings.append(f"Invalid risk_level: {risk_level}")
                    quality_score -= 0.1

            # Check signal metadata completeness
            if not trading_signals.get("generated_at"):
                warnings.append("Missing generated_at timestamp in trading signals")
                quality_score -= 0.1

            if not trading_signals.get("model_used"):
                warnings.append("Missing model_used in trading signals metadata")
                quality_score -= 0.1

            # Check for essential trading fields
            if direction in ["BUY", "SELL"]:
                # For actionable signals, check price guidance
                entry_points = trading_signals.get("entry_points", [])
                if not entry_points or (isinstance(entry_points, list) and len(entry_points) == 0):
                    warnings.append("Missing entry_points for actionable trading signal")
                    quality_score -= 0.2

                stop_loss = trading_signals.get("stop_loss")
                if not stop_loss and direction in ["BUY", "SELL"]:
                    warnings.append("Missing stop_loss for risk management")
                    quality_score -= 0.2

        # Check that all previous validation stages passed using new pipeline system
        pipeline = document.get("pipeline", {})
        validation_gates_passed = pipeline.get("validation_gates_passed", [])
        
        required_gates = ["extraction", "categorization", "analysis", "market_analysis"]
        for gate in required_gates:
            if gate not in validation_gates_passed:
                failures.append(
                    f"Missing validation gate: {gate} - signals generated on unvalidated data"
                )
                quality_score -= 0.2

        passed = len(failures) == 0 and quality_score >= self.min_quality_score
        dlq_queue = None if passed else self.dlq_queues[ValidationStage.TRADING_SIGNALS]

        return ValidationResult(
            passed=passed,
            stage=ValidationStage.TRADING_SIGNALS,
            failures=failures,
            warnings=warnings,
            quality_score=max(0.0, quality_score),
            should_retry=False,  # Retries disabled
            dlq_queue=dlq_queue,
        )

    async def validate_storage(self, document: Dict) -> ValidationResult:
        """
        Validate document before final storage

        Quality gates:
        - Must have data from ALL previous stages (complete pipeline)
        - Must have all previous validation flags
        - Must have complete processing history
        - Must have market analysis results
        - Must have all required metadata
        - No critical data fields can be null/empty
        """
        failures = []
        warnings = []
        quality_score = 1.0

        # CRITICAL: Check for required data from ALL previous stages
        # Stage 1: Extraction - must have content
        if not document.get("content") and not document.get("extracted_text"):
            failures.append("Missing content/extracted_text from extraction stage")
            quality_score -= 0.5
        
        # Stage 2: Categorization - must have categorization
        if not document.get("categorization"):
            failures.append("Missing categorization from categorization stage")
            quality_score -= 0.5
        
        # Stage 3: Analysis - must have pro_analysis
        if not document.get("pro_analysis"):
            failures.append("Missing pro_analysis from analysis stage")
            quality_score -= 0.5
        
        # Stage 4: Market Analysis - must have market_analysis
        if not document.get("market_analysis"):
            failures.append("Missing market_analysis from market analysis stage")
            quality_score -= 0.5
        
        # Stage 5: Trading Signals - must have trading_signals, no_signals_reason, or trading_signals_processed flag
        has_trading_signals = document.get("trading_signals")
        has_no_signals_reason = document.get("no_signals_reason")
        has_processed_flag = document.get("trading_signals_processed")
        
        if not has_trading_signals and not has_no_signals_reason and not has_processed_flag:
            failures.append("Missing trading_signals/no_signals_reason/trading_signals_processed from trading signals stage")
            quality_score -= 0.5
        
        # Must have metadata
        if not document.get("metadata"):
            failures.append("Missing metadata - required for document tracking")
            quality_score -= 0.3

        # Check all previous validation gates using new pipeline system
        pipeline = document.get("pipeline", {})
        validation_gates_passed = pipeline.get("validation_gates_passed", [])
        
        required_gates = ["discovery", "extraction", "categorization", "analysis", "market_analysis", "trading_signals"]
        for gate in required_gates:
            if gate not in validation_gates_passed:
                failures.append(f"Missing validation gate: {gate}")
                quality_score -= 0.2

        # Check core document fields based on document type
        categorization = document.get("categorization", {})
        primary_category = categorization.get("primary_category", "")
        
        # Base fields required for all documents
        base_fields = ["id", "title", "url", "published_at"]
        for field in base_fields:
            value = (
                document.get(field, "").strip()
                if isinstance(document.get(field), str)
                else document.get(field)
            )
            if not value:
                failures.append(f"Missing or empty core field: {field}")
                quality_score -= 0.15
        
        # Category-specific validation
        if primary_category == "corporate_filing":
            # Corporate filings must have company information
            company_fields = ["company_name", "company_code"]
            for field in company_fields:
                # Check main document fields or metadata
                value = document.get(field) or document.get("metadata", {}).get(field)
                if not value:
                    failures.append(f"Missing or empty company field for corporate filing: {field}")
                    quality_score -= 0.15
        elif primary_category == "central_bank":
            # Central bank documents should have institution info
            institution = (document.get("institution_name") or 
                         document.get("metadata", {}).get("source") or
                         document.get("source", {}).get("source_type"))
            if not institution:
                warnings.append("Central bank document missing institution identifier")
                quality_score -= 0.05
        elif primary_category == "government":
            # Government documents should have agency info
            agency = (document.get("agency_name") or 
                     document.get("metadata", {}).get("source"))
            if not agency:
                warnings.append("Government document missing agency identifier")
                quality_score -= 0.05

        # Check processing history
        if not document.get("extracted_text"):
            failures.append("Missing extracted text content")
            quality_score -= 0.3

        if not document.get("categorization"):
            failures.append("Missing categorization data")
            quality_score -= 0.2

        if not document.get("pro_analysis"):
            failures.append("Missing pro analysis data")
            quality_score -= 0.2

        if not document.get("market_analysis"):
            failures.append("Missing market analysis data")
            quality_score -= 0.2

        # Check timestamps
        timestamp_fields = ["created_at", "extracted_at", "categorized_at", "analyzed_at"]
        for field in timestamp_fields:
            if field in document and not document.get(field):
                warnings.append(f"Missing timestamp: {field}")
                quality_score -= 0.05

        # Check data completeness
        if document.get("extraction_method") == "error":
            failures.append("Document has extraction errors")
            quality_score -= 0.3

        # Check for any "null" string values (common data issue)
        for key, value in document.items():
            if isinstance(value, str) and value.lower() == "null":
                warnings.append(f"Field '{key}' contains string 'null' instead of proper value")
                quality_score -= 0.1

        # Verify no temporary processing fields remain
        temp_fields = ["retry_count", "retry_after", "processing_error", "temp_data"]
        for field in temp_fields:
            if field in document:
                warnings.append(f"Temporary field '{field}' should be removed before storage")
                quality_score -= 0.05

        passed = len(failures) == 0 and quality_score >= self.min_quality_score
        dlq_queue = None if passed else self.dlq_queues[ValidationStage.STORAGE]

        return ValidationResult(
            passed=passed,
            stage=ValidationStage.STORAGE,
            failures=failures,
            warnings=warnings,
            quality_score=max(0.0, quality_score),
            should_retry=False,  # Storage validation failures should not retry
            dlq_queue=dlq_queue,
        )

    async def route_to_dlq(self, document: Dict, validation_result: ValidationResult) -> bool:
        """
        Route failed document to appropriate DLQ with failure metadata

        Args:
            document: The document that failed validation
            validation_result: The validation result with failure details

        Returns:
            bool: True if successfully routed to DLQ
        """
        try:
            # Add validation failure metadata with detailed context
            document["validation_failure"] = {
                "stage": validation_result.stage.value,
                "failures": validation_result.failures,
                "warnings": validation_result.warnings,
                "quality_score": validation_result.quality_score,
                "should_retry": validation_result.should_retry,
                "failed_at": datetime.utcnow().isoformat(),
                "retry_count": document.get("retry_count", 0),
                "document_context": {
                    "document_id": document.get("id", "unknown"),
                    "category": document.get("categorization", {}).get("category", "unknown"),
                    "company_code": document.get("company_code", "N/A"),
                    "content_length": len(str(document.get("extracted_text") or "")),
                    "source_url": document.get("url", "unknown"),
                },
            }

            # Route to appropriate DLQ
            dlq_queue = validation_result.dlq_queue
            if not dlq_queue:
                logger.error(f"❌ DLQ queue is None for failed document {document.get('id', 'unknown')} at stage {validation_result.stage.value}")
                return False
            await self.redis_client.lpush(dlq_queue, json.dumps(document))

            logger.warning(
                f"📤 Document {document.get('id', 'unknown')} routed to {dlq_queue}. "
                f"Failures: {', '.join(validation_result.failures)}"
            )

            return True

        except Exception as e:
            import traceback
            logger.error(f"❌ Failed to route document to DLQ: {e}")
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            return False

    async def route_to_categorization_retry(self, document: Dict, reason: str) -> bool:
        """
        Route document back to categorization step for retry

        Args:
            document: The document that needs recategorization
            reason: Reason for sending back to categorization

        Returns:
            bool: True if successfully routed back
        """
        try:
            # Clean up incomplete categorization data
            if "categorization" in document:
                del document["categorization"]
            if "pro_analysis" in document:
                del document["pro_analysis"]

            # Add retry metadata
            document["retry_info"] = {
                "reason": reason,
                "retry_at": datetime.utcnow().isoformat(),
                "retry_count": document.get("retry_count", 0) + 1,
                "original_stage": "categorization",
            }

            # Route back to preprocessing queue for categorization
            await self.redis_client.lpush("preprocessing_queue", json.dumps(document))

            logger.info(
                f"🔄 Document {document.get('id', 'unknown')} routed back to categorization. "
                f"Reason: {reason}"
            )

            return True

        except Exception as e:
            logger.error(f"❌ Failed to route document back to categorization: {e}")
            return False

    async def validate_and_route(
        self, document: Dict, stage: ValidationStage, source_queue: str, target_queue: str
    ) -> bool:
        """
        Validate document at given stage and route to target queue or DLQ

        Args:
            document: Document to validate
            stage: Validation stage
            source_queue: Source queue name
            target_queue: Target queue if validation passes

        Returns:
            bool: True if document passed validation and was routed to target
        """
        try:
            # Normalize company information before validation (fixes EDINET missing company data)
            document = normalize_document_company_info(document)
            
            # Perform validation based on stage
            if stage == ValidationStage.DISCOVERY:
                result = await self.validate_discovery(document)
            elif stage == ValidationStage.EXTRACTION:
                result = await self.validate_extraction(document)
            elif stage == ValidationStage.ENRICHMENT:
                result = await self.validate_enrichment(document)
            elif stage == ValidationStage.CATEGORIZATION:
                result = await self.validate_categorization(document)
            elif stage == ValidationStage.ANALYSIS:
                result = await self.validate_analysis(document)
            elif stage == ValidationStage.MARKET_ANALYSIS:
                result = await self.validate_market_analysis(document)
            elif stage == ValidationStage.TRADING_SIGNALS:
                result = await self.validate_trading_signals(document)
            elif stage == ValidationStage.STORAGE:
                result = await self.validate_storage(document)
            else:
                logger.error(f"Unknown validation stage: {stage}")
                return False

            if result.passed:
                # Use new pipeline validation system instead of old l2_valid flags
                pipeline = document.get("pipeline", {})
                validation_gates_passed = pipeline.get("validation_gates_passed", [])
                
                # Add the stage to validation gates if not already present
                stage_name = stage.value  # "discovery", "extraction", etc.
                if stage_name not in validation_gates_passed:
                    validation_gates_passed.append(stage_name)
                
                # Update pipeline metadata
                if "pipeline" not in document:
                    document["pipeline"] = {}
                document["pipeline"]["validation_gates_passed"] = validation_gates_passed
                
                # Update processing timestamps
                if "processing_timestamps" not in document["pipeline"]:
                    document["pipeline"]["processing_timestamps"] = {}
                document["pipeline"]["processing_timestamps"][f"{stage_name}_validation"] = datetime.utcnow().isoformat()
                
                # Store quality scores
                if "validation_scores" not in document["pipeline"]:
                    document["pipeline"]["validation_scores"] = {}
                document["pipeline"]["validation_scores"][stage_name] = result.quality_score

                # Validation passed - route to target queue
                # Pre-push JSON validation to ensure document can be serialized
                try:
                    serialized_doc = json.dumps(document)
                    # Try to parse it back to ensure it's valid JSON
                    json.loads(serialized_doc)
                except (TypeError, ValueError, json.JSONDecodeError) as e:
                    logger.error(
                        f"❌ Document failed JSON serialization after passing validation: {e}"
                    )
                    logger.error(f"Document id: {document.get('id', 'unknown')}")
                    # Route to special malformed JSON DLQ
                    malformed_result = ValidationResult(
                        passed=False,
                        failures=[f"JSON serialization failed: {str(e)}"],
                        warnings=[],
                        quality_score=0.0,
                        should_retry=False,
                        dlq_queue="malformed_json_dlq",
                    )
                    await self.route_to_dlq(document, malformed_result)
                    return False

                await self.redis_client.lpush(target_queue, serialized_doc)
                logger.info(
                    f"✅ Document {document.get('id', 'unknown')} passed {stage.value} validation "
                    f"(score: {result.quality_score:.2f}) → {target_queue}"
                )
                
                # Log validation event
                self._log_validation_event(document.get('id', 'unknown'), stage.value, True, result)
                
                return True
            else:
                # Validation failed - NO RETRIES, send directly to DLQ
                await self.route_to_dlq(document, result)
                
                # Log validation event
                self._log_validation_event(document.get('id', 'unknown'), stage.value, False, result)
                
                return False

        except Exception as e:
            logger.error(f"❌ Validation error for {stage.value}: {e}")
            return False
    
    def _log_validation_event(self, document_id: str, stage: str, passed: bool, result: ValidationResult):
        """Log validation events for monitoring"""
        try:
            # Import from service module to access global validation_logs
            from services.pipeline_validator.service import validation_logs, MAX_VALIDATION_LOGS
            
            event = {
                'timestamp': datetime.utcnow().isoformat(),
                'document_id': document_id,
                'stage': stage,
                'passed': passed,
                'quality_score': result.quality_score,
                'failures': result.failures,
                'warnings': result.warnings,
                'dlq_queue': result.dlq_queue if not passed else None
            }
            
            validation_logs.append(event)
            
            # Keep only last MAX_VALIDATION_LOGS events
            if len(validation_logs) > MAX_VALIDATION_LOGS:
                validation_logs.pop(0)
                
        except Exception as e:
            logger.error(f"Failed to log validation event: {e}")
