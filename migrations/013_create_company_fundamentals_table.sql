-- Create company market data table for real-time fundamental data from IBKR
-- This table stores comprehensive financial metrics and ratios from market data providers

CREATE TABLE IF NOT EXISTS company_market_data (
    id SERIAL PRIMARY KEY,
    country VARCHAR(10) NOT NULL DEFAULT 'JP',
    code VARCHAR(20) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    
    -- Snapshot metadata
    snapshot_date DATE NOT NULL,
    data_source VARCHAR(20) NOT NULL DEFAULT 'ibkr',
    data_quality_score DECIMAL(3,2) DEFAULT 0.0,
    
    -- Basic market data
    current_price DECIMAL(15,4),
    currency VARCHAR(3) NOT NULL DEFAULT 'JPY',
    market_cap BIGINT,
    shares_outstanding BIGINT,
    
    -- Valuation ratios
    pe_ratio DECIMAL(8,2),
    pb_ratio DECIMAL(8,2),
    ps_ratio DECIMAL(8,2),
    ev_ebitda DECIMAL(8,2),
    price_to_free_cash_flow DECIMAL(8,2),
    
    -- Profitability ratios
    roe DECIMAL(8,4), -- Return on Equity
    roa DECIMAL(8,4), -- Return on Assets
    roic DECIMAL(8,4), -- Return on Invested Capital
    gross_margin DECIMAL(8,4),
    operating_margin DECIMAL(8,4),
    net_margin DECIMAL(8,4),
    
    -- Financial health ratios
    current_ratio DECIMAL(8,2),
    quick_ratio DECIMAL(8,2),
    debt_to_equity DECIMAL(8,2),
    debt_to_assets DECIMAL(8,4),
    interest_coverage DECIMAL(8,2),
    
    -- Growth metrics
    revenue_growth_1y DECIMAL(8,4),
    earnings_growth_1y DECIMAL(8,4),
    revenue_growth_3y_avg DECIMAL(8,4),
    earnings_growth_3y_avg DECIMAL(8,4),
    
    -- Dividend metrics
    dividend_yield DECIMAL(8,4),
    dividend_per_share DECIMAL(8,2),
    payout_ratio DECIMAL(8,4),
    
    -- Financial statement data (in millions of currency)
    revenue_ttm BIGINT, -- Trailing twelve months
    net_income_ttm BIGINT,
    ebitda_ttm BIGINT,
    free_cash_flow_ttm BIGINT,
    total_assets BIGINT,
    total_debt BIGINT,
    shareholders_equity BIGINT,
    book_value_per_share DECIMAL(10,2),
    
    -- Trading metrics
    avg_volume_30d BIGINT,
    beta DECIMAL(8,4),
    week_52_high DECIMAL(15,4),
    week_52_low DECIMAL(15,4),
    
    -- Company details from IBKR
    employees_count INTEGER,
    business_description TEXT,
    website_url TEXT,
    industry_classification TEXT,
    sector_classification TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(country, code, snapshot_date, data_source)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_company_market_data_code_date ON company_market_data(country, code, snapshot_date DESC);
CREATE INDEX IF NOT EXISTS idx_company_market_data_symbol ON company_market_data(symbol);
CREATE INDEX IF NOT EXISTS idx_company_market_data_source ON company_market_data(data_source);
CREATE INDEX IF NOT EXISTS idx_company_market_data_quality ON company_market_data(data_quality_score);
CREATE INDEX IF NOT EXISTS idx_company_market_data_sector ON company_market_data(sector_classification);

-- Create hypertable for time-series optimization (if TimescaleDB)
DO $$
BEGIN
    -- Try to create hypertable, ignore if TimescaleDB not available
    PERFORM create_hypertable('company_market_data', 'snapshot_date', 
                             chunk_time_interval => INTERVAL '1 month',
                             if_not_exists => TRUE);
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'TimescaleDB not available, using regular table';
END
$$;

-- Create a view for latest market data per company
CREATE OR REPLACE VIEW company_market_data_latest AS 
SELECT DISTINCT ON (country, code) 
    country, code, symbol, snapshot_date, data_source,
    current_price, currency, market_cap, pe_ratio, pb_ratio, ps_ratio,
    roe, roa, debt_to_equity, current_ratio, dividend_yield,
    revenue_ttm, net_income_ttm, free_cash_flow_ttm,
    data_quality_score, last_updated
FROM company_market_data 
ORDER BY country, code, snapshot_date DESC, last_updated DESC;

-- Create view for Japanese companies latest market data
CREATE OR REPLACE VIEW jp_company_market_data_latest AS
SELECT *
FROM company_market_data_latest
WHERE country = 'JP';

-- Create view for US companies latest market data  
CREATE OR REPLACE VIEW us_company_market_data_latest AS
SELECT *
FROM company_market_data_latest
WHERE country = 'US';

-- Add some sample data for testing
INSERT INTO company_market_data (
    country, code, symbol, snapshot_date, current_price, currency,
    pe_ratio, pb_ratio, roe, debt_to_equity, dividend_yield,
    revenue_ttm, net_income_ttm, data_quality_score
) VALUES 
(
    'JP', '7203', '7203.T', CURRENT_DATE, 3350.00, 'JPY',
    12.5, 1.2, 0.095, 0.25, 0.028,
    37154000, 2891000, 0.85
),
(
    'US', 'AAPL', 'AAPL', CURRENT_DATE, 195.50, 'USD',
    28.5, 7.8, 0.265, 0.45, 0.0145,
    *********, 99803000, 0.92
)
ON CONFLICT (country, code, snapshot_date, data_source) 
DO UPDATE SET
    current_price = EXCLUDED.current_price,
    pe_ratio = EXCLUDED.pe_ratio,
    pb_ratio = EXCLUDED.pb_ratio,
    roe = EXCLUDED.roe,
    debt_to_equity = EXCLUDED.debt_to_equity,
    dividend_yield = EXCLUDED.dividend_yield,
    revenue_ttm = EXCLUDED.revenue_ttm,
    net_income_ttm = EXCLUDED.net_income_ttm,
    data_quality_score = EXCLUDED.data_quality_score,
    last_updated = CURRENT_TIMESTAMP;