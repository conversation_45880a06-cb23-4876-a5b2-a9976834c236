-- ABOUTME: Database schema for user watchlists and market data caching
-- ABOUTME: Supports cross-device persistent watchlists with real-time market data

-- Main watchlists table
CREATE TABLE user_watchlists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL DEFAULT 'default_user',
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT user_watchlists_name_check CHECK (char_length(name) >= 1),
    CONSTRAINT user_watchlists_user_name_unique UNIQUE (user_id, name)
);

-- Watchlist items table  
CREATE TABLE watchlist_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    watchlist_id UUID NOT NULL REFERENCES user_watchlists(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    company_name VA<PERSON>HA<PERSON>(255),
    data_source VARCHAR(20) NOT NULL DEFAULT 'yahoo',
    added_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    sort_order INTEGER DEFAULT 0,
    
    CONSTRAINT watchlist_items_symbol_check CHECK (char_length(symbol) >= 1),
    CONSTRAINT watchlist_items_data_source_check CHECK (data_source IN ('jquants', 'yahoo', 'ibkr')),
    CONSTRAINT watchlist_items_unique_symbol UNIQUE (watchlist_id, symbol)
);

-- Cached market data (separate from watchlist structure for efficiency)
CREATE TABLE watchlist_market_cache (
    symbol VARCHAR(20) PRIMARY KEY,
    price DECIMAL(15,4),
    change_amount DECIMAL(15,4),
    change_percent DECIMAL(8,4),
    market_cap BIGINT,
    pe_ratio DECIMAL(8,2),
    dividend_yield DECIMAL(5,2),
    currency VARCHAR(3) DEFAULT 'USD',
    data_source VARCHAR(20) NOT NULL DEFAULT 'yahoo',
    last_updated TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT, -- Store any fetch errors
    
    CONSTRAINT market_cache_data_source_check CHECK (data_source IN ('jquants', 'yahoo', 'ibkr')),
    CONSTRAINT market_cache_currency_check CHECK (currency IN ('USD', 'JPY', 'EUR', 'GBP', 'CAD', 'AUD'))
);

-- Indexes for performance
CREATE INDEX idx_user_watchlists_user_id ON user_watchlists(user_id);
CREATE INDEX idx_user_watchlists_created_at ON user_watchlists(created_at);

CREATE INDEX idx_watchlist_items_watchlist_id ON watchlist_items(watchlist_id);
CREATE INDEX idx_watchlist_items_symbol ON watchlist_items(symbol);
CREATE INDEX idx_watchlist_items_sort_order ON watchlist_items(watchlist_id, sort_order);

CREATE INDEX idx_market_cache_last_updated ON watchlist_market_cache(last_updated);
CREATE INDEX idx_market_cache_data_source ON watchlist_market_cache(data_source);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_watchlist_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for auto-updating timestamps
CREATE TRIGGER user_watchlists_updated_at_trigger
    BEFORE UPDATE ON user_watchlists
    FOR EACH ROW
    EXECUTE FUNCTION update_watchlist_updated_at();

-- Create default watchlists for existing users
INSERT INTO user_watchlists (user_id, name, description, is_default) VALUES
('default_user', 'Japanese Stocks', 'Japanese companies tracked via JQuants', true),
('default_user', 'Global Stocks', 'International stocks via Yahoo Finance & IBKR', false);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON user_watchlists TO omotesamba;
GRANT SELECT, INSERT, UPDATE, DELETE ON watchlist_items TO omotesamba;
GRANT SELECT, INSERT, UPDATE, DELETE ON watchlist_market_cache TO omotesamba;