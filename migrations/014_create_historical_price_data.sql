-- Create historical price data table for currency analysis and trend analysis
-- This table stores daily, weekly, and monthly historical price data from IBKR
-- Optimized for TimescaleDB time-series queries

-- Create TimescaleDB extension if not exists
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Historical price data table
CREATE TABLE IF NOT EXISTS company_historical_data (
    id BIGSERIAL,
    country VARCHAR(10) NOT NULL,
    code VARCHAR(20) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    
    -- Time series data
    date DATE NOT NULL,
    data_frequency VARCHAR(10) NOT NULL, -- 'daily', 'weekly', 'monthly'
    
    -- OHLCV data
    open_price DECIMAL(15,4) NOT NULL,
    high_price DECIMAL(15,4) NOT NULL,
    low_price DECIMAL(15,4) NOT NULL,
    close_price DECIMAL(15,4) NOT NULL,
    volume BIGINT DEFAULT 0,
    
    -- Additional market data
    currency VARCHAR(3) NOT NULL DEFAULT 'JPY',
    adjusted_close DECIMAL(15,4), -- Split/dividend adjusted
    
    -- Metadata
    data_source VARCHAR(20) NOT NULL DEFAULT 'ibkr',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Composite primary key for TimescaleDB
    PRIMARY KEY (id, date)
);

-- Create TimescaleDB hypertable partitioned by date
SELECT create_hypertable(
    'company_historical_data', 
    'date',
    chunk_time_interval => INTERVAL '1 month',
    if_not_exists => TRUE
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_historical_country_code_date 
    ON company_historical_data (country, code, date DESC);

CREATE INDEX IF NOT EXISTS idx_historical_symbol_frequency_date 
    ON company_historical_data (symbol, data_frequency, date DESC);

CREATE INDEX IF NOT EXISTS idx_historical_date_frequency 
    ON company_historical_data (date DESC, data_frequency);

CREATE INDEX IF NOT EXISTS idx_historical_code_frequency 
    ON company_historical_data (code, data_frequency, date DESC);

-- Create unique constraint to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_historical_unique_entry 
    ON company_historical_data (country, code, data_frequency, date);

-- Add foreign key constraint to companies table
ALTER TABLE company_historical_data 
ADD CONSTRAINT fk_historical_company 
FOREIGN KEY (country, code) 
REFERENCES companies(country, code) 
ON DELETE CASCADE;

-- Create continuous aggregates for performance

-- Weekly aggregates from daily data
CREATE MATERIALIZED VIEW IF NOT EXISTS company_weekly_aggregates
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('7 days', date) AS week_start,
    country,
    code,
    symbol,
    currency,
    FIRST(open_price, date) AS week_open,
    MAX(high_price) AS week_high,
    MIN(low_price) AS week_low,
    LAST(close_price, date) AS week_close,
    SUM(volume) AS week_volume,
    COUNT(*) AS trading_days
FROM company_historical_data
WHERE data_frequency = 'daily'
GROUP BY week_start, country, code, symbol, currency;

-- Monthly aggregates from daily data
CREATE MATERIALIZED VIEW IF NOT EXISTS company_monthly_aggregates
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 month', date) AS month_start,
    country,
    code,
    symbol,
    currency,
    FIRST(open_price, date) AS month_open,
    MAX(high_price) AS month_high,
    MIN(low_price) AS month_low,
    LAST(close_price, date) AS month_close,
    SUM(volume) AS month_volume,
    COUNT(*) AS trading_days,
    -- Calculate monthly returns
    (LAST(close_price, date) - FIRST(open_price, date)) / FIRST(open_price, date) * 100 AS monthly_return_pct
FROM company_historical_data
WHERE data_frequency = 'daily'
GROUP BY month_start, country, code, symbol, currency;

-- Create data retention policies
-- Keep daily data for 2 years, weekly for 5 years, monthly forever
SELECT add_retention_policy('company_historical_data', 
    INTERVAL '2 years',
    if_not_exists => TRUE);

-- Add compression policy for older data
SELECT add_compression_policy('company_historical_data', 
    INTERVAL '3 months',
    if_not_exists => TRUE);

-- Create helper view for currency analysis
CREATE OR REPLACE VIEW currency_analysis_data AS
SELECT 
    h.country,
    h.code,
    h.symbol,
    h.date,
    h.close_price,
    h.volume,
    h.currency,
    -- Price changes
    LAG(h.close_price, 1) OVER (PARTITION BY h.country, h.code ORDER BY h.date) AS prev_close,
    (h.close_price - LAG(h.close_price, 1) OVER (PARTITION BY h.country, h.code ORDER BY h.date)) / 
        LAG(h.close_price, 1) OVER (PARTITION BY h.country, h.code ORDER BY h.date) * 100 AS daily_return_pct,
    -- Moving averages
    AVG(h.close_price) OVER (PARTITION BY h.country, h.code ORDER BY h.date ROWS BETWEEN 19 PRECEDING AND CURRENT ROW) AS ma_20d,
    AVG(h.close_price) OVER (PARTITION BY h.country, h.code ORDER BY h.date ROWS BETWEEN 49 PRECEDING AND CURRENT ROW) AS ma_50d,
    AVG(h.close_price) OVER (PARTITION BY h.country, h.code ORDER BY h.date ROWS BETWEEN 199 PRECEDING AND CURRENT ROW) AS ma_200d,
    -- Volatility (20-day rolling standard deviation)
    STDDEV(h.close_price) OVER (PARTITION BY h.country, h.code ORDER BY h.date ROWS BETWEEN 19 PRECEDING AND CURRENT ROW) AS volatility_20d,
    -- Company info
    c.company_name,
    c.sector_17_name,
    c.scale_category
FROM company_historical_data h
JOIN companies c ON h.country = c.country AND h.code = c.code
WHERE h.data_frequency = 'daily'
ORDER BY h.country, h.code, h.date DESC;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON company_historical_data TO postgres;
GRANT SELECT ON currency_analysis_data TO postgres;
GRANT SELECT ON company_weekly_aggregates TO postgres;
GRANT SELECT ON company_monthly_aggregates TO postgres;

-- Add comments
COMMENT ON TABLE company_historical_data IS 'Historical OHLCV price data for companies from IBKR - optimized for currency analysis';
COMMENT ON COLUMN company_historical_data.data_frequency IS 'Data frequency: daily (1Y), weekly (2Y), monthly (5Y)';
COMMENT ON VIEW currency_analysis_data IS 'Pre-calculated view with price changes, moving averages, and volatility for currency analysis';
