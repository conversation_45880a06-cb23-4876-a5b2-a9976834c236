#!/usr/bin/env python3
"""
ABOUTME: Basic info about the downloaded NVIDIA OpenReasoning-Nemotron-14B model
ABOUTME: Reads model config and files without requiring CUDA
"""
import os
import json

def show_nemotron_info():
    """Show basic info about the downloaded Nemotron model"""
    model_path = "./models/nemotron-14b"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        print("💡 Please run download_nemotron.py first")
        return
    
    print("📋 NVIDIA OpenReasoning-Nemotron-14B Model Information")
    print("="*60)
    print(f"📁 Model path: {os.path.abspath(model_path)}")
    
    # Read config.json
    config_path = os.path.join(model_path, "config.json")
    if os.path.exists(config_path):
        print("\n⚙️  Model Configuration:")
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"   Model type: {config.get('model_type', 'Unknown')}")
        print(f"   Architecture: {config.get('architectures', ['Unknown'])[0]}")
        print(f"   Vocab size: {config.get('vocab_size', 0):,}")
        print(f"   Hidden size: {config.get('hidden_size', 0):,}")
        print(f"   Number of layers: {config.get('num_hidden_layers', 0)}")
        print(f"   Number of attention heads: {config.get('num_attention_heads', 0)}")
        print(f"   Max position embeddings: {config.get('max_position_embeddings', 0):,}")
    
    # List model files
    print("\n📂 Model Files:")
    total_size = 0
    safetensors_files = []
    other_files = []
    
    for file in os.listdir(model_path):
        file_path = os.path.join(model_path, file)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            
            if file.endswith('.safetensors'):
                safetensors_files.append((file, size))
            else:
                other_files.append((file, size))
    
    print(f"\n🔹 SafeTensors files ({len(safetensors_files)}):")
    for file, size in sorted(safetensors_files):
        print(f"   {file}: {size / (1024**3):.2f} GB")
    
    print(f"\n🔹 Other files ({len(other_files)}):")
    for file, size in sorted(other_files):
        if size > 1024*1024:  # Only show files > 1MB
            print(f"   {file}: {size / (1024**2):.1f} MB")
    
    print(f"\n📊 Summary:")
    print(f"   Total model size: {total_size / (1024**3):.2f} GB")
    print(f"   Total files: {len(safetensors_files) + len(other_files)}")
    print(f"   Model ready for inference: ✅")
    
    # Check tokenizer files
    tokenizer_files = ['tokenizer.json', 'vocab.json', 'merges.txt']
    tokenizer_found = [f for f in tokenizer_files if os.path.exists(os.path.join(model_path, f))]
    print(f"   Tokenizer files: {len(tokenizer_found)}/{len(tokenizer_files)} found")
    
    print(f"\n🚀 Next Steps:")
    print(f"   1. Install CUDA dependencies: pip install torch --index-url https://download.pytorch.org/whl/cu121")
    print(f"   2. Run with GPU: python test_nemotron.py")
    print(f"   3. Model requires ~28GB VRAM (RTX 3090 has 24GB - may need CPU offloading)")

if __name__ == "__main__":
    show_nemotron_info()